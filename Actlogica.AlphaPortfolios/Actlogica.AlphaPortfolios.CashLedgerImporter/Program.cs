﻿// See https://aka.ms/new-console-template for more information
using Actlogica.AlphaPortfolios.Data.Configs;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.ServiceIntegration.Configs;
using Actlogica.AlphaPortfolios.ServiceIntegration.Imports.LegacyData;
using AutoMapper;

Console.WriteLine("Hello!");
Console.WriteLine("Please paste the path of the file for Capital Register Transaction import including the file name!");
Console.WriteLine("Note: Only XLSX files are accepted!");

var filePath = Console.ReadLine();
var mapper = new MapperConfiguration((Action<IMapperConfigurationExpression>)(mc =>
{
	mc.AddProfile((Profile)new DataMappingProfiles());
	mc.AddProfile((Profile)new ServiceMappingProfiles());
})).CreateMapper();
var dbContext = new AlphaPortfolioDbContext("Server=localhost\\SQLEXPRESS;Database=alphaportfolios-local-db;Trusted_Connection=True;MultipleActiveResultSets=true");

var clientRepo = new ClientRepository(dbContext);
var strategyRepo = new StrategyRepository(dbContext);
var portfolioCashLedgerRepo = new PortfolioCashLedgerRepository(dbContext);
var strategyModelRepo = new StrategyModelRepository(dbContext);
var portfolioRepo = new PortfolioRepository(dbContext);
//var incomeExpenseImporter = new IncomeAndExpensesImporter(clientRepo, strategyRepo, portfolioCashLedgerRepo, strategyModelRepo, portfolioRepo);
//var mappedLedgerTxns = await incomeExpenseImporter.LoadDataFromFile(filePath);
//await incomeExpenseImporter.ImportTransactions(mappedLedgerTxns);
