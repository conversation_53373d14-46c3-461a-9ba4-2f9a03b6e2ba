﻿// See https://aka.ms/new-console-template for more information
using Actlogica.AlphaPortfolios.Data.Configs;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Configs;
using Actlogica.AlphaPortfolios.ServiceIntegration.Imports.LegacyData;
using AutoMapper;

var mapper = new MapperConfiguration((Action<IMapperConfigurationExpression>)(mc =>
{
	mc.AddProfile((Profile)new DataMappingProfiles());
	mc.AddProfile((Profile)new ServiceMappingProfiles());
})).CreateMapper();
var masterDataDbConnStr = "Data Source=masterdatadbsvr-si.database.windows.net;Initial Catalog=marketdata-db-uat;Persist Security Info=False;User ID=productdbadmin;Password=***************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
var localDbConnectionString = "Server=localhost\\SQLEXPRESS;Database=alphaportfolios-local-db;Trusted_Connection=True;MultipleActiveResultSets=true";
var uatDbConnectionString = "Server=tcp:finflo-finplan.database.windows.net,1433;Initial Catalog=alphaportfolio-db-uat;Persist Security Info=False;User ID=finplan-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
var qaDbConnectionString = "Server=tcp:finflo-finplan.database.windows.net,1433;Initial Catalog=alphaportfolio-db-qa;Persist Security Info=False;User ID=finplan-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
var commonDataConnStr = "DefaultEndpointsProtocol=https;AccountName=finflocommondata;AccountKey=****************************************************************************************;";

Console.WriteLine("Hello! This console allows you to import CLIENT, BANKS, CUSTODIANS, STRATEGIES master data in bulk across multiple clients.");
Console.WriteLine("Start by selecting an environment. Please enter the numeric value for selecting an environement for import:");
Console.WriteLine("1: Local DB\n2: UAT DB\n3: QA DB");
var environment = Console.ReadLine();
var dbConnectionString = string.Empty;

if (environment == "1")
{
  dbConnectionString = localDbConnectionString;
  Console.WriteLine("Selectd environment: LOCAL");
}
else if (environment == "2")
{
  dbConnectionString = uatDbConnectionString;
  Console.WriteLine("Selectd environment: UAT");
}
else if (environment == "3")
{
  dbConnectionString = qaDbConnectionString;
  Console.WriteLine("Selectd environment: QA");
}
else
{
  Console.WriteLine("Invalid environment value entered. Console aborting. Please try again.");
  return;
}
var dbContext = new AlphaPortfolioDbContext(dbConnectionString);

var clientRepo = new ClientRepository(dbContext);
var custodianRepo = new CustodianRepository(dbContext);

using(var bulkInserTransaction = await dbContext.Database.BeginTransactionAsync())
{
	try
	{
		Console.WriteLine("Please paste the path of the folder to import ClientMaster data from");
		Console.WriteLine("Note: Only XLSX files are accepted!");
		var rootDirectory = Console.ReadLine();
		if (string.IsNullOrEmpty(rootDirectory))
			return;

		Console.WriteLine("Reading files from directory.");
		var filesInDirectory = Directory.GetFiles(rootDirectory);
		Console.WriteLine($"{filesInDirectory.Count()} files found.");
		filesInDirectory.ToList().ForEach(file => Console.WriteLine($"{file}"));
		Console.WriteLine("Please type \"Yes\" and hit enter key to proceed.");
		var proceedYesOrNo = Console.ReadLine();

		if (string.IsNullOrWhiteSpace(proceedYesOrNo) || proceedYesOrNo.ToLower() != "yes")
		{
			Console.WriteLine("Invalid input. Process aboring...");
			Console.WriteLine("Enter any key to shut down...");
			Console.ReadLine();
			return;
		}

		Console.WriteLine("===========================================================================================================================");
		Console.WriteLine("Beginning client file import.");


		var clientMasterFile = filesInDirectory.Where(file => file.Contains("ClientsMaster")).FirstOrDefault();
		if (clientMasterFile == null)
		{
			Console.WriteLine("Client Master input template not found in directory. Process aborting...");
			Console.WriteLine("Enter any key to shut down...");
			Console.ReadLine();
			return;
		}

		var clientMasterImporter = new ClientMasterDataImporter(clientRepo, custodianRepo);
		var clientMasterData = await clientMasterImporter.LoadClientDetailsDataFromFile(clientMasterFile);
		var clientCustodianData = await clientMasterImporter.LoadClientCustodianDataFromFile(clientMasterFile);

		await clientMasterImporter.ImportClientMaster(clientMasterData, clientCustodianData);

		await bulkInserTransaction.CommitAsync();
		Console.WriteLine($"Import completed successfully. You may now close the console.");
		Console.WriteLine("===========================================================================================================================");
	}
	catch (Exception ex)
	{
		await bulkInserTransaction.RollbackAsync();
	}
}

//var capitalRegisterImporter = new CapitalRegisterImporter(clientRepo, strategyRepo, portfolioCapRegRepo, portfolioCashLedgerRepo, strategyModelRepo, portfolioRepo);
//var mappedCapRegTxns = await capitalRegisterImporter.LoadDataFromFile(filePath);
//await capitalRegisterImporter.ImportTransactions(mappedCapRegTxns);
