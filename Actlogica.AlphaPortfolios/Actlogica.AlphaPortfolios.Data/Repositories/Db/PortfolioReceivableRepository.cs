﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.ReceivablePayables;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Dapper;
using DapperExtensions;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;

namespace Actlogica.AlphaPortfolios.Data.Repositories.Db
{
	public class PortfolioReceivableRepository : DbRepositoryBase<PortfolioReceivable>, IPortfolioReceivableRepository
	{
		private readonly AlphaPortfolioDbContext _db;
		public PortfolioReceivableRepository(AlphaPortfolioDbContext db) : base(db)
		{
			_db = db;
		}

		public async Task<IEnumerable<PortfolioReceivable>> GetByPortfolioIdForDateRange(string portfolioId, DateTime fromDate, DateTime toDate)
		{
			return await _db.PortfolioReceivables.Where(pr => pr.PortfolioId == portfolioId && pr.TransactionDate >= fromDate && pr.TransactionDate <= toDate).ToListAsync();
		}

		public async Task<IEnumerable<PortfolioReceivable>> GetByPortfolioId(string portfolioId)
		{
			return await _db.PortfolioReceivables.Where(pr => pr.PortfolioId == portfolioId).ToListAsync();
		}

		public async Task<IEnumerable<PortfolioReceivable>> GetAllDetailedReceivables()
		{
			var query = $@"
							SELECT 
								pr.TransactionSubType,
								p.ClientStrategyCode, 
								p.Name, 
								pr.TransactionDate, 
								pr.SettlementDate,
								pr.Price, 
								pr.Quantity,
								pr.Amount,
								pr.ReceivableStatus 
							FROM 
								portfolioreceivables pr 
							JOIN 
								portfolios p on p.Id = pr.PortfolioId 
							JOIN 
								Clients c on c.Id = pr.ClientId  
						";

			using var sqlConn = new SqlConnection(_db.Database.GetConnectionString());
			return (await sqlConn.QueryAsync<PortfolioReceivable>(query, commandTimeout: 300)).ToList();
		}
		public async Task<IEnumerable<ReceivablePayablePayload>> GetByType(string type)
		{
			var query = $@"
							SELECT 
								pr.Id,
								pr.TransactionSubType,
                                c.FirstName + ' ' + c.LastName as ClientName,
								p.ClientStrategyCode, 
								p.Name as PortfolioName, 
								pr.TransactionDate, 
								pr.SettlementDate,
								pr.Price as Rate, 
								pr.Quantity,
								pr.Amount,
								pr.ReceivableStatus as Status,
								pr.Remarks,
								pr.CreatedDate
							FROM 
								portfolioreceivables pr 
							JOIN 
								portfolios p on p.Id = pr.PortfolioId 
							JOIN 
								Clients c on c.Id = pr.ClientId    
							Where
								pr.TransactionType = '{type}'
						";

			using var sqlConn = new SqlConnection(_db.Database.GetConnectionString());
			return (await sqlConn.QueryAsync<ReceivablePayablePayload>(query, commandTimeout: 300)).ToList();
		}
		public async Task<ReceivablePayablePayload> GetById(string id)
		{
			var query = $@"
							SELECT 
								pr.Id,
								pr.TransactionSubType,
                                c.FirstName + ' ' + c.LastName as ClientName,
								p.ClientStrategyCode, 
								p.Name as PortfolioName, 
								pr.TransactionDate, 
								pr.SettlementDate,
								pr.Price as Rate, 
								pr.Quantity,
								pr.Amount,
								pr.ReceivableStatus as Status,
								pr.Remarks,
								pr.CreatedDate,
								pr.InvestmentName
							FROM 
								portfolioreceivables pr 
							JOIN 
								portfolios p on p.Id = pr.PortfolioId 
							JOIN 
								Clients c on c.Id = pr.ClientId    
							Where
								pr.Id = '{id}'
						";

			using var sqlConn = new SqlConnection(_db.Database.GetConnectionString());
			return (await sqlConn.QueryAsync<ReceivablePayablePayload>(query, commandTimeout: 300)).FirstOrDefault();
		}
	}
}
