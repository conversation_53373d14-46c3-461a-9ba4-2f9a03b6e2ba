﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DeploymentTrackerLog : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string SellDeploymentTrackerId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BuyDeploymentTrackerId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string OrderStatus { get; set; }
	}
}
