﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Microsoft.VisualBasic;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientPersonalDetail : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }

		[StringLength(20)]
		public MaritalStatus? MaritalStatus { get; set; }

		[StringLength(50)]
		public ClientOccupation? Occupation { get; set; }

		[StringLength(20)]
		public Nationality? Nationality { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string FamilyOrGroup { get; set; }

		public bool? HeadOfFamily { get; set; }

		[Required]
		public bool KYCValid { get; set; }

		[Required]
		[StringLength(20)]
		public ClientTaxStatus TaxStatus { get; set; }

		[Required]
		[StringLength(50)]
		public ClientCategory Category { get; set; }

		[StringLength(50)]
		public Currency? ReportingCurreny { get; set; }

		public DateTime? AnniversaryDate { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string Spouse { get; set; }
		public DateTime? SpouseDOB { get; set; }

		[StringLength(50)]
		public ClientQualification? Qualification { get; set; }

		[StringLength(50)]
		public ClientWorkExperience? WorkExpererience { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string OrganizationName { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string EmployerID { get; set; }

		[StringLength(50)]
		public IndustryType? IndustryType { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string AddressOrganisation { get; set; }

		public string PlaceOfBirth { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string CountryOfBirth { get; set; }

		[StringLength(50)]
		public ClientGrossAnnualIncome? GrossAnnualIncome { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string SourceOfWealth { get; set; }

		[StringLength(50)]
		public ClientEstimatedFinancialWealth? EstimatedFinancialWealth { get; set; }

		[Required]
		public bool PoliticalExposure { get; set; }

		public bool? Amlcertified { get; set; }

		[Required]
		[StringLength(50)]
		public Gender Gender { get; set; }

		public string SubCategory { get; set; }


		[StringLength(200), Column(TypeName = "varchar")]
		public string ResidentialStatus { get; set; }
		public bool IsPioOciCardHolder { get; set; }

		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }

	}
}
