﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class CustomOrderEntry : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Industry { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		public bool IsBuy { get; set; }
		public bool IsSell { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		public double CurrentHolding { get; set; }
		public double BuyQuantity { get; set; }
		public double BuyPrice { get; set; }
		public double SellQuantity { get; set; }
		public double SellPrice { get; set; }
		public double Weight { get; set; }
		public double NewWeight { get; set; }
		public double TradeAmount { get; set; }
		public double CurrentPrice { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string CustomOrderRequestId { get; set; }
		public virtual CustomOrderRequest CustomOrderRequest { get; set; }
	}
}
