﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DirectEquityInvestment : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public Portfolio Portfolio { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ModelportfolioId { get; set; }
		public ModelPortfolio ModelPortfolio { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public Client Client { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		public double CurrentPrice { get; set; }
		public DateTime CurrentPriceDate { get; set; }
		public double CurrentHolding { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double TotalRealisations { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double Dividends { get; set; }
		public double IrrSinceInception { get; set; }
		public double IrrCurrent { get; set; }
		public DateTime FirstTransactionDate { get; set; }
		public DateTime LastTransactionDate { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MarketCap { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Sector { get; set; }

		public virtual IEnumerable<DirectEquityTransaction> Transactions { get; set; }
	}
}
