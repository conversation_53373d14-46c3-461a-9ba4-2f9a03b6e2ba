﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class BseSettlementCalendar : BaseTableEntity
	{
		
		[StringLength(10), Column(TypeName = "varchar")]
		public string SettlementCalendar { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Year { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Month { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string SettlementNo { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string SettlementNoForDep { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string TradingDate { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string EntrySixASevenADataByMembers { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ConfOfSixASevenAByCustodians { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string PayInPayOut { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string AuctionSettNo { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string SubmissionOfAuctionOffersOn { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string AuctionPayInPayOut { get; set; }
  }
}
