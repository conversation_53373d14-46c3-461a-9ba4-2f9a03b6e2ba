﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMasterRegion : BaseTableEntity
	{
		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }

	}
}
