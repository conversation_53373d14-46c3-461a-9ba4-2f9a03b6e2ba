﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Collections;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class SecurityMaster : BaseTableEntity
	{
		[Required]
		[StringLength(500), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ShortName { get; set; }
		public int IndustryCode { get; set; }
		public int SectorCode { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BseSymbol { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BseScripGroup { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string NseSymbol { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string NseSeries { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string Status { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string Sublisting { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string AssetClass { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string SecurityType { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string SecuritySubType { get; set; }
		public bool IsListedSecurity { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string MarketCap { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string Sector { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string Industry { get; set; }

		public virtual ICollection<SecurityPrice> Prices { get; set; }
	}
}
