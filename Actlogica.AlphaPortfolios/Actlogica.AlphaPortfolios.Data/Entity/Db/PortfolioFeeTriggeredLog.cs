﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioFeeTriggeredLog : BaseTableEntity
	{
		public DateTime TriggeredDate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string TriggerBy { get; set; }

		public int NoOfPortfolios { get; set; }

		public DateTime AsAtDate { get; set; }

		public double TotalCapital { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string Purpose { get; set; }
		[StringLength(1000), Column(TypeName = "varchar")]
		public string Remarks { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }

		public string PortfolioIdList { get; set; }

	}
}
