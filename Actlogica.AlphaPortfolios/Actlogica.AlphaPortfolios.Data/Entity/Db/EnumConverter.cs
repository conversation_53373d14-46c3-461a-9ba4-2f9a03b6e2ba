using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System;
using System.Runtime.Serialization;
using System.Linq;
using System.Reflection;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class EnumMemberConverter<TEnum> : ValueConverter<TEnum, string> where TEnum : Enum
    {
        public EnumMemberConverter() : base(
            v => GetEnumMemberValue(v),
            v => GetEnumFromMemberValue(v))
        {
            
        }

        private static string GetEnumMemberValue(TEnum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attribute = (EnumMemberAttribute)field.GetCustomAttribute(typeof(EnumMemberAttribute));
            return attribute == null ? value.ToString() : attribute.Value;
        }

        private static TEnum GetEnumFromMemberValue(string value)
        {
            var type = typeof(TEnum);
            foreach (var field in type.GetFields())
            {
                var attribute = (EnumMemberAttribute)field.GetCustomAttribute(typeof(EnumMemberAttribute));
                if (attribute != null && attribute.Value == value)
                {
                    return (TEnum)field.GetValue(null);
                }
            }
            throw new ArgumentException($"No enum value with EnumMember value '{value}' found in {type.Name}.");
        }
    }

}