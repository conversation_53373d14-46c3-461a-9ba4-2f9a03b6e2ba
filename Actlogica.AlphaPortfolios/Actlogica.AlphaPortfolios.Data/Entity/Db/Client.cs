﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class Client : BaseTableEntity
	{
		[Required]
		[StringLength(20), Column(TypeName = "varchar")]
		public string ClientCode { get; set; }

		[Required]
		[StringLength(200), Column(TypeName = "varchar")]
		public string FirstName { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string MiddleName { get; set; }

		[Required]
		[StringLength(200), Column(TypeName = "varchar")]
		public string LastName { get; set; }

		public DateTime DateOfBirth { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string Pan { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string A<PERSON>har { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		[EmailAddress(ErrorMessage = "Invalid Email Address")]
		public string Email { get; set; }

		[Required]
		[StringLength(20), Column(TypeName = "varchar")]
		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
		public string Phone { get; set; }

		[Required]
		[StringLength(20)]
		public ClientType ClientType { get; set; }

		[Required]
		[StringLength(20)]
		public DomicileType Domicile { get; set; }

		[StringLength(20), Column(TypeName = "varchar")]
		public string BseStarUcc { get; set; }

		[Required]
		[StringLength(20)]
		public ClientTitle Title { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string UserName { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string UserId { get; set; }

		public virtual ICollection<ClientBank> BankAccounts { get; set; }

		public virtual ClientPersonalDetail ClientPersonalDetail { get; set; }

		public virtual ClientContactDetail ClientContactDetail { get; set; }

		public int PctTotalEquityConsent { get; set; }

		public int PctTotalDebtConsent { get; set; }

		public int PctTotalLimitConsent { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string CKYCNo {  get; set; }


		[StringLength(400), Column(TypeName = "varchar")]
		public string FullName { get; set; }

		[StringLength(400), Column(TypeName = "varchar")]
		public string NameAsPerPan { get; set; }

		public bool IsAccreditedInvestor { get; set; }
		public virtual ClientFamilyDetail ClientFamilyDetail { get; set; }
		public virtual ICollection<ClientIdentity> ClientIdentities { get; set; }
		public virtual ClientDueDiligence ClientDueDiligence { get; set; }
		public virtual ClientAccreditedInvestorDetail ClientAccreditedInvestorDetail { get; set; }
		public virtual ClientOverseasDetail ClientOverseasDetail { get; set; }
		public virtual ClientIncorporationDetail ClientIncorporationDetail { get; set; }
		public virtual PortfolioHolder PortfolioHolder { get; set; }
	}
}
