﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class Broker : BaseTableEntity
	{
		[StringLength(150), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string AddressLine1 { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string AddressLine2 { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string City { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string State { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Postcode { get; set; }
		[StringLength(15), Column(TypeName = "varchar")]
		public string Phone { get; set; }
		[StringLength(15), Column(TypeName = "varchar")]
		public string Mobile { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string SupportEmail { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string OrderEmail { get; set; }
		public double AgreedBrokerage { get; set; }
		public virtual IEnumerable<StrategyBroker> StrategyBrokers { get; set; }
		public virtual List<ClientBroker> ClientBrokers { get; set; }

	}
}
