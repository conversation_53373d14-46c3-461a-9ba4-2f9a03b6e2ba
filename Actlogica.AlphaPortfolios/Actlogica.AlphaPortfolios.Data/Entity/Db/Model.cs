﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class Model : BaseTableEntity
	{
		[StringLength(250), Column(TypeName = "varchar")]
		public string Name { get; set; }
		public bool IsOpen { get; set; }
		public bool IsDiscretionary { get; set; }
		public bool IsNonDiscretionary { get; set; }
		public bool IsAdvisory { get; set; }
		public string ModelCode { get; set; }
		public string StrategyId { get; set; }
		public Strategy Strategy { get; set; }
		public virtual ICollection<ModelSecurity> SecuritiesInModel { get; set; }
	}
}
