using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class FAInvestmentMapping : BaseTableEntity
    {
        [Required]
        [<PERSON><PERSON><PERSON><PERSON>(2), <PERSON><PERSON><PERSON><PERSON>(50), <PERSON><PERSON><PERSON>(TypeName = "varchar")]
        public string FAName { get; set; }

        [Required]
        [StringLength(50), <PERSON>umn(TypeName = "varchar")]
        public string InvestmentId { get; set; }

        [ForeignKey("InvestmentId")]
        public Investment Investment { get; set; }

    }

}