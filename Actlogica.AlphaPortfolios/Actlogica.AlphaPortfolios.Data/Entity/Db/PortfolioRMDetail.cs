﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioRMDetail : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
 
		[StringLength(200), Column(TypeName = "varchar")]
		public string RMName { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string UserID { get; set; }
		public DateTime? FromDate { get; set; }

		public DateTime? ToDate { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string RMType { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string Branch { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string BusinessUnit { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string Introducer { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string Consultant { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string RMHierarchy { get; set; }    //(It will have Primary / Secondary)


		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorName { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorRMName { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorRMHierarchy { get; set; }

		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }


	}
}
