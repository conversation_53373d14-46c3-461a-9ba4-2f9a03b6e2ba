﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class TradeBuyIdea : BaseTableEntity
  {
    [StringLength(500), Column(TypeName = "varchar")]
    public string StrategyModelName { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string StrategyId { get; set; }
    public virtual Strategy Strategy { get; set; }
    [StringLength(250), Column(TypeName = "varchar")]
    public string SecurityName { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Symbol { get; set; }
    public double Change { get; set; }
    public double BuyRangePrice { get; set; }
    public double Till { get; set; }
    public int ExitLevel { get; set; }
    public int StoplossLevel { get; set; }
    [Column(TypeName = "varchar(MAX)")]
    public string Description { get; set; }

    [StringLength(500), Column(TypeName = "varchar")]
    public string FilePath { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ModelId { get; set; }
    public Model Model { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Isin { get; set; }
    [StringLength(10), Column(TypeName = "varchar")]
    public string Exchange { get; set; }
    public int Status { get; set; }
  }
}
