using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class ReportsCronConfig : BaseTableEntity
    {
        public DateTime RequestDate { get; set; }

        [Required]
        [StringLength(50), Column(TypeName = "varchar")]
        public string RequestedBy { get; set; }

        [StringLength(100), Column(TypeName = "varchar")]
        public ReportType ReportType { get; set; }

        [StringLength(100), Column(TypeName = "varchar")]
        public ReportFrequency Frequency { get; set; }

        [StringLength(20), Column(TypeName = "varchar")]
        public ReportsFormat ReportFormat { get; set; }

        public string PortfolioId { get; set; }
        public virtual Portfolio Portfolio { get; set; }

        [Required]
        [StringLength(50),]
        public string ClientId { get; set; }
        public virtual Client Client { get; set; }

        public DateTime Time { get; set; }

        [StringLength(30), Column(TypeName = "varchar")]
        public ScheduledReportStatus Status { get; set; } = ScheduledReportStatus.Active;
    }
}
