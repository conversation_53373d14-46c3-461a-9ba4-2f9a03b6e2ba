﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class TradeOrder : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string SourceOrderSettlementFileEntryId { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string PlacedBy { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string LastSettledBy { get; set; }
		public int OrderNo { get; set; }
		[StringLength(75), Column(TypeName = "varchar")]
		public string StrategyCode { get; set; }
		[StringLength(75), Column(TypeName = "varchar")]
		public string StrategyId { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Identifier { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Series { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Type { get; set; }
		public double Price { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string BuySell { get; set; }
		public double OrderQuantity { get; set; }
		public double OrderAmount { get; set; }
		public double LastSettledQuantity { get; set; }
		public double PendingOrderQuantity { get; set; }
		public double PendingOrderPercentage { get; set; }
		public DateTime OrderDate { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string CustodianDpCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TradingAccountNo { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string SettlementStatus { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string CustodianId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BrokerId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MfFolioNumber { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MfBuySellType { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string MfAllUnitsRedemptionFlag { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MfClientBank { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MfOmsClientCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string EUINNumber { get; set; }

		[StringLength(1000), Column(TypeName = "varchar")]
		public string SecurityName { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		public virtual Custodian Custodian { get; set; }
		public virtual Broker Broker { get; set; }
	}
}
