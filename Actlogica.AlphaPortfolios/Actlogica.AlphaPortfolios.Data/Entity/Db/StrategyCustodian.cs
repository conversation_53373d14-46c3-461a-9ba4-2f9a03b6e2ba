﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class StrategyCustodian : BaseTableEntity
    {
        [StringLength(100), Column(TypeName = "varchar")]
        public string DpID { get; set; }
        public string CustodianId { get; set; }
        public string StrategyId { get; set; }
    }
}