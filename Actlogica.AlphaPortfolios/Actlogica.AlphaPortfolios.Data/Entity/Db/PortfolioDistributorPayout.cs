﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioDistributorPayout : BaseTableEntity
	{

		[StringLength(200), Column(TypeName = "varchar")]
		public string PortfolioDistributorManagerSharingConfigurationId {get; set;}
		public double FixedFeeAmount {get; set;}
		public double PerformanceFeeAmount {get; set;}
		public double UpFrontFeeAmount {get; set;}
		public double ExitLoadAmount {get; set;}
		public double GrossPayoutAmount {get; set;}
		public double DiscountRate {get; set;}
		public double DiscountAmount {get; set;}
		public double TDS {get; set;}
		public double NetAmount {get; set;} //(GrossPayoutAMount - DiscountAmount - TDS)
		
		public DateTime FromDate {get; set;}
		public DateTime? ToDate {get; set;}
		[StringLength(50), Column(TypeName = "varchar")] 
		public string Status {get; set;} //(Prepared/Approved/Processing/Paid/Cancelled/Rejected)
		public DateTime? PaidDate {get; set;}
		[StringLength(50), Column(TypeName = "varchar")]
		public string RunBy { get; set;} //(PMS UserId)
		[StringLength(50), Column(TypeName = "varchar")]
		public string ApprovedBy { get; set;} //(PMS UserId)
		public string Remarks{ get; set;}

	}
}
