﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioDistributorMapping : BaseTableEntity
	{
		[StringLength(100), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }
		public DateTime FromDate { get; set; }
		public DateTime? ToDate { get; set; }
	}
}
