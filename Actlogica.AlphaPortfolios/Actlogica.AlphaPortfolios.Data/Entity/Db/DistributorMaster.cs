﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMaster : BaseTableEntity
	{
		[StringLength(150), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Type { get; set; } // (Distributor/ RIA)
		
		[StringLength(100), Column(TypeName = "varchar")]
		public string TaxStatus { get; set; } // (Individual/Proprietorship/Partnership Firm/Provate Limited/Public Limited/Society Trust / Others)
		[StringLength(200), Column(TypeName = "varchar")]
		public string Email { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Mobile { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Address1 { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Address2 { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string City { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string State { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PinCode { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Country { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Website { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string PayoutFrequency { get; set; } // (Monthly,Quaterly,Halfyearly,Yearly)

		[StringLength(50), Column(TypeName = "varchar")]

		public string PayoutBankAcNo { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]

		public string PayoutBankName { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]

		public string PayoutBankIFSCCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]

		public string PayoutBankBranch { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]

		public string PayoutBankAccountType { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string PayoutBankMIRC { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string CompanyPAN { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string EUIN { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BrokerCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string GSTNo { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public DateTime BrokerCodeValidateUpto { get; set; }

		public DateTime DMStartDate { get; set; }
		public DateTime? DMEndDate { get; set; }
		public string Remarks { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string UniqueDistributorCode { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }
		public DateTime AgreementDate { get; set; }
		public DateTime EffectiveFrom { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AgSignMode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AgStyle { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string SignedBy { get; set; }
		public DateTime SignedAt { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorCodeWS { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string BranchId { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string RegionId { get; set; }

	}
}
