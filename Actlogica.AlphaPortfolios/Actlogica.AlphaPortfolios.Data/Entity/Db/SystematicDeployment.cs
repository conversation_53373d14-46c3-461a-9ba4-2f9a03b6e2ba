﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class SystematicDeployment : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string SourcePortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string DestinationPortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Type { get; set; } //Fixed, Percentage
		[StringLength(50), Column(TypeName = "varchar")]
		public string Frequency { get; set; } //Weekly, Fortnightly, Monthly
		[StringLength(50), Column(TypeName = "varchar")]
		public string Status { get; set; } //Active, Completed
		public DateTime StartDate { get; set; }
		public DateTime EndDate { get; set; }
		public DateTime InstalmentDate { get; set; }
		public DateTime RedemptionDate { get; set; }
		public int NoOfInstallments { get; set; } //should be calculated at the time of saving the STP
		public int CompletedInstallments { get; set; }
		public int PendingInstallments { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string CreatedBy { get; set; } //the name of the user who created this
		public double InstallmentAmount { get; set; } //Entered by user when in "Fixed" mode or calculated by the system when in "Percentage" mode
		public double InstallmentPercentage { get; set; } //Entered by the user only when in "Percentage" mode
		public int SellTriggerPeriod { get; set; } //
		public bool IsActive { get; set; }
		public bool IsAutoSellTrigger { get; set; }
		public bool IsAutoBuyTrigger { get; set; }
		public bool HasSellLeg { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string LastBuyTrackerId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string LastSellTrackerId { get; set; }
		public virtual Client ForClient { get; set; }
		public virtual Portfolio SourcePortfolio { get; set; }
		public virtual Portfolio DestinationPortfolio { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string DeploymentType { get; set; } 
	}
}
