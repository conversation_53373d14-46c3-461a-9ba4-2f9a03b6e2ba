﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioHoldingUpdate : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string FAName { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string FilePath { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string UploadedBy { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }
		public DateTime UploadDate { get; set; }
		public int NoOfRecords { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string UploadType { get; set; }
	}
}
