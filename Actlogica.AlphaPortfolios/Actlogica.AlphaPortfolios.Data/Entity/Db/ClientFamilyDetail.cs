﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientFamilyDetail : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Relationtype { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string FatherSpouseTitle { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string FatherSpouseFirstName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string FatherSpouseMiddleName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string FatherSpouseLastName { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MotherTitle { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string MotherFirstName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string MotherMiddleName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string MotherLastName { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
	}
}
