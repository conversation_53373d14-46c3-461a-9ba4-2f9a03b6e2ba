﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class HoldingReconProcessResultData : BaseTableEntity
  {
    [StringLength(20), Column(TypeName = "varchar")]
    public string ClientCode { get; set; }
    [StringLength(200), Column(TypeName = "varchar")]
    public string ClientName { get; set; }
    [StringLength(20), Column(TypeName = "varchar")]
    public string Strategy { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string Model { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string Symbol { get; set; }
    [StringLength(200), Column(TypeName = "varchar")]

    public string SCRIP { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ISIN { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string PortfolioHolding { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ReportedCustFileHolding { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ReportedFAFileHolding { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string ProcessingStatus { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string HoldingReconProcessId { get; set; }
        

    }
}
