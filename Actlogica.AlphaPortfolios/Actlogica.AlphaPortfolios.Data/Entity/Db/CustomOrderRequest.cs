﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class CustomOrderRequest : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string CreatedBy { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Status { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }
	}
}
