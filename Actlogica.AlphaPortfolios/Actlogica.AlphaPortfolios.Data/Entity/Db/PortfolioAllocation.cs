﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioAllocation : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ModelPortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AllocationAnalysisType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Type { get; set; }
		public double Value { get; set; }
		public double Weight { get; set; }
		public DateTime AsAtDate { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string UniqueKey { get; set; }
	}
}
