﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMasterBranches : BaseTableEntity
	{
		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string BranchName { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Email { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Mobile { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Address1 { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Address2 { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string City { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string State { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PinCode { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Country { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string GSTNo { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }

	}
}
