﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ValuationMethodologyMapping : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string SecurityType { get; set; }
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string SecuritySubType { get; set; }
		[Required]
		public string IsListedSecurity { get; set; }
	}
}
