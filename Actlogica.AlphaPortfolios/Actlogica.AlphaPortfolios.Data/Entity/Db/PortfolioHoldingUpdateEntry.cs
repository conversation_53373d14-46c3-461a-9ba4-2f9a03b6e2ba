﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Microsoft.OData.Edm;
//2
namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioHoldingUpdateEntry : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string FAAccountNo { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string ClientName { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string AssetClass { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Identifier { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string SecurityName { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string Sector { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string MarketCap { get; set; }
		public double Quantity { get; set; }
		public double AveragePrice { get; set; }
		public double TotalCost { get; set; }
		public double CurrentPrice { get; set; }
		public double AccruedIncome { get; set; }
		public double MarketValue { get; set; }
		public double Weight { get; set; }
		public double GainLoss { get; set; }
		public double GainLossPct { get; set; }
		public double Receivable { get; set; }
		public double? Payable { get; set; }
		public DateTime HoldingDate { get; set; }
		public int NoOfRecords { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Status { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string InvestmentId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioHoldingUpdateId { get; set; }
		public virtual PortfolioHoldingUpdate PortfolioHoldingUpdate { get; set; }

		public string ModelCode { get; set; }

	}
}
