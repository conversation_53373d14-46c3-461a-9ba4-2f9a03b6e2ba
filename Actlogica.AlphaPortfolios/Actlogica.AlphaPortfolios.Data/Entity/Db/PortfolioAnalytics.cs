﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioAnalytics : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string InvestmentId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ModelPortfolioId { get; set; }
		public DateTime AsAtDate { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AggregateType{ get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MfRtaCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Series { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AmfiCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string InvestmentName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AmcName { get; set; }
		[StringLength(350), Column(TypeName = "varchar")]
		public string LogoImgUrl { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AssetClass { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AssetType { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string FundClass { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string CreditRating { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string IndexName { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string Industry { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MarketCap { get; set; }
		public double ClosePrice { get; set; }
		public DateTime ClosePriceDate { get; set; }
		public double ClosePriceChange { get; set; }
		public double AbsoluteReturnPercentage { get; set; }
		public double AbsoluteReturnValue { get; set; }
		public double AveragePurchasePrice { get; set; }
		public double AllUnits { get; set; }
		public double LongTermUnits { get; set; }
		public double ShortTermUnits { get; set; }
		public double BenchmarkReturn { get; set; }
		public double Cagr { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double Withdrawals { get; set; }
		public double MarketValue { get; set; }
		public double Weight { get; set; }
		public double MarketValueChange { get; set; }
		public double MarketValueChangePercentage { get; set; }
		public double DividendPaid { get; set; }
		public double DividendReinvested { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double Xirr { get; set; }
		public double Twrr { get; set; }
		public DateTime FirstDateOfInvestment { get; set; }
		public DateTime DateOfMaturity { get; set; }
		public double GrowthAt10000 { get; set; }
		public double GrowthAt10000Percentage { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string UniqueKey { get; set; }
		public double ComparePrice { get; set; }
		public DateTime ComparePriceDate { get; set; }
		public int NumberOfInvestments { get; set; }
	}
}
