﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class BuyTradeIdea : BaseTableEntity
    {
        [StringLength(250), Column(TypeName = "varchar")]
        public string SecurityName { get; set; }
        [StringLength(50), Column(TypeName = "varchar")]
        public string Symbol { get; set; }
        [StringLength(50), Column(TypeName = "varchar")]
        public string Isin { get; set; }
        [StringLength(10), Column(TypeName = "varchar")]
        public string Exchange { get; set; }
        [StringLength(20), Column(TypeName = "varchar")]
        public string SecurityType { get; set; }
        public double BuyPriceFrom { get; set; }
        public double BuyPriceTo { get; set; }
        public double ExitPrice { get; set; }
        public double StopLoss { get; set; }
        public double Change { get; set; }
        public double BuyNetChange { get; set; }
        [Column(TypeName = "varchar(MAX)")]
        public string Description { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string FilePath { get; set; }
        [StringLength(100), Column(TypeName = "varchar")]
        public string Status { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string CreatedBy { get; set; }

        public DateTime? CreatedTime { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string UpdatedBy { get; set; }

        public DateTime? UpdatedTime { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string SubmittedBy { get; set; }

        public DateTime? SubmittedTime { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string ApprovedBy { get; set; }

        public DateTime? ApprovedTime { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string ExecutedBy { get; set; }

        public DateTime? ExecutedTime { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string RejectedBy { get; set; }

        public DateTime? RejectedTime { get; set; }

        [StringLength(500), Column(TypeName = "varchar")]
        public string AbandonedBy { get; set; }

        public DateTime? AbandonedTime { get; set; }


        [StringLength(50), Column(TypeName = "varchar")]
        public string StrategyId { get; set; }
        public virtual Strategy Strategy { get; set; }
        [StringLength(50), Column(TypeName = "varchar")]
        public string ModelId { get; set; }
        public virtual Model Model { get; set; }
    }
}
