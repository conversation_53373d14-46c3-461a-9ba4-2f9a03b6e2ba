﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class TradeOrderSettlementFile : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string PlacedBy { get; set; }
		[StringLength(4000), Column(TypeName = "varchar")]
		public string FilePath { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AlphaTransformerIntegrationId { get; set; }
		[StringLength(4000), Column(TypeName = "varchar")]
		public string AlphaFilePath { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ProcessingStatus { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string FailureMessage { get; set; }
		[StringLength(4000), Column(TypeName = "varchar")]
		public string FailureDescription { get; set; }
	}
}
