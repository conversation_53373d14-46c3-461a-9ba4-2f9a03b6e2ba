﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;


namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class ClientOrderEntry : BaseTableEntity
  {
    [StringLength(100), Column(TypeName = "varchar")]
    public string Identifier { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string Isin { get; set; }
    [StringLength(10), Column(TypeName = "varchar")]
    public string Exchange { get; set; }
    [StringLength(1000), Column(TypeName = "varchar")]
    public string ScripName { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string InvestmentType { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string TransactionType { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string TransactionSubType { get; set; }
    public double Quantity { get; set; }
    public double PendingQuantity { get; set; }
    public double Price { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string OrderType { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime SettlementDate { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string StrategyCode { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string StrategyName { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string StrategyModelName { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string StrategyTradingAccountNumber { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string StrategyCustodyCode { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string StrategyModelId { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string OrderStatus { get; set; }
    public double TransactionAmount { get; set; }
    public double SettlementQuantity { get; set; }
    public double SettlementPrice { get; set; }
    public double SettlementMarketAmount { get; set; }
    public double SettlementBrokerageAmount { get; set; }
    public double SettlementServiceTax { get; set; }
    public double SettlementSttAmount { get; set; }
    public double SettlementNetRate { get; set; }
    public double SettlementTurnTax { get; set; }
    public double SettlementOtherTax { get; set; }
    public double SettlementNetAmount { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ClientCode { get; set; }
    public string SourceType { get; set; }

    [StringLength(50), Column(TypeName = "varchar")]
    public string SourceReference { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ClientId { get; set; }
    public virtual Client Client { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string MfFolioNumber { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string MfBuySellType { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string MfAllUnitsRedemptionFlag { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MfClientBank { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MfOmsClientCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string EUINNumber { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public DomicileType ClientDomicile { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientTradingAccount { get; set; }
		public string Remarks { get; set; }

		[StringLength(500), Column(TypeName = "varchar")]
		public string OrderRationale { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PeakMargin { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PeakMarginRef { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Currency { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Series { get; set; }
		public double CurrencyConversionRate { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string CreatedBy { get; set; }
	}
}
