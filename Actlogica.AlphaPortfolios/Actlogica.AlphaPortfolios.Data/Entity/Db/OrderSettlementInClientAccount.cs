﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class OrderSettlementInClientAccount : BaseTableEntity
	{
		[StringLength(150), Column(TypeName = "varchar")]
		public string SrNo { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ContractNumber { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string PartyCode { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ClientShortName { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string CustodyClearingCode { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ClientCustodyCode { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ClientFaCode { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ClientDematNumber { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ScripCode { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string FolioNo { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ScripName { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string Series { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string SettNo { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string SettType { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string SellBuy { get; set; }
		public DateTime TradeDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public double OriginalQuantityOrdered { get; set; }
		public double Quantity { get; set; }
		public double SettlementAllocationPercentage { get; set; }
		public double MarketRate { get; set; }
		public double MarketAmount { get; set; }
		public double ActualBrokerage { get; set; }
		public double ActualBrokeragePerUnit { get; set; }
		public double ExpectedBrokerage { get; set; }
		public double ExpectedBrokeragePerUnit { get; set; }
		public double ServiceTax { get; set; }
		public double NetRate { get; set; }
		public double SttAmount { get; set; }
		public double TurnTaxExchangeTxnTax { get; set; }
		public double StampDutyAndOtherCharges { get; set; }
		public double NetAmount { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ClientOrderEntryId { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string StrategyModelid { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string StrategyName { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string TradeOrderSettlementFileId { get; set; }
	}
}
