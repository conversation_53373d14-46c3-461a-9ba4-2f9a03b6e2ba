﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;
using System.Runtime.ConstrainedExecution;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMasterCertifications : BaseTableEntity
	{
		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string TeamMemberName { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string NISMCertification { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string NISMCertificationType { get; set; }

		public DateTime NISMCertValidfrom { get; set; }

		public DateTime NISMCertValidto { get; set; }

		public string Remarks { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string Attachment { get; set; }

	}
}
