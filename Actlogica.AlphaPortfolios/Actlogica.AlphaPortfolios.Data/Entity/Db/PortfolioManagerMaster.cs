﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioManagerMaster : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string PMName { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]

		public string PMPan { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string PMPOName { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]

		public string PMPOPan { get; set; }
		public DateTime PMPODateOfJoin { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string PMSebiRegno { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]

		public string PMCOPan { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string PMCOName { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string PONismCertNo { get; set; }
		public DateTime PONismCertDate { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string FIURegNo { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string KRARegNo { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string CERSAIRegno { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string ScoreRegNo { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string WebLink { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string FOSystemName { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string BPSystemName { get; set; }
		public DateTime PMSurrenderDate { get; set; }
		public double PMNetworth { get; set; }
		public DateTime PMNetworthDate { get; set; }

	}
}
