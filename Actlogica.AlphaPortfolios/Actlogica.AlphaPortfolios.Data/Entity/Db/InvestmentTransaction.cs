﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Microsoft.EntityFrameworkCore;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	[Index(nameof(ClientId), Name = $"NonUnique_{nameof(ClientId)}")]
	[Index(nameof(PortfolioId), Name = $"NonUnique_{nameof(PortfolioId)}")]
	[Index(nameof(InvestmentId), Name = $"NonUnique_{nameof(InvestmentId)}")]
	[Index(nameof(Isin), Name = $"NonUnique_{nameof(Isin)}")]
	[Index(nameof(Type), Name = $"NonUnique_{nameof(Type)}")]
	[Index(nameof(SubType), Name = $"NonUnique_{nameof(SubType)}")]
	public class InvestmentTransaction : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string InvestmentId { get; set; }
		public Investment Investment { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public Portfolio Portfolio { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public Client Client { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public DateTime CGTDate { get; set; }
		public double Quantity { get; set; }
		public double Price { get; set; }
		public double MarketRate { get; set; }
		public double Amount { get; set; }
		public double Brokerage { get; set; }
		public double ServiceTax { get; set; }
		public double SttAmount { get; set; }
		public double TurnTax { get; set; }
		public double OtherTax { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Type { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string SubType { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string MFFolio { get; set; }
		public double CurrentHolding { get; set; }
		public double UnrealisedHolding { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Currency { get; set; }
		public double CurrencyConversionRate { get; set; }
		public double AcquisitionRate { get; set; }
	}
}
