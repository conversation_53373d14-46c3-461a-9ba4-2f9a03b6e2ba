﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioHolder : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string HolderType { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
	}
}
