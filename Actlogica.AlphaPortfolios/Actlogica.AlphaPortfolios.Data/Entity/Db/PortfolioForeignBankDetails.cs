﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioForeignBankDetails : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }


		[StringLength(50), Column(TypeName = "varchar")]
		public string IntermittentBankAccount { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string NOSTRODetails { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string SenderCorrespondentBankDetails { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ReceiverCorrespondentBankDetails { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string SwiftCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Currency { get; set; }

		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }

	}
}
