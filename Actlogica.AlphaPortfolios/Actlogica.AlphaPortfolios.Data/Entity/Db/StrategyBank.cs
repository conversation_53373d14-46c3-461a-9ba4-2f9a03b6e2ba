﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class StrategyBank : BaseTableEntity
	{	
		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string Name { get; set; }

		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string AddressLine1 { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string AddressLine2 { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string City { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string State { get; set; }

		[Required]
		[StringLength(20), Column(TypeName = "varchar")]
		public string Postcode { get; set; }

		[Required]
		[StringLength(200), Column(TypeName = "varchar")]
		public string AccountName { get; set; }

		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string AccountNumber { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string Ifsc { get; set; }

		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string Micr { get; set; }

		[Required]
		[StringLength(150), Column(TypeName = "varchar")]
		public BankAccountType BankAccountType { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string BranchName { get; set; }

		[StringLength(50),Column(TypeName = "varchar")]
		public Currency? Currency { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string SwiftCode { get; set; }

		[Required]
		[StringLength(20), Column(TypeName = "varchar")]
		public AccountStatus AccountStatus { get; set; }

		public DateTime? FromDate { get; set; }
		public DateTime? ToDate { get; set; }
	}
}
