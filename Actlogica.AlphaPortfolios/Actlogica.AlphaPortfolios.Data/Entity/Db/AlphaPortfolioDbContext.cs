﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Fees;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.EntityFrameworkCore.SqlServer;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public partial class AlphaPortfolioDbContext : DbContext
  {
    private readonly string _connectionString;
    public AlphaPortfolioDbContext(DbContextOptions<AlphaPortfolioDbContext> options) : base(options)
    {

    }

    public AlphaPortfolioDbContext(string connectionString)
    {
      _connectionString = connectionString;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
      if (!string.IsNullOrEmpty(_connectionString))
        optionsBuilder.UseSqlServer(_connectionString);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
      modelBuilder.Entity<Client>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<Client>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<Client>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<DirectEquityInvestment>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<DirectEquityInvestment>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<DirectEquityInvestment>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<DirectEquityTransaction>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<DirectEquityTransaction>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<DirectEquityTransaction>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<Model>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<Model>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<Model>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ModelSecurity>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ModelSecurity>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ModelSecurity>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<Portfolio>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<Portfolio>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<Portfolio>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ModelPortfolio>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ModelPortfolio>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ModelPortfolio>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<Strategy>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<Strategy>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<Strategy>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ClientOrderEntry>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ClientOrderEntry>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ClientOrderEntry>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioCapitalRegister>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioCapitalRegister>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioCapitalRegister>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioCashLedger>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioCashLedger>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioCashLedger>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioCashLedger>().Property(p => p.TxnSequenceId).ValueGeneratedOnAdd()
        .Metadata.SetAfterSaveBehavior(Microsoft.EntityFrameworkCore.Metadata.PropertySaveBehavior.Throw);

      modelBuilder.Entity<PoolCapitalRegister>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PoolCapitalRegister>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PoolCapitalRegister>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PoolCashLedger>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PoolCashLedger>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PoolCashLedger>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<Broker>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<Broker>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<Broker>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<Custodian>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<Custodian>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<Custodian>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ClientBank>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ClientBank>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ClientBank>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<StrategyBank>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<StrategyBank>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<StrategyBank>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<StrategyBroker>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<StrategyBroker>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<StrategyBroker>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");


      modelBuilder.Entity<GeneralSetting>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<GeneralSetting>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<GeneralSetting>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<StrategyCustodian>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<StrategyCustodian>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<StrategyCustodian>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<RestrictedStocksForClient>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<RestrictedStocksForClient>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<RestrictedStocksForClient>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<RestrictedStockForOrganisation>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<RestrictedStockForOrganisation>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<RestrictedStockForOrganisation>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<TradeBuyIdea>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TradeBuyIdea>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TradeBuyIdea>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<TradeOrder>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TradeOrder>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TradeOrder>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<TradeOrderSettlementFile>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TradeOrderSettlementFile>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TradeOrderSettlementFile>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<TradeOrderSettlementFileEntry>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TradeOrderSettlementFileEntry>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TradeOrderSettlementFileEntry>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<TradeSettlementLog>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TradeSettlementLog>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TradeSettlementLog>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ClientCustodian>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ClientCustodian>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ClientCustodian>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<OrderSettlementInClientAccount>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<OrderSettlementInClientAccount>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<OrderSettlementInClientAccount>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<StrategyUser>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<StrategyUser>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<StrategyUser>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");


      modelBuilder.Entity<TradeSellIdea>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TradeSellIdea>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TradeSellIdea>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BuyTradeIdea>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BuyTradeIdea>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BuyTradeIdea>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<SellTradeIdea>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<SellTradeIdea>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<SellTradeIdea>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<MutualFundInvestment>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<MutualFundInvestment>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<MutualFundInvestment>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<MutualFundTransaction>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<MutualFundTransaction>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<MutualFundTransaction>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BondInvestment>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BondInvestment>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BondInvestment>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BondTransaction>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BondTransaction>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BondTransaction>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<CustomOrderRequest>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<CustomOrderRequest>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<CustomOrderRequest>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<CustomOrderEntry>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<CustomOrderEntry>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<CustomOrderEntry>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<TradeOrderUnsettledAmounts>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TradeOrderUnsettledAmounts>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TradeOrderUnsettledAmounts>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BseSettlementCalendar>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BseSettlementCalendar>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BseSettlementCalendar>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<Investment>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<Investment>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<Investment>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<InvestmentTransaction>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<InvestmentTransaction>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<InvestmentTransaction>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<FAInvestmentMapping>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<FAInvestmentMapping>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<FAInvestmentMapping>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<SecurityType>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<SecurityType>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<SecurityType>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<CustomOrderEntry>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<CustomOrderEntry>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<CustomOrderEntry>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BankReconProcess>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BankReconProcess>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BankReconProcess>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BankReconTransactionProcessInputData>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BankReconTransactionProcessInputData>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BankReconTransactionProcessInputData>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BankReconBalanceProcessInputData>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BankReconBalanceProcessInputData>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BankReconBalanceProcessInputData>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<BankReconProcessResultData>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<BankReconProcessResultData>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<BankReconProcessResultData>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<HoldingReconCustodianInputData>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<HoldingReconCustodianInputData>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<HoldingReconCustodianInputData>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<HoldingReconProcessResultData>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<HoldingReconProcessResultData>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<HoldingReconProcessResultData>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<HoldingReconInputFile>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<HoldingReconInputFile>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<HoldingReconInputFile>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<HoldingReconProcess>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<HoldingReconProcess>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<HoldingReconProcess>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<FinFloClientSyncConfiguration>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<FinFloClientSyncConfiguration>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<FinFloClientSyncConfiguration>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ReportRequest>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ReportRequest>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ReportRequest>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<HistoricalModelPortfolioHolding>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<HistoricalModelPortfolioHolding>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<HistoricalModelPortfolioHolding>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<HistoricalModelPortfolioRecord>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<HistoricalModelPortfolioRecord>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<HistoricalModelPortfolioRecord>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioAnalytics>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioAnalytics>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioAnalytics>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioAllocation>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioAllocation>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioAllocation>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioAumDaily>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioAumDaily>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioAumDaily>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ClientBroker>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ClientBroker>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ClientBroker>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioReceivable>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioReceivable>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioReceivable>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioHoldingUpdate>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioHoldingUpdate>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioHoldingUpdate>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioHoldingUpdateEntry>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioHoldingUpdateEntry>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioHoldingUpdateEntry>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<SystematicDeployment>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<SystematicDeployment>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<SystematicDeployment>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<DeploymentTracker>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<DeploymentTracker>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<DeploymentTracker>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<DeploymentTrackerLog>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<DeploymentTrackerLog>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<DeploymentTrackerLog>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<FeeTemplate>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<FeeTemplate>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<FeeTemplate>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<StrategyModelFeeTemplate>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<StrategyModelFeeTemplate>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<StrategyModelFeeTemplate>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioFeeTemplate>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioFeeTemplate>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioFeeTemplate>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioFee>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioFee>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioFee>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioFeeTriggeredLog>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioFeeTriggeredLog>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioFeeTriggeredLog>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ClientPersonalDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ClientPersonalDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ClientPersonalDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ClientContactDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ClientContactDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ClientContactDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioRMDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioRMDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioRMDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioPreference>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioPreference>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioPreference>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioNomineeDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioNomineeDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioNomineeDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PortfolioTransactionsPrefernce>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PortfolioTransactionsPrefernce>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PortfolioTransactionsPrefernce>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<AlphaTransformerIntegration>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<AlphaTransformerIntegration>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<AlphaTransformerIntegration>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<TransfomerFileRequest>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<TransfomerFileRequest>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<TransfomerFileRequest>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<PeakMarginLog>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<PeakMarginLog>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<PeakMarginLog>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<ReportsCronConfig>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ReportsCronConfig>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ReportsCronConfig>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
      modelBuilder.Entity<SecurityMaster>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<SecurityMaster>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<SecurityMaster>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
      modelBuilder.Entity<SecurityPrice>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<SecurityPrice>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<SecurityPrice>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
      modelBuilder.Entity<ValuationMethodologyMapping>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<ValuationMethodologyMapping>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<ValuationMethodologyMapping>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<HoldingReconRequest>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<HoldingReconRequest>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<HoldingReconRequest>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

      modelBuilder.Entity<DataUpdateFileUploads>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
      modelBuilder.Entity<DataUpdateFileUploads>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
      modelBuilder.Entity<DataUpdateFileUploads>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMaster>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMaster>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMaster>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterUser>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterUser>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterUser>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterSharingConfiguration>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterSharingConfiguration>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterSharingConfiguration>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<PortfolioDistributorSharingConfiguration>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<PortfolioDistributorSharingConfiguration>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<PortfolioDistributorSharingConfiguration>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<PortfolioDistributorPayout>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<PortfolioDistributorPayout>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<PortfolioDistributorPayout>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterBranches>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterBranches>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterBranches>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterRegion>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterRegion>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterRegion>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterCertifications>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterCertifications>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterCertifications>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterPayoutDetails>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterPayoutDetails>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterPayoutDetails>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterEmpanelmentDetails>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterEmpanelmentDetails>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterEmpanelmentDetails>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterDocuments>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterDocuments>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterDocuments>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<MasterLookupValues>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<MasterLookupValues>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<MasterLookupValues>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterStatutoryInfo>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterStatutoryInfo>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterStatutoryInfo>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterBranchesAMC>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterBranchesAMC>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterBranchesAMC>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<DistributorMasterRegionAMC>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<DistributorMasterRegionAMC>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<DistributorMasterRegionAMC>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<MasterLookupValuesAMC>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<MasterLookupValuesAMC>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<MasterLookupValuesAMC>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<PortfolioManagerMaster>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<PortfolioManagerMaster>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<PortfolioManagerMaster>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<PortfolioDistributorMapping>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<PortfolioDistributorMapping>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<PortfolioDistributorMapping>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<ComplianceRuleType>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ComplianceRuleType>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ComplianceRuleType>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<ComplianceRuleSubType>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ComplianceRuleSubType>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ComplianceRuleSubType>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<CompliancePreTradeRule>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<CompliancePreTradeRule>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<CompliancePreTradeRule>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<PortfolioCapitalRegisterTransactions>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<PortfolioCapitalRegisterTransactions>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<PortfolioCapitalRegisterTransactions>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
			modelBuilder.Entity<PortfolioHolder>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<PortfolioHolder>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<PortfolioHolder>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
			modelBuilder.Entity<ClientIncorporationDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ClientIncorporationDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ClientIncorporationDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
			modelBuilder.Entity<ClientOverseasDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ClientOverseasDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ClientOverseasDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
			modelBuilder.Entity<ClientAccreditedInvestorDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ClientAccreditedInvestorDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ClientAccreditedInvestorDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
			modelBuilder.Entity<ClientDueDiligence>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ClientDueDiligence>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ClientDueDiligence>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
			modelBuilder.Entity<ClientIdentity>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ClientIdentity>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ClientIdentity>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");
      
			modelBuilder.Entity<ClientFamilyDetail>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<ClientFamilyDetail>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<ClientFamilyDetail>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			modelBuilder.Entity<PortfolioForeignBankDetails>().Property(p => p.Id).HasDefaultValueSql("replace(lower(newid()),'-', '')");
			modelBuilder.Entity<PortfolioForeignBankDetails>().Property(p => p.CreatedDate).HasDefaultValueSql("getutcdate()");
			modelBuilder.Entity<PortfolioForeignBankDetails>().Property(p => p.LastUpdatedDate).HasDefaultValueSql("getutcdate()");

			

			modelBuilder.HasSequence<int>("OrderNumbers").StartsAt(1000).IncrementsBy(1);
      modelBuilder.Entity<TradeOrder>().Property(prop => prop.OrderNo).HasDefaultValueSql("NEXT VALUE FOR OrderNumbers");

      BuildIndexes(modelBuilder);

    }

    private void BuildIndexes(ModelBuilder modelBuilder)
    {
      //PortfolioCashLedger
      modelBuilder.Entity<PortfolioCashLedger>()
        .HasIndex(p => new { p.PortfolioId })
        .HasFilter("PortfolioId IS NOT NULL")
        .IncludeProperties(p => new { p.TransactionDate, p.TransactionType, p.TransactionSubType, p.TxnSequenceId, p.RunningBalance, p.Amount });

      //Investments
      modelBuilder.Entity<Investment>()
        .HasIndex(p => new { p.PortfolioId })
        .HasFilter("PortfolioId IS NOT NULL")
        .IncludeProperties(p => new
        {
          p.AssetClass,
          p.AveragePrice,
          p.Category,
          p.ClientId,
          p.MarketValue,
          p.Name,
          p.CurrentHolding,
          p.CurrentPrice,
          p.CurrentPriceDate,
          p.Dividends,
          p.FirstTransactionDate,
          p.LastTransactionDate,
          p.Symbol,
          p.Isin
        });

      //HistoricalModelPortfolioHolding
      modelBuilder.Entity<HistoricalModelPortfolioHolding>()
        .HasIndex(p => new { p.ModelPortfolioId, p.AsAtDate })
        .HasFilter("ModelPortfolioId IS NOT NULL and AsAtDate IS NOT NULL")
        .IncludeProperties(p => new
        {
          p.AssetClass,
          p.AveragePrice,
          p.Category,
          p.CurrentHolding,
          p.CurrentPrice,
          p.Dividends,
          p.Symbol,
          p.Isin,
          p.MarketValue,
          p.Name
        });

      //HistoricalModelPortfolioHolding
      modelBuilder.Entity<HistoricalModelPortfolioRecord>()
        .HasIndex(p => new { p.ModelPortfolioId, p.AsAtDate })
        .HasFilter("ModelPortfolioId IS NOT NULL and AsAtDate IS NOT NULL")
        .IncludeProperties(p => new
        {
          p.Name,
          p.TotalCapital,
          p.InvestedCapital,
          p.Dividends,
          p.MarketValue,
          p.AnnualReturnIrr,
          p.TwrrSinceInception,
          p.AnnualPerformanceTwrr
        });

      //HistoricalModelPortfolioHolding
      modelBuilder.Entity<HistoricalModelPortfolioHolding>()
        .HasIndex(p => new { p.AsAtDate })
        .HasFilter("AsAtDate IS NOT NULL")
        .IncludeProperties(p => new
        {
          p.AssetClass,
          p.AveragePrice,
          p.Category,
          p.CurrentHolding,
          p.CurrentPrice,
          p.Dividends,
          p.Symbol,
          p.Isin,
          p.MarketValue,
          p.Name
        });

      //HistoricalModelPortfolioHolding
      modelBuilder.Entity<HistoricalModelPortfolioRecord>()
        .HasIndex(p => new { p.AsAtDate })
        .HasFilter("AsAtDate IS NOT NULL")
        .IncludeProperties(p => new
        {
          p.Name,
          p.TotalCapital,
          p.InvestedCapital,
          p.Dividends,
          p.MarketValue,
          p.AnnualReturnIrr,
          p.TwrrSinceInception,
          p.AnnualPerformanceTwrr
        });

      // Reports Cron Config
      modelBuilder.Entity<ReportsCronConfig>()
          .Property(e => e.Frequency)
          .HasConversion<string>();

      modelBuilder.Entity<ReportsCronConfig>()
        .Property(e => e.ReportType)
        .HasConversion<string>();

      modelBuilder.Entity<ReportsCronConfig>()
        .Property(e => e.Status)
        .HasConversion<string>();

      modelBuilder.Entity<ReportsCronConfig>()
        .Property(e => e.ReportFormat)
        .HasConversion<string>();


      //Portfolio
      modelBuilder.Entity<Portfolio>()
      .Property(e => e.PortfolioType)
      .HasConversion<string>();

      modelBuilder.Entity<Portfolio>()
      .Property(e => e.AccountStatus)
      .HasConversion<string>();

      modelBuilder.Entity<Portfolio>()
      .Property(e => e.ModeOfOperation)
      .HasConversion<string>();

      modelBuilder.Entity<Portfolio>()
     .Property(e => e.TradingMode)
     .HasConversion<string>();

      modelBuilder.Entity<Portfolio>()
      .Property(e => e.FundSettlementMode)
      .HasConversion<string>();

      modelBuilder.Entity<Portfolio>()
      .HasIndex(e => e.ClientStrategyCode)
      .IsUnique();

      modelBuilder.Entity<Portfolio>()
      .HasIndex(e => e.CustodianPortfolioCode)
      .IsUnique();

      // Portfolio Nominee
      modelBuilder.Entity<PortfolioNomineeDetail>()
      .Property(e => e.Relationship)
      .HasConversion<string>();

      //Portfolio Preference
      modelBuilder.Entity<PortfolioPreference>()
      .Property(e => e.SendClientReport)
      .HasConversion<string>();

      modelBuilder.Entity<PortfolioPreference>()
      .Property(e => e.SendAlert)
      .HasConversion<string>();

      modelBuilder.Entity<PortfolioPreference>()
      .Property(e => e.AcceptOrderRequest)
      .HasConversion<string>();

      modelBuilder.Entity<PortfolioPreference>()
      .Property(e => e.FinancialMonthStartfrom)
      .HasConversion<string>();

      modelBuilder.Entity<ClientBank>()
      .Property(e => e.AccountStatus)
      .HasConversion<string>();

      modelBuilder.Entity<ClientBank>()
     .Property(e => e.BankAccountType)
     .HasConversion<string>();

      modelBuilder.Entity<ClientBank>()
      .Property(e => e.Currency)
      .HasConversion<string>();

      modelBuilder.Entity<ClientCustodian>()
      .Property(e => e.DPType)
      .HasConversion<string>();

      modelBuilder.Entity<ClientCustodian>()
     .Property(e => e.ModeofHolding)
     .HasConversion<string>();


      //Portfolio Fee Template
      modelBuilder.Entity<PortfolioFeeTemplate>()
      .Property(e => e.FixedFeeFrequency)
      .HasConversion<string>();

      modelBuilder.Entity<PortfolioFeeTemplate>()
      .Property(e => e.PerformanceFeeFrequency)
      .HasConversion<string>();

      modelBuilder.Entity<ClientOrderEntry>()
      .Property(e => e.ClientDomicile)
      .HasConversion<string>();

      //Client 
      modelBuilder.Entity<Client>()
      .Property(e => e.Domicile)
      .HasConversion<string>();

      modelBuilder.Entity<Client>()
      .Property(e => e.ClientType)
      .HasConversion<string>();

      modelBuilder.Entity<Client>()
      .Property(e => e.Title)
      .HasConversion<string>();

      modelBuilder.Entity<Client>()
      .HasIndex(e => e.ClientCode)
      .IsUnique();

      modelBuilder.Entity<ClientPersonalDetail>()
      .Property(e => e.MaritalStatus)
      .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
      .Property(e => e.Occupation)
      .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
      .Property(e => e.Nationality)
      .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
      .Property(e => e.TaxStatus)
      .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
     .Property(e => e.Category)
     .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
     .Property(e => e.ReportingCurreny)
     .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
     .Property(e => e.Qualification)
     .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
     .Property(e => e.WorkExpererience)
     .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
     .Property(e => e.IndustryType)
     .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
     .Property(e => e.GrossAnnualIncome)
     .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
     .Property(e => e.EstimatedFinancialWealth)
     .HasConversion<string>();

      modelBuilder.Entity<ClientPersonalDetail>()
      .Property(e => e.Gender)
      .HasConversion<string>();


      modelBuilder.Entity<Broker>()
      .HasMany(e => e.ClientBrokers)
      .WithOne(e => e.Broker)
      .HasForeignKey(e => e.BrokerId)
      .OnDelete(DeleteBehavior.Restrict);


      modelBuilder.Entity<ClientBroker>()
      .HasOne(e => e.Client)
      .WithMany()
      .HasForeignKey(e => e.ClientId)
      .OnDelete(DeleteBehavior.NoAction);


      modelBuilder.Entity<ClientCustodian>()
      .HasOne(p => p.Portfolio)
      .WithOne(cc => cc.ClientCustodian)
      .HasForeignKey<ClientCustodian>(cc => cc.PortfolioId);


      modelBuilder.Entity<ClientCustodian>()
      .HasOne(e => e.Custodian)
      .WithMany(e => e.ClientCustodians)
      .HasForeignKey(e => e.CustodianId)
      .OnDelete(DeleteBehavior.Restrict);

      modelBuilder.Entity<ClientCustodian>()
      .HasOne(e => e.Client)
      .WithMany()
      .HasForeignKey(e => e.ClientId)
      .OnDelete(DeleteBehavior.NoAction);


      // 1 - 1 Relation
      modelBuilder.Entity<FAInvestmentMapping>()
       .HasOne(e => e.Investment)
       .WithOne();


      modelBuilder.Entity<StrategyBank>()
      .Property(e => e.AccountStatus)
      .HasConversion<string>();

      modelBuilder.Entity<StrategyBank>()
      .Property(e => e.Currency)
      .HasConversion<string>();

      modelBuilder.Entity<StrategyBank>()
      .Property(e => e.BankAccountType)
      .HasConversion<string>();

      modelBuilder.Entity<HoldingReconRequest>()
      .Property(e => e.Status)
      .HasConversion<string>();

      modelBuilder.Entity<HoldingReconRequest>()
      .HasOne(e => e.Custodian)
      .WithMany()
      .HasForeignKey(e => e.CustodianId)
      .IsRequired()
      .OnDelete(DeleteBehavior.NoAction)
      ;

      //DataUpdate
      modelBuilder.Entity<DataUpdateFileUploads>()
      .Property(e => e.Status)
      .HasConversion<string>();

      modelBuilder.Entity<DataUpdateFileUploads>()
      .Property(e => e.Type)
      .HasConversion<string>();

      modelBuilder.Entity<CompliancePreTradeRule>()
      .Property(e => e.Level)
      .HasConversion<string>();

      modelBuilder.Entity<ComplianceRuleType>()
      .Property(e => e.RuleTypeName)
      .HasConversion<string>();

    }

    public virtual DbSet<Client> Clients { get; set; }
    public virtual DbSet<DirectEquityInvestment> DirectEquityInvestments { get; set; }
    public virtual DbSet<DirectEquityTransaction> DirectEquityTransactions { get; set; }
    public virtual DbSet<Model> StrategyModels { get; set; }
    public virtual DbSet<ModelSecurity> StrategyModelSecurities { get; set; }
    public virtual DbSet<Portfolio> Portfolios { get; set; }
    public virtual DbSet<ModelPortfolio> ModelPortfolios { get; set; }
    public virtual DbSet<Strategy> Strategies { get; set; }
    public virtual DbSet<ClientOrderEntry> ClientOrderEntries { get; set; }
    public virtual DbSet<PortfolioCapitalRegister> PortfolioCapitalRegisters { get; set; }
    public virtual DbSet<PortfolioCashLedger> PortfolioCashLedgers { get; set; }
    public virtual DbSet<PoolCapitalRegister> PoolCapitalRegisters { get; set; }
    public virtual DbSet<PoolCashLedger> PoolCashLedgers { get; set; }
    public virtual DbSet<Broker> Brokers { get; set; }
    public virtual DbSet<StrategyBroker> StrategyBrokers { get; set; }
    public virtual DbSet<StrategyBank> StrategyBanks { get; set; }
    public virtual DbSet<ClientBank> ClientBanks { get; set; }
    public virtual DbSet<Custodian> Custodians { get; set; }
    public virtual DbSet<GeneralSetting> GeneralSettings { get; set; }
    public virtual DbSet<StrategyCustodian> StrategyCustodians { get; set; }
    public virtual DbSet<RestrictedStocksForClient> RestrictedStocksForClients { get; set; }
    public virtual DbSet<RestrictedStockForOrganisation> RestrictedStockForOrganisation { get; set; }
    public virtual DbSet<TradeOrder> TradeOrders { get; set; }
    public virtual DbSet<TradeOrderSettlementFile> TradeOrderSettlementFiles { get; set; }
    public virtual DbSet<TradeOrderSettlementFileEntry> TradeOrderSettlementFileEntries { get; set; }
    public virtual DbSet<TradeSettlementLog> TradeSettlementLogs { get; set; }
    public virtual DbSet<ClientCustodian> ClientCustodians { get; set; }
    public virtual DbSet<TradeBuyIdea> TradeBuyIdeas { get; set; }
    public virtual DbSet<TradeSellIdea> TradeSellIdeas { get; set; }
    public virtual DbSet<OrderSettlementInClientAccount> OrderSettlementInClientAccounts { get; set; }
    public virtual DbSet<StrategyUser> StrategyUsers { get; set; }
    public virtual DbSet<BuyTradeIdea> BuyTradeIdeas { get; set; }
    public virtual DbSet<SellTradeIdea> SellTradeIdeas { get; set; }
    public virtual DbSet<MutualFundInvestment> MutualFundInvestments { get; set; }
    public virtual DbSet<MutualFundTransaction> MutualFundTransactions { get; set; }
    public virtual DbSet<BondInvestment> BondInvestments { get; set; }
    public virtual DbSet<BondTransaction> BondTransactions { get; set; }
    public virtual DbSet<CustomOrderRequest> CustomOrderRequests { get; set; }
    public virtual DbSet<CustomOrderEntry> CustomOrderEntries { get; set; }
    public virtual DbSet<TradeOrderUnsettledAmounts> TradeOrderUnsettledAmounts { get; set; }
    public virtual DbSet<BseSettlementCalendar> BseSettlementCalendarDates { get; set; }
    public virtual DbSet<Investment> Investments { get; set; }
    public virtual DbSet<InvestmentTransaction> InvestmentTransactions { get; set; }
    public virtual DbSet<FAInvestmentMapping> FAInvestmentMappings { get; set; }
    public virtual DbSet<SecurityType> SecurityTypes { get; set; }
    public virtual DbSet<BankReconProcess> BankReconProcesses { get; set; }
    public virtual DbSet<BankReconTransactionProcessInputData> BankReconTransactionInput { get; set; }
    public virtual DbSet<BankReconBalanceProcessInputData> BankReconBalancesInput { get; set; }
    public virtual DbSet<BankReconProcessResultData> BankReconProcessResults { get; set; }
    public virtual DbSet<HoldingReconCustodianInputData> HoldingReconCustodianInputDatas { get; set; }
    public virtual DbSet<HoldingReconProcessResultData> HoldingReconProcessResultDatas { get; set; }
    public virtual DbSet<HoldingReconInputFile> HoldingReconInputFiles { get; set; }
    public virtual DbSet<HoldingReconProcess> HoldingReconProcesses { get; set; }
    public virtual DbSet<FinFloClientSyncConfiguration> FinFloClientSyncConfigurations { get; set; }
    public virtual DbSet<ReportRequest> ReportRequests { get; set; }
    public virtual DbSet<HistoricalModelPortfolioHolding> HistoricalModelPortfolioHoldings { get; set; }
    public virtual DbSet<HistoricalModelPortfolioRecord> HistoricalModelPortfolioRecords { get; set; }
    public virtual DbSet<PortfolioAnalytics> PortfolioAnalytics { get; set; }
    public virtual DbSet<PortfolioAllocation> PortfolioAllocations { get; set; }
    public virtual DbSet<PortfolioAumDaily> PortfolioAumDaily { get; set; }
    public virtual DbSet<ClientBroker> ClientBrokers { get; set; }
    public virtual DbSet<PortfolioReceivable> PortfolioReceivables { get; set; }
    public virtual DbSet<PortfolioHoldingUpdate> PortfolioHoldingUpdates { get; set; }
    public virtual DbSet<PortfolioHoldingUpdateEntry> PortfolioHoldingUpdateEntries { get; set; }
    public virtual DbSet<SystematicDeployment> SystematicDeployments { get; set; }
    public virtual DbSet<DeploymentTracker> DeploymentTrackers { get; set; }
    public virtual DbSet<DeploymentTrackerLog> DeploymentTrackerLogs { get; set; }
    public virtual DbSet<FeeTemplate> FeeTemplates { get; set; }
    public virtual DbSet<StrategyModelFeeTemplate> StrategyModelFeeTemplates { get; set; }
    public virtual DbSet<PortfolioFeeTemplate> PortfolioFeeTemplates { get; set; }
    public virtual DbSet<PortfolioFee> PortfolioFees { get; set; }
    public virtual DbSet<PortfolioFeeTriggeredLog> PortfolioFeeTriggeredLogs { get; set; }
    public virtual DbSet<ClientPersonalDetail> ClientPersonalDetails { get; set; }
    public virtual DbSet<ClientContactDetail> ClientContactDetails { get; set; }

    public virtual DbSet<PortfolioRMDetail> PortfolioRMDetails { get; set; }
    public virtual DbSet<PortfolioPreference> PortfolioPreferences { get; set; }
    public virtual DbSet<PortfolioNomineeDetail> PortfolioNomineeDetails { get; set; }
    public virtual DbSet<PortfolioTransactionsPrefernce> PortfolioTransactionsPrefernces { get; set; }
    public virtual DbSet<AlphaTransformerIntegration> AlphaTransformerIntegrations { get; set; }
    public virtual DbSet<TransfomerFileRequest> TransformerFileRequests { get; set; }

    public virtual DbSet<PeakMarginLog> PeakMarginLogs { get; set; }

    public virtual DbSet<ReportsCronConfig> ReportsCronConfigs { get; set; }
    public virtual DbSet<SecurityMaster> SecurityMasters { get; set; }
    public virtual DbSet<SecurityPrice> SecurityPrices { get; set; }
    public virtual DbSet<ValuationMethodologyMapping> ValuationMethodologyMappings { get; set; }
    public virtual DbSet<HoldingReconRequest> HoldingReconRequest { get; set; }

    public virtual DbSet<DataUpdateFileUploads> DataUpdateFileUploads { get; set; }

		public virtual DbSet<DistributorMaster> DistributorMasters { get; set; }

		public virtual DbSet<DistributorMasterUser> DistributorMasterUsers { get; set; }
		public virtual DbSet<DistributorMasterSharingConfiguration> DistributorMasterSharingConfigurations { get; set; }

		public virtual DbSet<PortfolioDistributorSharingConfiguration> PortfolioDistributorSharingConfigurations { get; set; }

		public virtual DbSet<PortfolioDistributorPayout> PortfolioDistributorPayouts { get; set; }
		public virtual DbSet<DistributorMasterBranches> DistributorMasterBranches { get; set; }

		public virtual DbSet<DistributorMasterRegion> DistributorMasterRegions { get; set; }
    public virtual DbSet<DistributorMasterCertifications> DistributorMasterCertifications { get; set; }

		public virtual DbSet<DistributorMasterPayoutDetails> DistributorMasterPayoutDetails { get; set; }

		public virtual DbSet<DistributorMasterEmpanelmentDetails> DistributorMasterEmpanelmentDetails { get; set; }

		public virtual DbSet<DistributorMasterDocuments> DistributorMasterDocuments { get; set; }

		public virtual DbSet<MasterLookupValues> MasterLookupValues { get; set; }

		public virtual DbSet<DistributorMasterStatutoryInfo> DistributorMasterStatutoryInfos { get; set; }

		public virtual DbSet<DistributorMasterBranchesAMC> DistributorMasterBranchesAMCs { get; set; }
    public virtual DbSet<DistributorMasterRegionAMC> DistributorMasterRegionAMCs { get; set; }

		public virtual DbSet<MasterLookupValuesAMC> MasterLookupValuesAMCs { get; set; }

		public virtual DbSet<PortfolioManagerMaster> PortfolioManagerMasters { get; set; }

		public virtual DbSet<PortfolioDistributorMapping> PortfolioDistributorMappings { get; set; }

		public virtual DbSet<ComplianceRuleType> ComplianceRuleTypes { get; set; }

		public virtual DbSet<ComplianceRuleSubType> ComplianceRuleSubTypes { get; set; }

		public virtual DbSet<CompliancePreTradeRule> CompliancePreTradeRules { get; set; }

		public virtual DbSet<PortfolioCapitalRegisterTransactions> PortfolioCapitalRegisterTransactions { get; set; }
		public virtual DbSet<PortfolioHolder> PortfolioHolders { get; set; }
		public virtual DbSet<ClientIncorporationDetail> ClientIncorporationDetails { get; set; }
		public virtual DbSet<ClientOverseasDetail> ClientOverseasDetails { get; set; }
		public virtual DbSet<ClientAccreditedInvestorDetail> ClientAccreditedInvestorDetails { get; set; }
		public virtual DbSet<ClientDueDiligence> ClientDueDiligences { get; set; }
		public virtual DbSet<ClientIdentity> ClientIdentities { get; set; }
		public virtual DbSet<ClientFamilyDetail> ClientFamilyDetails { get; set; }
		public virtual DbSet<PortfolioForeignBankDetails> PortfolioForeignBankDetails { get; set; }

	}
}
