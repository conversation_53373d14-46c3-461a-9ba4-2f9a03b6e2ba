﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class TradeSettlementLog : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string TradeOrderId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TradeOrderSettlementFileId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TradeOrderSettlementFileEntryId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AppliedSettlementSellBuy { get; set; }
		public DateTime AppliedSettlementTradeDate { get; set; }
		public double AppliedSettlementQty { get; set; }
		public double AppliedSettlementMktRate { get; set; }
		public double AppliedSettlementMktAmount { get; set; }
		public double AppliedSettlementBrkg { get; set; }
		public double ApplidSettlementSerTax { get; set; }
		public double AppliedSettlementNetRate { get; set; }
		public double AppliedSettlementSttAmount { get; set; }
		public double AppliedSettlementTurnTax { get; set; }
		public double AppliedSettlementStampDuty { get; set; }
		public double AppliedSettlementNetAmount { get; set; }
		public virtual TradeOrder TradeOrder { get; set; }
		public virtual TradeOrderSettlementFile TradeOrderSettlementFile { get; set; }
		public virtual TradeOrderSettlementFileEntry TradeOrderSettlementFileEntry { get; set; }
	}
}
