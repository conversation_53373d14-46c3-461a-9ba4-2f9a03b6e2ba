﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DeploymentTracker : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string DeploymentSetupId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string SetupType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Status { get; set; }
		public DateTime TriggerDate { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TriggeredBy { get; set; }
		public int InstallmentNo { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string Rationale { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string DeploymentType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PreviousTrackerId { get; set; }
		public bool IsTriggered { get; set; }

		public bool IsGenerated { get; set; }
	}
}
