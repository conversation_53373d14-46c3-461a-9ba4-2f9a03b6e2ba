﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class StrategyModelFeeTemplate : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyModelId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string FeeTemplateId { get; set; }

		public virtual Model StrategyModel { get; set; }
		public virtual FeeTemplate FeeTemplate { get; set; }
	}
}
