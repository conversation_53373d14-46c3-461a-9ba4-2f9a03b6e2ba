﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PeakMarginLog : BaseTableEntity
	{

		[StringLength(50), Column(TypeName = "varchar")]
		public string peakMarginId { get; set; }



		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }

		public virtual Portfolio Portfolio { get; set; }

		public double PeakMarginPct { get; set; }
		public double Amount
		{
			get; set;
		}
		[StringLength(200), Column(TypeName = "varchar")]
		public string Status { get; set; }

		public DateTime Date { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string Type { get; set; }

	}
}
