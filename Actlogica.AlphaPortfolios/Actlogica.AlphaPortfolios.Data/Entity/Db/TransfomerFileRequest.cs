﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class TransfomerFileRequest : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string RequestedBy { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MetaDataRefId { get; set; }
		public int TransformerProjectCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AlphaTransformerIntegrationId { get; set; }
		[StringLength(4000), Column(TypeName = "varchar")]
		public string AlphaFilePath { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string OutputFilePath { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TransformType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ProcessingStatus { get; set; }
		public string FailureMessage { get; set; }
		[StringLength(4000), Column(TypeName = "varchar")]
		public string FailureDescription { get; set; }
	}
}
