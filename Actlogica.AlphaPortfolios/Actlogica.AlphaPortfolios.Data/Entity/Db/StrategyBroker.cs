﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class StrategyBroker : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string TradingAccountNumber { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyCustodianId { get; set; }
		public virtual StrategyCustodian StrategyCustodian { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BrokerId { get; set; }
		public virtual Broker Broker{ get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyId { get; set; }
	}
}
