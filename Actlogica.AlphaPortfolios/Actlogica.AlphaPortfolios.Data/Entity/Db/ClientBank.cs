﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientBank : BaseTableEntity
	{
		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string Name { get; set; }

		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string AddressLine1 { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string AddressLine2 { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string City { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string State { get; set; }

		[Required]
		[StringLength(20), Column(TypeName = "varchar")]
		public string Postcode { get; set; }

		[Required]
		[StringLength(200), Column(TypeName = "varchar")]
		public string AccountName { get; set; }

		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string AccountNumber { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string Ifsc { get; set; }

		[Required]
		[StringLength(250), Column(TypeName = "varchar")]
		public string Micr { get; set; }

		[Required]
		[StringLength(20)]
		public BankAccountType BankAccountType { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string AccountLink { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string BranchName { get; set; }

		[StringLength(50)]
		public Currency? Currency { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string SwiftCode { get; set; }

		[Required]
		[StringLength(20)]
		public AccountStatus AccountStatus { get; set; }

		public DateTime? FromDate { get; set; }
		public DateTime? ToDate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string SecondHolderName { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string ThirdHolderName { get; set; }
		public bool ForeignBankDetails { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string BankAccountCategory { get; set; }
		
	}
}
