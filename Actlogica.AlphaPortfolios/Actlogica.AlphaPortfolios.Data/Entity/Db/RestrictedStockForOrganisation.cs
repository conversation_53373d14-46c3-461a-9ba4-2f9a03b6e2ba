﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class RestrictedStockForOrganisation : BaseTableEntity
    {
        public string RestrictedSecurityIdentifier { get; set; }
        public string AlternativeSecurityIdentifier { get; set; }
        public string Rationale { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string ExchangeRestrictedSecurity { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string ExchangeAlternativeSecurity { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string IsinRestrictedSecurity { get; set; }
        
        [StringLength(50), Column(TypeName = "varchar")]
        public string IsinAlternativeSecurity { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string Status { get; set; }
    }
}
