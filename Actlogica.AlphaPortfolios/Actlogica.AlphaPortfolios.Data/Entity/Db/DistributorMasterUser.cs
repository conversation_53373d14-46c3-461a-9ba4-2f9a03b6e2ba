﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMasterUser : BaseTableEntity
	{

		[StringLength(150), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }
		public int UserId { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string EmployeeId { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string FirstName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string LastName { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string Email { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Mobile { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string UserName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AccessLevel { get; set; }

		public DateTime FromDate { get; set; }
		public DateTime? ToDate { get; set; }
		public string Remarks { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string Branch { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Region { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ReportTo { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string Email2 { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Mobile2 { get; set; }

		public DateTime? ResignedOn { get; set; }

		public bool LoginEnabled { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string Designation { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string Department { get; set; }


		[StringLength(50), Column(TypeName = "varchar")]
		public string Gender { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string Salutation { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]

		public string Role { get; set; }

	}
}
