﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class HoldingReconProcess : BaseTableEntity
  {
    public string ReconInputFilePath { get; set; }
    public string CustodianInputFilePath { get; set; }
    public int NumberOfCustodian { get; set; }
    public int NumberOfClientsInReconFile { get; set; }
    public DateTime OldestTransactionDate { get; set; }
    public DateTime RecentTransactionDate { get; set; }
    public DateTime ProcessingDate { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string ProcessingStatus { get; set; }
    public virtual ICollection<HoldingReconInputFile> HoldingReconEntries { get; set; }
    public virtual ICollection<HoldingReconCustodianInputData> CustodianInputEntries { get; set; }
    public virtual ICollection<HoldingReconProcessResultData> HoldingProcessOutputData { get; set; }
  }
}
