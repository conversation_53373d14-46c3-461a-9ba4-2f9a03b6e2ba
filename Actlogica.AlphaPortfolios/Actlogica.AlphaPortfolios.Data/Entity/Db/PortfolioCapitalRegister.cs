﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class PortfolioCapitalRegister : BaseTableEntity
    {
        [StringLength(50), Column(TypeName = "varchar")]
        public string TransactionType { get; set; }
        [StringLength(50), Column(TypeName = "varchar")]
        public string TransactionSubType { get; set; }
        public double Amount { get; set; }
        public double RunningBalance { get; set; }
        public string Description { get; set; }
        public DateTime TransactionDate { get; set; }
        public DateTime SettlementDate { get; set; }
        [StringLength(100), Column(TypeName = "varchar")]
        public string TxnRefId { get; set; }
        public bool IsModelPortfolio { get; set; }
        public string PortfolioId { get; set; }
        public virtual Portfolio Portfolio { get; set; }
        public string ModelportfolioId { get; set; }
        public virtual ModelPortfolio ModelPortfolio { get; set; }

		    public double Units { get; set; }
		    public double ExitLoadAmount { get; set; }
	}
}
