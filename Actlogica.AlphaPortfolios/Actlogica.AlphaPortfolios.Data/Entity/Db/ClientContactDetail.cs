﻿using Microsoft.VisualBasic;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientContactDetail : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }

		[Required]
		[StringLength(150), Column(TypeName = "varchar")]
		public string AddressLine1 { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string AddressLine2 { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string City { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string State { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string Country { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PinCode { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string MobileCountryCode { get; set; }

		[StringLength(20), Column(TypeName = "varchar")]
		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Phone Number")]
		public string MobileSecondary { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		[EmailAddress(ErrorMessage = "Invalid Email")]
		public string EmailSecondary { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string AddressType { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string AddressTypeIndividual { get; set; }
		public bool UsedForCommunication { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string AddressLine3 { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string District { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string GSTNo { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string GSTNoPlaceOfSupply { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]

		public string TelephoneOffice { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]

		public string TelephoneResidence { get; set; }

		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }

	}
}
