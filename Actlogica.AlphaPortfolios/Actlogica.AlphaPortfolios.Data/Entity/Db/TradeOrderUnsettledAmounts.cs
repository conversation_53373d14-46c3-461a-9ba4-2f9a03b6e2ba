﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class TradeOrderUnsettledAmounts : BaseTableEntity
  {
		public DateTime TradeDate { get; set; }
		public DateTime SettlementDate { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Type { get; set; }
		public double Amount { get; set; }
		public double Quantity { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string SettlementCalendarId { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientOrderEntryId { get; set; }
		public virtual ClientOrderEntry ClientOrderEntry { get; set; }
		
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyModelId { get; set; }
		public virtual Model StrategyModel { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
	}
}
