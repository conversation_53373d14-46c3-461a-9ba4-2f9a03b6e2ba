﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class HistoricalModelPortfolioHolding : BaseTableEntity
  {
    public DateTime AsAtDate { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string InvestmentId { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ModelPortfolioId { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string Name { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string Symbol { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string Isin { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string AmfiCode { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Exchange { get; set; }
    [StringLength(250), Column(TypeName = "varchar")]
    public string FundClass { get; set; }
    [StringLength(250), Column(TypeName = "varchar")]
    public string MarketCap { get; set; }
    [StringLength(250), Column(TypeName = "varchar")]
    public string Sector { get; set; }
    [StringLength(250), Column(TypeName = "varchar")]
    public string Category { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string AssetClass { get; set; }
    [StringLength(250), Column(TypeName = "varchar")]
    public string Rating { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string SecurityType { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string SecuritySubType { get; set; }
    public double CurrentPrice { get; set; }
    public double AveragePrice { get; set; }
    public double TotalCapital { get; set; }
    public double InvestedCapital { get; set; }
    public double MarketValue { get; set; }
    public double Dividends { get; set; }
    public double TotalRealisations { get; set; }
    public double RealisedGainLoss { get; set; }
    public double UnRealisedGainLoss { get; set; }
    public double IrrSinceInception { get; set; }
    public double IrrCurrent { get; set; }
    public double CurrentHolding { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string AsAtDateIndex { get; set; }
  }
}
