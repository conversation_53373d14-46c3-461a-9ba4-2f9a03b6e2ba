﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class StrategyUser : BaseTableEntity
	{
		public int UserId { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string FirstName { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string LastName { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string Email { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyId { get; set; }
		public int AssignedAs { get; set; }
	}
}
