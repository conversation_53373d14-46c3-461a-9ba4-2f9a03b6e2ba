﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientOverseasDetail : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Country { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string TaxIdentificationNo { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string IdentificationType { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ISO3166Code { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string OverseasAddressline1 { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string OverseasAddressline2 { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string OverseasAddressline3 { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string OverseasCity { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string OverseasDistrict { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string OverseasState { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }

		public virtual Client Client { get; set; }
	}
}
