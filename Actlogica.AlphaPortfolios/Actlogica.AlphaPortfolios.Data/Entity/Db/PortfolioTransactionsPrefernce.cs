﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioTransactionsPrefernce : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
		public bool? FixedIncome { get; set; }
		public bool? Equity { get; set; }
		public bool? MutualFund { get; set; }
		public bool? IPO { get; set; }
		public bool? Derivative { get; set; }
		public bool? Others { get; set; }

		public bool UnListedEquity { get; set; }

		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }
        public bool? DebtInstruments { get; set; }

	}
}
