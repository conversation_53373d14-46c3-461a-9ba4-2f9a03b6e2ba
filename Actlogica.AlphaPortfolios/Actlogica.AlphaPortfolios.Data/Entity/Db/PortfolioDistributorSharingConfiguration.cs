﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public  class PortfolioDistributorSharingConfiguration : BaseTableEntity
	{

		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }

		public virtual Portfolio Portfolio { get; set; }


		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterSharingConfigurationId { get; set; }

		public double FixedFeeSharingPercentage { get; set; }
		public double PerformanceFeeSharingPercentage { get; set; }
		public double UpFrontFeeSharingPercentage { get; set; }
		public double ExitLoadSharingPercentage { get; set; }

		public DateTime FromDate { get; set; }
		public DateTime? ToDate { get; set; }

		public string Remarks { get; set; }


		public double AMCMinRetentionInFixedFees { get; set; }
		public double ExitLoadWithin1YSharingPercentage { get; set; }
		public double ExitLoadWithin2YSharingPercentage { get; set; }
		public double ExitLoadWithin3YSharingPercentage { get; set; }


	}
}
