﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientIdentity : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string IdentityIDType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string IDNo { get; set; }
		public DateTime ExpiryDate { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
	}
}
