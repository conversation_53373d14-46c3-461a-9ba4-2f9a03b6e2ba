﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;
using System.Runtime.ConstrainedExecution;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMasterPayoutDetails : BaseTableEntity
	{
		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string Frequency { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string PayoutBank { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string PayoutBankIFSC { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]

		public string PayoutBankType { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]

		public string PayoutBankMIRC { get; set; }

		public bool PennyDropDone { get; set; }

		public DateTime ValidFrom { get; set; }

		public DateTime ValidTo { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string Status { get; set; }

		public bool IsDefault { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string PayoutBankAccNo { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string PayoutBankBranch { get; set; }

	}
}
