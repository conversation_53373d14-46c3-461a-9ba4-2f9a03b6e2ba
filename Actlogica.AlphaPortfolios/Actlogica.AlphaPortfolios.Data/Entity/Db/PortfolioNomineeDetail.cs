﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioNomineeDetail : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeName { get; set; }

		public bool? IsMinor { get; set; }

		public DateTime? NomineeDOB { get; set; }

		[StringLength(50)]
		public NomineeRelationship? Relationship { get; set; }

		[Range(0, 100)]
		public double? NomineeSharePercentage { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeNo { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianName { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianPAN { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianAddress { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string TPAName { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineePanNo { get; set; }


		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeIdentityIDType { get; set; }
		
		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeIdentityIDNo { get; set; }


		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeAddressType { get; set; }
		
		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeAddressLine1 { get; set; }
		
		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeAddressLine2 { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeAddressLine3 { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string NomineeCity { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string NomineeDistrict { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeState { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeCountry { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string NomineePincode { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string NomineeCountrycode { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string NomineeMobileNo { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string NomineeTelephoneOffice { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string NomineeEmailIDPrimary { get; set; }




		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianIdentityIDType { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianIdentityIDNo { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianAddressType { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianAddressLine1 { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianAddressLine2 { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianAddressLine3 { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string GuardianCity { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string GuardianDistrict { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianState { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianCountry { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string GuardianPincode { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string GuardianCountrycode { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string GuardianMobileNo { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string GuardianTelephoneOffice { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string GuardianEmailIDPrimary { get; set; }


		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }

	}
}
