
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.DataUpdates;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class DataUpdateFileUploads : BaseTableEntity
    {
        [Required]
        [StringLength(30), Column(TypeName = "varchar")]
        public DataUpdateType Type { get; set; }

        [Required]
        [StringLength(250), Column(TypeName = "varchar")]
        public string FilePath { get; set; }

        [Required]
        [StringLength(20), Column(TypeName = "varchar")]
        public DataUpdateStatus Status { get; set; }

        [Required]
        [StringLength(100), Column(TypeName = "varchar")]
        public string CreatedBy { get; set; }
        public string FailureMessage { get; set; }
        public string FailureDescription { get; set; }

        [Required]
        [StringLength(30), Column(TypeName = "varchar")]
        public DataUpdateMethod UpdateType { get; set; }

    }

}