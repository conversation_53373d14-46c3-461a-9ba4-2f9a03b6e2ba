﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class BankReconTransactionProcessInputData : BaseTableEntity
	{
		public DateTime TransactionInputFileDate { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime PostingDate { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string TransactionType { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string BankAccountNumber { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string BankBranch { get; set; }
		public double Amount { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string Description { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BankReconProcessId { get; set; }
		public virtual BankReconProcess BankReconProcess { get; set; }
	}
}
