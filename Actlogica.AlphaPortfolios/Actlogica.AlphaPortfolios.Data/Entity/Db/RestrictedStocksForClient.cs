﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class RestrictedStocksForClient : BaseTableEntity
    {
        public string RestrictedSecurityIdentifier { get; set; }
        public string AlternativeSecurityIdentifier { get; set; }
        public string Rationale { get; set; }

        [StringLength(100), Column(TypeName = "varchar")]
        public string SecurityType { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string ExchangeRestrictedSecurity { get; set; }
        
        [StringLength(50), Column(TypeName = "varchar")]
        public string ExchangeAlternativeSecurity { get; set; }
        public string ClientId { get; set; }
        public virtual Client Client { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string IsinRestrictedSecurity { get; set; }
        [StringLength(50), Column(TypeName = "varchar")]
        public string IsinAlternativeSecurity { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string Status { get; set; }
    }
}
