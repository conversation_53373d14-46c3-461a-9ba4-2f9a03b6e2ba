﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioPreference : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }

		public virtual Portfolio Portfolio { get; set; }

		[StringLength(20)]
		public SendPortfolioReportBy? SendClientReport { get; set; }

		[StringLength(20)]
		public SendAlertBy? SendAlert { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string SendTo { get; set; }

		[StringLength(50)]
		public AcceptOrderRequest? AcceptOrderRequest { get; set; }
		
		[StringLength(50)]
		public FinancialMonthStartfrom? FinancialMonthStartfrom { get; set; }

	}
}
