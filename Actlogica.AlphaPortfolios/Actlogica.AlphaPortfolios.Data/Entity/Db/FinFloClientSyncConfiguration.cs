﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class FinFloClientSyncConfiguration : BaseTableEntity
  {
    [StringLength(50), Column(TypeName = "varchar")]
    public string ClientId { get; set; }
    public Client Client { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string PortfolioId { get; set; }
    public Portfolio Portfolio { get; set; }
    public DateTime LastTransactionSyncDateTime { get; set; }
    public DateTime LastCapitalRegisterSyncDateTime { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string LicenseNumber { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string LastSyncStatus { get; set; }
    [StringLength(500), Column(TypeName = "varchar")]
    public string LastSyncRemarks { get; set; }
    public bool IsEnabled { get; set; }
  }
}
