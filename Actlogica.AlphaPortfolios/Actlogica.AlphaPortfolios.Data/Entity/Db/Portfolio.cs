﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class Portfolio : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ModelId { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientStrategyCode { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string CustodianPortfolioCode { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string FAAccountNo { get; set; }

		[Required]
		[StringLength(200), Column(TypeName = "varchar")]
		public string Name { get; set; }

		[Required]
		[StringLength(50)]
		public PortfolioType PortfolioType { get; set; }

		public DateTime StartDate { get; set; }

		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double AnnualReturnIrr { get; set; }
		public double AnnualReturnIrrUnrealised { get; set; }
		public double TwrrSinceInception { get; set; }
		public double AnnualPerformanceTwrr { get; set; }
		public double CurrentCashBalance { get; set; }
		public double UnsettledBuyOrderAmount { get; set; }
		public double UnsettledCashAmount { get; set; }
		public double CurrentAssetsAndLiabilities { get; set; }
		public double NetReceivables {get; set; }
		public double NetPayables {get; set; }

		[Required]
		[StringLength(20)]
		public AccountStatus AccountStatus { get; set; }

		[Required]
		[StringLength(20)]
		public ModeOfOperation ModeOfOperation { get; set; }

		[Required]
		[StringLength(20)]
		public TradingMode TradingMode { get; set; }

		[Required]
		[StringLength(20)]
		public FundSettlementMode FundSettlementMode { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string StockSettlementMode { get; set; }

		[Required]
		public bool POAOnBank { get; set; }

		[Required]
		public bool POAOnDemat { get; set; }

		[Required]
		public bool POAOnMF { get; set; }

		[StringLength(50),Column(TypeName = "varchar")]
		public string AccountName { get; set; }

		public virtual Client Client { get; set; }

		public virtual Model Model { get; set; }

		public virtual ICollection<Investment> Investments { get; set; }

		public virtual ICollection<PortfolioNomineeDetail> PortfolioNomineeDetails { get; set; }

		public virtual PortfolioPreference PortfolioPreference { get; set; }

		public virtual ICollection<PortfolioRMDetail> PortfolioRMDetails { get; set; }

		public virtual PortfolioTransactionsPrefernce PortfolioTransactionsPreference { get; set; }
		public virtual PortfolioFeeTemplate PortfolioFeeTemplate { get; set; }

		public virtual ClientBank ClientBank { get; set; }

		public virtual ClientBroker ClientBroker { get; set; }

		public virtual ClientCustodian ClientCustodian { get; set; }
		public int BenchmarkIndexCode { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string BenchmarkIndexName { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }

		public DateTime? AgreementSigningDate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string StratergyAssetClass { get; set; }

		public bool IsPermissionInvestInAssociates { get; set; }

		public int PctIndEquityConsent { get; set; }
		public int PctIndDebtConsent { get; set; }
		public bool IsConsentRebalancePassive { get; set; }
		public bool IsConsentInvestInEquityDerivate { get; set; }
		public bool IsConsentInvestInCommodityDerivate { get; set; }
		public int PctDerivateConsent { get; set; }
		public bool IsConsentLending { get; set; }

		public DateTime? InactiveSince { get; set; }

		[StringLength(1000), Column(TypeName = "varchar")]
		public String AccountInactivityDesc { get; set; }
		public DateTime? DateOfPMSAccountClosure { get; set; }

		public virtual PortfolioDistributorSharingConfiguration PortfolioDistributorSharingConfiguration { get; set; }

		public bool IsDividendPaidToClient { get; set; }
		public virtual PortfolioForeignBankDetails PortfolioForeignBankDetails { get; set; }
		
	}
}
