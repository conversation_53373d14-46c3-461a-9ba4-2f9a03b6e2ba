﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMasterEmpanelmentDetails : BaseTableEntity
	{
		[StringLength(150), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string StratergyId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]

		public string EmpanelmentType  { get; set; } //(Distributor/RIA)
}
}
