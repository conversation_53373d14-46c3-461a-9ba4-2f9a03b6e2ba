﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class FeeTemplate : BaseTableEntity
	{
		[StringLength(150), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Type { get; set; }
		public double FixedFeePercentage { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string FixedFeeFrequency { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string PerformanceFeeFrequency { get; set; }
		public double PerformanceFeeSharingPercentage { get; set; }
		public double PerformanceFeeHurdleRate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string AMCMinRetentionInFixedFees { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string HurdleRateRedeemedWithin1Year { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string HurdleRateRedeemedWithin2Year { get; set; }
		public bool WithCatchup { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string ExitLoadWithin1Y { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ExitLoadWithin2Y { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ExitLoadWithin3Y { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string FixedFeeType { get; set; }
		public double FixedFeeAbsolute { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string FeesLevyMechansim { get; set; }
		public double HurdleRate { get; set; }
		public double PerformanceFeeLevyRate { get; set; }
		public double PerformanceFeeRetention { get; set; }
	}
}
