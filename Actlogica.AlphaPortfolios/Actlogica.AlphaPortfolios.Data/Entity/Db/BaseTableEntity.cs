﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.OData.Edm;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class BaseTableEntity
	{
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		[StringLength(50), Column(TypeName = "varchar")]
		public string Id { get; private set; }

		[DatabaseGenerated(DatabaseGeneratedOption.Computed)]
		public DateTime CreatedDate { get; set; }

		public DateTime LastUpdatedDate { get; set; }
	}
}
