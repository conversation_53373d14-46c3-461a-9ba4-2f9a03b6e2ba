﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class BondTransaction : BaseTableEntity
	{
		public string InvestmentId { get; set; }
		public BondInvestment Investment { get; set; }
		public string PortfolioId { get; set; }
		public Portfolio Portfolio { get; set; }
		public string ClientId { get; set; }
		public Client Client { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public double Quantity { get; set; }
		public double Price { get; set; }
		public double Amount { get; set; }
		public double Brokerage { get; set; }
		public double ServiceTax { get; set; }
		public double SttAmount { get; set; }
		public double TurnTax { get; set; }
		public double OtherTax { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Type { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string SubType { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		public double CurrentHolding { get; set; }
		public double UnrealisedHolding { get; set; }
	}
}
