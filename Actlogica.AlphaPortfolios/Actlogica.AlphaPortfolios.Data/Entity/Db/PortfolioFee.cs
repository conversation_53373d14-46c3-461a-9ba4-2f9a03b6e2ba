﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioFee : BaseTableEntity
	{
		public DateTime PeriodFrom { get; set; }
		public DateTime PeriodTo { get; set; }
		public double FixedFeeAmount { get; set; }
		public double FixedFeePercentageApplied { get; set; }
		public double PerformanceFeeAmount { get; set; }
		public double HurdleRateApplied { get; set; }
		public double SharingPercentage { get; set; }
		public double WatermarkApplied { get; set; }
		public double PortfolioReturnForPeriod { get; set; }
		public double OpeningAum { get; set; }
		public double ClosingAum { get; set; }
		public double NetCashFlow { get; set; }
		public double AverageAum { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Status { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioFeeTemplateId { get; set; }
		public virtual PortfolioFeeTemplate PortfolioFeeTemplate { get; set; }


		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }


		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioFeeTriggeredLogId { get; set; }
		[StringLength(1000), Column(TypeName = "varchar")]

		public string Remarks { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]

		public string ReasonForEdit { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]

		public string UserName { get; set; }
	}
}
