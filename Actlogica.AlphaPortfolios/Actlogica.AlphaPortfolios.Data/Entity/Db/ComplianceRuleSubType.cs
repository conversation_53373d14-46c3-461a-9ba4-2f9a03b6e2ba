﻿

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;
using System.Runtime.ConstrainedExecution;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ComplianceRuleSubType : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string RuleTypeId { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string SubTypeName { get; set; }

		public bool IsActive { get; set; }
	}
}
