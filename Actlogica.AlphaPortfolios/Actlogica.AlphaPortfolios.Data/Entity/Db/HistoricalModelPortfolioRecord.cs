﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class HistoricalModelPortfolioRecord : BaseTableEntity
  {
    [StringLength(50), Column(TypeName = "varchar")]
    public string ModelPortfolioId { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string Name { get; set; }
    public DateTime AsAtDate { get; set; }
    public double TotalCapital { get; set; }
    public double InvestedCapital { get; set; }
    public double RealisedGainLoss { get; set; }
    public double UnRealisedGainLoss { get; set; }
    public double Dividends { get; set; }
    public double MarketValue { get; set; }
    public double AnnualReturnIrr { get; set; }
    public double AnnualReturnIrrUnrealised { get; set; }
    public double TwrrSinceInception { get; set; }
    public double AnnualPerformanceTwrr { get; set; }
    public double CurrentCashBalance { get; set; }
    [StringLength(150), Column(TypeName = "varchar")]
    public string AsAtDateIndex { get; set; }
  }
}
