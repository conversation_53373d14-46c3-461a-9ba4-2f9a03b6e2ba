﻿using Microsoft.OData.Edm;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  //public class HoldingReconFileInputEntry : BaseTableEntity
  //{
  //  public string BrokerCode { get; set; }
  //  public string CliendCode { get; set; }
  //  public string ISIN { get; set; }
  //  public string Exchange { get; set; }
  //  public string TransactionType { get; set; }
  //  public Date AcquisitionDate { get; set; }
  //  public Date SettlementDate { get; set; }
  //  public decimal Quantity { get; set; }
  //  public decimal Price { get; set; }
  //  public decimal Brokerage { get; set; }
  //  public decimal ServiceTax { get; set; }
  //  public string SettlementDateFlag { get; set; }
  //  public decimal MarketRate { get; set; }
  //  public string CashSymbol { get; set; }
  //  public string STTAmount { get; set; }
  //  public decimal AccruedInterest { get; set; }
  //  public string BlockRef { get; set; }
  //  public string TransRef { get; set; }
  //  public string Remarks { get; set; }
  //  public string FileType { get; set; } = "HDFC";
  //}
}
