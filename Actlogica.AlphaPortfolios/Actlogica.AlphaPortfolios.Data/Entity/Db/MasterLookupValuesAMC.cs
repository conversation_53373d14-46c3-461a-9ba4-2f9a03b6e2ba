﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;
using System.Runtime.ConstrainedExecution;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class MasterLookupValuesAMC : BaseTableEntity
	{
		[StringLength(250), Column(TypeName = "varchar")]
		public string Value { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string Type { get; set; }

		public bool IsActive { get; set; }
	}
}
