﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class PoolCapitalRegister : BaseTableEntity
    {
        [StringLength(30), Column(TypeName = "varchar")]
        public string TransactionType { get; set; }
        [StringLength(30), Column(TypeName = "varchar")]
        public string TransactionSubType { get; set; }
        public double TransactionAmount { get; set; }
        public double RunningBalance { get; set; }
        [StringLength(500), Column(TypeName = "varchar")]
        public string Description { get; set; }
        public DateTime TransactionDate { get; set; }
        public DateTime SettlementDate { get; set; }
        [StringLength(100), Column(TypeName = "varchar")]
        public string TxnRefId { get; set; }
        public bool IsModelPortfolio { get; set; }
        public string StrategyId { get; set; }
        public virtual Strategy Strategy { get; set; }
    }
}
