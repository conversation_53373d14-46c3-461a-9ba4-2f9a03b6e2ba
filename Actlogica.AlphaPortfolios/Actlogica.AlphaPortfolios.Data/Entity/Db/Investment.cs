﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class Investment : BaseTableEntity
  {
		[StringLength(200), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public Portfolio Portfolio { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ModelportfolioId { get; set; }
		public ModelPortfolio ModelPortfolio { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public Client Client { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		public double CurrentPrice { get; set; }
		public DateTime CurrentPriceDate { get; set; }
		public double CurrentHolding { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double AveragePrice { get; set; }
		public double TotalRealisations { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double Dividends { get; set; }
		public double IrrSinceInception { get; set; }
		public double IrrCurrent { get; set; }
		public DateTime FirstTransactionDate { get; set; }
		public DateTime LastTransactionDate { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MarketCap { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Sector { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string FundClass { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Category { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AssetClass { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AmfiCode { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Rating { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string SecuritySubType { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string SecurityType { get; set; }
		public virtual IEnumerable<InvestmentTransaction> Transactions { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string IssuerName { get; set; }

		public bool IsSecurityAssociated { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string SecurityRating { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]

		public string RatingAgency { get; set; }

		public DateTime MaturityDate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string OptionType { get; set; }


	}
}
