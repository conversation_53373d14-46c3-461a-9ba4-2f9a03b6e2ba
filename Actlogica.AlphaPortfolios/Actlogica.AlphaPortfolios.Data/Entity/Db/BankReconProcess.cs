﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class BankReconProcess : BaseTableEntity
	{
        public string TransactionInputFilePath { get; set; }
        public string BalancesInputFilePath { get; set; }
		public int NumberOfTransactions { get; set; }
		public int NumberOfClientsInTxnFile { get; set; }
		public int NumberOfBalances { get; set; }
		public int NumberOfClientsInBalancesFile { get; set; }
		public DateTime OldestTransactionDate { get; set; }
		public DateTime RecentTransactionDate { get; set; }
		public DateTime ProcessingDate { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ProcessingStatus { get; set; }
		public virtual ICollection<BankReconTransactionProcessInputData> TransactionEntries { get; set; }
		public virtual ICollection<BankReconBalanceProcessInputData> BalancesEntries { get; set; }
		public virtual ICollection<BankReconProcessResultData> ReconProcessOutputData { get; set; }
	}
}
