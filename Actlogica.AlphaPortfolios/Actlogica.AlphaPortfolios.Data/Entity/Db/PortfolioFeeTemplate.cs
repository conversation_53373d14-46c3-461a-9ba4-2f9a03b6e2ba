﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;
using Actlogica.AlphaPortfolios.ApiContracts.Fees;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioFeeTemplate : BaseTableEntity
	{
		[Required]
		[StringLength(150), Column(TypeName = "varchar")]
		public string Name { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string Type { get; set; }
		public double FixedFeePercentage { get; set; }

		[StringLength(50)]
		public FixedFeeFrequency? FixedFeeFrequency { get; set; }

		[StringLength(50)]
		public PerformanceFeeFrequency? PerformanceFeeFrequency { get; set; }
		public double PerformanceFeeSharingPercentage { get; set; }
		public double PerformanceFeeHurdleRate { get; set; }
		public DateTime AppliedFromDate { get; set; }
		public DateTime AppliedToDate { get; set; }

		[StringLength(250), Column(TypeName = "varchar")]
		public string ApprovedBy { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyModelFeeTemplateId { get; set; }
		public virtual StrategyModelFeeTemplate StrategyModelFeeTemplate { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }



		[StringLength(100), Column(TypeName = "varchar")]
		public string AMCMinRetentionInFixedFees { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string HurdleRateRedeemedWithin1Year { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string HurdleRateRedeemedWithin2Year { get; set; }

		public bool WithCatchup { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string ExitLoadWithin1Y { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ExitLoadWithin2Y { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ExitLoadWithin3Y { get; set; }

		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string FixedFeeType { get; set; }
		public double FixedFeeAbsolute { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string FeesLevyMechansim { get; set; }
		public double HurdleRate { get; set; }
		public double PerformanceFeeLevyRate { get; set; }
		public double PerformanceFeeRetention { get; set; }

	}
}
