using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.HoldingRecon;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class HoldingReconRequest : BaseTableEntity
    {

        [Required]
        [Column(TypeName = "varchar(max)")]
        public string CustodyFilePath { get; set; }

        [Required]
        [StringLength(50), Column(TypeName = "varchar")]
        public string CustodianId { get; set; }

        public Custodian Custodian { get; set; }

        [Column(TypeName = "varchar(max)")]
        public string FundAccountantFilePath { get; set; }

        [Column(TypeName = "varchar(max)")]
        public string ReconResultFilePath { get; set; }

        [Required]
        [StringLength(20)]
        [Column(TypeName = "varchar")]
        public HoldingReconRequestStatus Status { get; set; }

        [Required]
        [StringLength(200), Column(TypeName = "varchar")]
        public string Remarks { get; set; }

        [Required]
        [MaxLength(50), MinLength(2), Column(TypeName = "varchar")]
        public string RequestedBy { get; set; }

    }
}
