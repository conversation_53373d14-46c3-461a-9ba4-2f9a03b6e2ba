﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class AlphaTransformerIntegration : BaseTableEntity
	{
		public int TransformerProjectCode { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string TransformerProjectName { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string FromName { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string ToName { get; set; }

		[StringLength(150), Column(TypeName = "varchar")]
		public string FileType { get; set; }

		[StringLength(350), Column(TypeName = "varchar")]
		public string TransformationDescription { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string FromRefId { get; set; }

		[StringLength(50), <PERSON>umn(TypeName = "varchar")]
		public string ToRefId { get; set; }
	}
}
