﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;
using DocumentFormat.OpenXml.Drawing;
using Actlogica.AlphaPortfolios.ApiContracts.Communications.Email;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public  class DistributorMasterStatutoryInfo : BaseTableEntity
	{
		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string PAN { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string APMIRegNo { get; set; }
		public DateTime APMIRegValidfrom { get; set; }
		public DateTime APMIRegValidto { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]

		public string AMFIRegNo { get; set; }
		public DateTime AMFIRegValidFrom { get; set; }
		public DateTime AMFIRegValidto { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]

		public string FirmNo { get; set; }
		public DateTime? DateofIncorporation { get; set; }

	}
}
