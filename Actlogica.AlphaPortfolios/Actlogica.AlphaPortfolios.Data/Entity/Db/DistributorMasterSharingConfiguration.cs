﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class DistributorMasterSharingConfiguration : BaseTableEntity
	{
		[StringLength(100), Column(TypeName = "varchar")]
		public string DistributorMasterId { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string StratergyId { get; set; }

		public double FixedFeeSharingPercentage { get; set; }
		public double PerformanceFeeSharingPercentage { get; set; }
		public double UpFrontFeeSharingPercentage { get; set; }
		public double ExitLoadSharingPercentage { get; set; }

		public DateTime FromDate { get; set; }
		public DateTime? ToDate { get; set; }

		public string Remarks { get; set; }

		public double AMCMinimumRetention { get; set; }


	}
}
