﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class ReportRequest : BaseTableEntity
  {
    public DateTime RequestDate { get; set; }
    
    [StringLength(50), Column(TypeName = "varchar")]
    public string RequestedBy { get; set; }

    [StringLength(100), Column(TypeName = "varchar")]
    public string ReportType { get; set; }

    public string RequestPayload { get; set; }

    [StringLength(100), Column(TypeName = "varchar")]
    public string Status { get; set; }
    
    [StringLength(4000), Column(TypeName = "varchar")]
    public string ReportPath { get; set; }
  }
}
