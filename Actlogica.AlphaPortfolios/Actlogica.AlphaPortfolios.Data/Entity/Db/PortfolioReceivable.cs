﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioReceivable : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string InvestmentId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string InvestmentName { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string CorporateActionType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TransactionType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TransactionSubType { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public DateTime CgtDate { get; set; }
		public double Amount { get; set; }
		public double Quantity { get; set; }
		public double Price { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string ReceivableStatus { get; set; }
		[StringLength(1000), Column(TypeName = "varchar")]
		public string Remarks { get; set; }
	}
}
