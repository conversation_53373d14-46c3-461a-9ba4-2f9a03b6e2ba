﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class HoldingReconInputFile : BaseTableEntity
  {
    [StringLength(20), Column(TypeName = "varchar")]
    public string ClientCode { get; set; }
    [StringLength(200), Column(TypeName = "varchar")]
    public string ClientName { get; set; }
    [StringLength(20), Column(TypeName = "varchar")]

    public string ClientSchemeCode { get; set; }
    [StringLength(20), Column(TypeName = "varchar")]

    public string InstrumentCode { get; set; }
    [StringLength(200), Column(TypeName = "varchar")]

    public string InstrumentName { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string ISINCode { get; set; }
    public double TotalFaceValue { get; set; }
    public double BookPosition { get; set; }
    public double TotalPhysicalSaleable { get; set; }
    public double TotalDematSaleableNet { get; set; }
    public double TotalSaleable { get; set; }
    public double PendingPurchase { get; set; }
    public double PendingCA { get; set; }
    public double AccruedInterest { get; set; }
    public double BlockRef { get; set; }
    public double PendingBlockedQty { get; set; }
    public double PendingPM { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string FileType { get; set; } = "HDFC";
    [StringLength(50), Column(TypeName = "varchar")]
    public string HoldingReconProcessId { get; set; }


  }
}
