﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientBroker : BaseTableEntity
	{
		[Required]
		[StringLength(200), Column(TypeName = "varchar")]
		public string TradingAccountNumber { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string CPCode { get; set; }


		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientCustodianId { get; set; }
		public virtual ClientCustodian ClientCustodian { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string BrokerId { get; set; }
		public virtual Broker Broker { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
	}
}
