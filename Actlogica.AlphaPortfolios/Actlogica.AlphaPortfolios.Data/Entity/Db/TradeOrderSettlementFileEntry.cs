﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class TradeOrderSettlementFileEntry : BaseTableEntity
	{

		[StringLength(75), Column(TypeName = "varchar")]
		public string SrNo { get; set; }
		[StringLength(75), Column(TypeName = "varchar")]
		public string ContractNumber { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TradeOrderSettlementFileId { get; set; }
		[StringLength(75), Column(TypeName = "varchar")]
		public string PartyCode { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string ClientShortName { get; set; }
		[StringLength(75), Column(TypeName = "varchar")]
		public string CustodyClearingCode { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string ScripCode { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(400), Column(TypeName = "varchar")]
		public string ScripName { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Series { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string SttNo { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string SettType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string SellBuy { get; set; }
		public DateTime TradeDate { get; set; }
		public double TradeQty { get; set; }
		public double PendingQtyAvailableForSettlement { get; set; }
		public double MarketRate { get; set; }
		public double MarketAmount { get; set; }
		public double Brokerage { get; set; }
		public double BrokeragePerUnit { get; set; }
		public double ServiceTax { get; set; }
		public double NetRate { get; set; }
		public double SttAmount { get; set; }
		public double TurnTax { get; set; }
		public double StampDuty { get; set; }
		public double NetAmount { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string DematPhysical { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string DPFolioNo { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string FolioNo { get; set; }
		[StringLength(30), Column(TypeName = "varchar")]
		public string BuySellType { get; set; }
		[StringLength(500), Column(TypeName = "varchar")]
		public string OrderRemarks { get; set; }
		public virtual TradeOrderSettlementFile TradeOrderSettlementFile { get; set; }
	}
}
