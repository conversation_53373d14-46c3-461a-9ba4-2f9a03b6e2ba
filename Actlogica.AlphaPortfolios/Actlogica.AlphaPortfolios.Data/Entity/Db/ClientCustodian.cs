﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientCustodian : BaseTableEntity
	{
		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string CustodianId { get; set; }

		public virtual Custodian Custodian { get; set; }

		[Required]
		[StringLength(100), Column(TypeName = "varchar")]
		public string CustodyAccountNumber { get; set; }

		[Required]
		[StringLength(20)]
		public DpType DPType { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public virtual Client Client { get; set; }

		[Required]
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }

		[Required]
		[StringLength(50)]
		public ModeOfHolding ModeofHolding { get; set; }
		public DateTime? FromDate { get; set; }
		public DateTime? ToDate { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string SecondHolderName { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string ThirdHolderName { get; set; }

		[StringLength(500), Column(TypeName = "varchar")]
		public string SecondHolderPAN { get; set; }

		[StringLength(500), Column(TypeName = "varchar")]
		public string ThirdHolderPAN { get; set; }

		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
		public string SecondHolderPhone { get; set; }

		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
		public string ThirdHolderPhone { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		[EmailAddress(ErrorMessage = "Invalid Email Id")]
		public string SecondHolderEmail { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		[EmailAddress(ErrorMessage = "Invalid Email Id")]
		public string ThirdHolderEmail { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string MFUCCDemat { get; set; }

		[StringLength(50), Column(TypeName = "varchar")]
		public string MFUCCPhysical { get; set; }


		[StringLength(50), Column(TypeName = "varchar")]
		public string DPID { get; set; }

		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }



	}
}
