﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class ClientMfTradingAccount : BaseTableEntity
  {
    [StringLength(20), Column(TypeName = "varchar")]
    public string TradingCode { get; set; }
    [StringLength(200), Column(TypeName = "varchar")]
    public string HoldingPattern { get; set; }
  }
}
