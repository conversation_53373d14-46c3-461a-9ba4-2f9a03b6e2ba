﻿

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;
using System.Runtime.ConstrainedExecution;
using Actlogica.AlphaPortfolios.ApiContracts.TradeCompliance;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ComplianceRuleType : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public PreTradeRuleTypes RuleTypeName { get; set; }

		public bool IsActive { get; set; }
	}
}
