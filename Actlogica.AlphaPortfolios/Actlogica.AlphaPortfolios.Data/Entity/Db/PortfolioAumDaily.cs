﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioAumDaily : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double NetCashFlow { get; set; }
		public double CashBalance { get; set; }
		public DateTime AsAtDate { get; set; }
		public double Twrr { get; set; }
		public double Nav { get; set; }
		public double Change { get; set; }
	}
}
