﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientAccreditedInvestorDetail : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string AccrediationAgency { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AccrediationCertificateNumber { get; set; }
		public DateTime CertificateValidityFrom { get; set; }
		public DateTime CertificateValidityTo { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
	}
}
