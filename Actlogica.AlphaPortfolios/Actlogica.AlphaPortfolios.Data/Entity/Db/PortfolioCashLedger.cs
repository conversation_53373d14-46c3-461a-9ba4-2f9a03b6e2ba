﻿using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	[Index(nameof(PortfolioId), Name = $"Unique_{nameof(PortfolioId)}")]
	[Index(nameof(TransactionType), Name = $"Unique_{nameof(TransactionType)}")]
	[Index(nameof(TransactionSubType), Name = $"Unique_{nameof(TransactionSubType)}")]
	public class PortfolioCashLedger : BaseTableEntity
	{
		public DateTime TransactionDate { get; set; }
		public DateTime SettlementDate { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TransactionType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TransactionSubType { get; set; }
		public double Amount { get; set; }
		public double RunningBalance { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string TxnRefId { get; set; }
		public string Description { get; set; }
		public bool IsModelPortfolio { get; set; }
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int TxnSequenceId { get; set; }
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
		public string ModelportfolioId { get; set; }
		public virtual ModelPortfolio ModelPortfolio { get; set; }
	}
}
