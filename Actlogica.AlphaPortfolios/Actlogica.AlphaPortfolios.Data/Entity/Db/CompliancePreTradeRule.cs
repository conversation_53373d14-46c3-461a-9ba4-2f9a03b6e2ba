﻿

using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.OData.UriParser;
using System;
using System.Runtime.ConstrainedExecution;
using Actlogica.AlphaPortfolios.ApiContracts.TradeCompliance;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class CompliancePreTradeRule : BaseTableEntity
	{
		[StringLength(200), Column(TypeName = "varchar")]
		public string RuleTypeId { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string RuleSubTypeId { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public PreTradeLevels Level { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string LevelValue { get; set; }

		[StringLength(100), Column(TypeName = "varchar")]
		public string ValueType { get; set; }

		[StringLength(200), Column(TypeName = "varchar")]
		public string Value { get; set; }


		[StringLength(200), Column(TypeName = "varchar")]
		public string RuleSubTypeValueExplicit { get; set; }

		public bool IsActive { get; set; }
	}
}
