﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class FundManagementChargeStructure : BaseTableEntity
	{
		public double FixedAmount { get; set; }
		public double FixedPercentage { get; set; }
		public double HurdleRate { get; set; }
		public double ProfitSharePercentage { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string ChargeFrequency { get; set; }
		public string ChargeTypeId { get; set; }
		public FundManagementChargeType ChargeType { get; set; }
	}
}
