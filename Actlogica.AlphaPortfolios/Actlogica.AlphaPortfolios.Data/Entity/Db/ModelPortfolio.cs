﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ModelPortfolio : BaseTableEntity
	{
		public string ModelId { get; set; }
		public Model Model { get; set; }
		[StringLength(150), Column(TypeName = "varchar")]
		public string Name { get; set; }
		public DateTime StartDate { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double Dividends { get; set; }
		public double MarketValue { get; set; }
		public double AnnualReturnIrr { get; set; }
		public double AnnualReturnIrrUnrealised { get; set; }
		public double TwrrSinceInception { get; set; }
		public double AnnualPerformanceTwrr { get; set; }
		public double CurrentCashBalance { get; set; }
		public virtual ICollection<Investment> Investments { get; set; }
	}
}
