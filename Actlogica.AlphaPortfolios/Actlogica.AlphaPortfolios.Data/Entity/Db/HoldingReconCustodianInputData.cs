﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
  public class HoldingReconCustodianInputData : BaseTableEntity
  {
    public DateTime HoldingDate { get; set; }
    [StringLength(20), Column(TypeName = "varchar")]
    public string Code { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string Name { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Assetclass { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Symbolcode { get; set; }
    [StringLength(100), Column(TypeName = "varchar")]
    public string Symbolname { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Isin { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Position { get; set; }
    public double Quantity { get; set; }
    public double Unitcost { get; set; }
    public double Totalcost { get; set; }
    public double Unitprice { get; set; }
    public double Accruedincome { get; set; }
    public double Marketvalue { get; set; }
    public double AssetsPercentage { get; set; }
    public double Totalportfoliovalue { get; set; }
    public double Gainloss { get; set; }
    public double GainlossPercentage { get; set; }
    [StringLength(200), Column(TypeName = "varchar")]
    public string Custody { get; set; }
    [StringLength(200), Column(TypeName = "varchar")]
    public string Sectordescription { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Dimension { get; set; }
    public double Sharecapital { get; set; }
    public double Facevalue { get; set; }
    public double Market { get; set; }
    public double HoldingasPercentageofmarketcapital { get; set; }
    public double Receivable { get; set; }
    public double Payable { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Parentsymbol { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Optiontype { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string Series { get; set; }
    public DateTime Expirydate { get; set; }
    public double Strikeprice { get; set; }
    [StringLength(50), Column(TypeName = "varchar")]
    public string HoldingReconProcessId { get; set; }
    }
}
