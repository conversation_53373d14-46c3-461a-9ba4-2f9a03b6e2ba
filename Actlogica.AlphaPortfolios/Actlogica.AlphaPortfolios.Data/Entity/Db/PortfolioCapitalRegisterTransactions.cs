﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class PortfolioCapitalRegisterTransactions : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string TransactionType { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string TransactionSubType { get; set; }
		public double Amount { get; set; }
		public string Description { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime? SettlementDate { get; set; }
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
		public string Status { get; set; }
    }
}
