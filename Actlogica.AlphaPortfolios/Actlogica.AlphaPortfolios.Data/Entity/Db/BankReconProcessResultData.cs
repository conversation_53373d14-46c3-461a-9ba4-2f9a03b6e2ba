﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class BankReconProcessResultData : BaseTableEntity
	{
		public DateTime PortfolioCashLedgerDate { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime PostingDate { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string TransactionTypeBank { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string TransactionTypeLedger { get; set; }
		public bool FoundInBank { get; set; }
		public bool FoundInCashLedger { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string BankAccountNumber { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string BankBranch { get; set; }
		public double BankInputAmount { get; set; }
		public double PortfolioCashLedgerAmount { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string Description { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string MatchStatus { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientCode { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string ClientName { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BankReconProcessId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BankReconTransactionProcessInputDataId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string BankReconBalanceProcessInputDataId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioCashLedgerId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string PortfolioId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyCode { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string StrategyModelId { get; set; }
	}
}
