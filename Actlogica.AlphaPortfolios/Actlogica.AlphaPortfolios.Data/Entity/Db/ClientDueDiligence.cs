﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientDueDiligence : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string AnnualIncome { get; set; }
		public double NetworthInRs { get; set; }
		public double NetworthAsOnDate { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string PepRPep { get; set; }
		public bool PEPDeclarationReceived { get; set; }
		public bool AMLCheckDone { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AMLCheckResult { get; set; }
		public DateTime AMLDoneDate { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string AMLReviewPeriodicity { get; set; }
		public DateTime NextAMLReviewDate { get; set; }
		public bool RiskProfilingDone { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string ClientRiskCategory { get; set; }
		public bool AdditionalDueDiligenceRequired { get; set; }
		[StringLength(250), Column(TypeName = "varchar")]
		public string AdditionalDueDiligenceStatus { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
	}
}
