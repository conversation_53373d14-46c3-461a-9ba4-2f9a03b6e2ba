﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
    public class Strategy : BaseTableEntity
    {
        [StringLength(20), Column(TypeName = "varchar")]
        public string StrategyCode { get; set; }
        [StringLength(200), Column(TypeName = "varchar")]
        public string Name { get; set; }
        public DateTime StartDate { get; set; }
        public bool IsOpen { get; set; }
        public bool IsMfDemat { get; set; }

        [StringLength(50), Column(TypeName = "varchar")]
        public string StrategyBankId { get; set; }
        public virtual StrategyBank StrategyBank { get; set; }
        public virtual IEnumerable<StrategyBroker> StrategyBrokers { get; set; }
        public virtual IEnumerable<StrategyCustodian> StrategyCustodians { get; set; }
        public virtual IEnumerable<StrategyUser> StrategyUsers { get; set; }

		    [StringLength(50), Column(TypeName = "varchar")]
		    public string PortfolioManagerMasterId { get; set; }
	}
}
