﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ModelSecurity : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string Isin { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string Name { get; set; }
		[StringLength(200), Column(TypeName = "varchar")]
		public string CompName { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Symbol { get; set; }
		[StringLength(50), Column(TypeName = "varchar")]
		public string Scripcode { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string Industry { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string SchemeName { get; set; }
		[StringLength(20), Column(TypeName = "varchar")]
		public string AmfiCode { get; set; }
		public string FundClass { get; set; }
		[StringLength(10), Column(TypeName = "varchar")]
		public string Exchange { get; set; }
		public double Weight { get; set; }
		public bool IsMutualFund { get; set; }
		//public int InstrumentType { get; set; }

		public string ModelId { get; set; }
		public Model Model { get; set; }
	}
}
