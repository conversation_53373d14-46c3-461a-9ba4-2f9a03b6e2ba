﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Data.Entity.Db
{
	public class ClientIncorporationDetail : BaseTableEntity
	{
		[StringLength(50), Column(TypeName = "varchar")]
		public string ClientId { get; set; }
		public DateTime DateOfIncorporation { get; set; }
		public DateTime DateOfCommencementOfBusiness { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string CityOfIncorporation { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string CountryOfIncorporation { get; set; }
		[StringLength(100), Column(TypeName = "varchar")]
		public string TINOfIssuingCountry { get; set; }
		public DateTime ValidFrom { get; set; }
		public DateTime ValidTo { get; set; }
	}
}
