﻿using Actlogica.AlphaPortfolios.Utils.Dates;
using Microsoft.Azure.Cosmos.Table;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Storage.PerformanceEngine.ModelPortfolio
{
	public class ModelPortfolioMarketValueDaily : TableEntity
	{
		public ModelPortfolioMarketValueDaily(string modelPortfolioId, DateTime asAtDate)
		{
			PartitionKey = modelPortfolioId;
			RowKey = asAtDate.ExtractFinFloDateBasedRowKey();
		}

		public ModelPortfolioMarketValueDaily()
		{

		}

		public DateTime AsAtDate { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double IrrSinceInception { get; set; }
		public double IrrCurrent { get; set; }
		public double TwrrSinceInception { get; set; }
		public string BenchmarkName { get; set; }
		public double BenchmarkIrrSinceInception { get; set; }
		public double BenchmarkIrrCurrent { get; set; }
		public double BenchmarkTwrrSinceInception { get; set; }
	}
}
