﻿using Actlogica.AlphaPortfolios.Utils.Dates;
using Microsoft.Azure.Cosmos.Table;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Storage.PerformanceEngine.Investment
{
	public class DirectEquityInvestmentMarketValueDaily : TableEntity
	{
		public DirectEquityInvestmentMarketValueDaily(string investmentId, DateTime asAtDate)
		{
			PartitionKey = investmentId;
			RowKey = asAtDate.ExtractFinFloDateBasedRowKey();
		}

		public DirectEquityInvestmentMarketValueDaily()
		{

		}

		public string InvestmentId { get; set; }
		public string PortfolioId { get; set; }
		public bool IsModelPortfolio { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double IrrSinceInception { get; set; }
		public double IrrCurrent { get; set; }
		public string BenchmarkName { get; set; }
		public double BenchmarkIrrSinceInception { get; set; }
		public double BenchmarkIrrCurrent { get; set; }
	}
}
