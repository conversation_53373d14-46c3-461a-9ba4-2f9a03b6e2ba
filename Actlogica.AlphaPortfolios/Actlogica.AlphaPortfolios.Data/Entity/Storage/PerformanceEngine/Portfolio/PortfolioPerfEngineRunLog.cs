﻿using Actlogica.AlphaPortfolios.Utils.Dates;
using Microsoft.Azure.Cosmos.Table;
using System;

namespace Actlogica.AlphaPortfolios.Data.Entity.Storage.PerformanceEngine.Portfolio
{
	public class PortfolioPerfEngineRunLog : TableEntity
	{
		public PortfolioPerfEngineRunLog(DateTime asAtDate, string portfolioId)
		{
			PartitionKey = $"{asAtDate.ExtractFinFloDateBasedRowKey()}";
			RowKey = $"{portfolioId}";
			AsAtDate = asAtDate;
			PortfolioId = portfolioId;
		}

		public PortfolioPerfEngineRunLog()
		{

		}

		public string PortfolioId { get; set; }
		public string ClientName { get; set; }
		public string ClientStrategyCode { get; set; }
		public DateTime AsAtDate { get; set; }
		public string Status { get; set; }
		public string Remarks { get; set; }
	}
}
