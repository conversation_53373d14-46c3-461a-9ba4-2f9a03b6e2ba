﻿using Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation;
using Actlogica.AlphaPortfolios.ApiContracts.CorporateActions;
using Actlogica.AlphaPortfolios.ApiContracts.DirectEquity;
using Actlogica.AlphaPortfolios.ApiContracts.HoldingRecon;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Entity.Storage;
using AutoMapper;

namespace Actlogica.AlphaPortfolios.Data.Configs
{
	public class DataMappingProfiles : Profile
	{
		public DataMappingProfiles()
		{
			CreateMap<EquityCorporateActionDaily, EquityCorporateAction>().ReverseMap();
			CreateMap<EquityHistory, DirectEquityPrice>();
			CreateMap<Entity.Db.Client, ApiContracts.Clients.Client>()
				.ForMember(x=>x.DisplayName,y=>y.MapFrom(s=> string.Format("{0}, {1}{2}",s.<PERSON>,s.<PERSON>,s.<PERSON>ame)))
				.ReverseMap();
            CreateMap<BankReconTransactionProcessInputData, BRProcessInputData>().ReverseMap();
            CreateMap<BankReconBalanceProcessInputData, BRBankProcessInputData>().ReverseMap();
            CreateMap<BankReconBalanceProcessInputData, PortfolioCashLedger>().ReverseMap();
            CreateMap<BRAdjustmentResult, PortfolioCashLedger>().ReverseMap();
            CreateMap<BankReconProcessResultData, BankReconProcess>().ReverseMap();

            CreateMap<HoldingReconCustodianInputData, HRCustProcessInputData>().ReverseMap();
            CreateMap<HoldingReconInputFile, HoldingReconInputFile>().ReverseMap();
            CreateMap<HoldingReconProcess, HRProcessedRecon>().ReverseMap();



        }
    }
}
