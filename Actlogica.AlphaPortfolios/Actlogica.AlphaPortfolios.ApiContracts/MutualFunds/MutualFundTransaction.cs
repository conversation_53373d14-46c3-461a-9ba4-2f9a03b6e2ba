﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.MutualFunds
{
	public class MutualFundTransaction
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string InvestmentId { get; set; }
		public string PortfolioId { get; set; }
		public Portfolio Portfolio { get; set; }
		public string ClientId { get; set; }
		public Client Client { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public double Quantity { get; set; }
		public double UnrealisedHolding { get; set; }
		public double CurrentHolding { get; set; }
		public double Price { get; set; }
		public double Amount { get; set; }
		public double Brokerage { get; set; }
		public double ServiceTax { get; set; }
		public double SttAmount { get; set; }
		public double TurnTax { get; set; }
		public double OtherTax { get; set; }
		public TransactionType Type { get; set; }
		public TransactionSubType SubType { get; set; }
		public string Symbol { get; set; }
		public string Exchange { get; set; }
		public string Isin { get; set; }
		public string ClientOrderEntryId { get; set; }
	}
}
