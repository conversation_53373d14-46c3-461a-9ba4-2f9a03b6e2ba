﻿using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Transformer
{
	public class TransformerProjectConf
	{
		public long ProjectId { get; set; }
		public string? ProjectName { get; set; }
		public Guid? DestinationFileId { get; set; }
		public string? Status { get; set; }
		public string? SourceSystem { get; set; }
		public string? DestinationSystem { get; set; }
		public string? DestDateFormat { get; set; }
		public List<TransformerFileConfiguration> FileList { get; set; }
	}
}
