﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Transformer
{
	public class AlphaTransformerIntegration
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public int TransformerProjectCode { get; set; }
		public string TransformerProjectName { get; set; }
		public string FromName { get; set; }
		public string ToName { get; set; }
		public string FileType { get; set; }
		public string TransformationDescription { get; set; }
		public string FromRefId { get; set; }
		public string ToRefId { get; set; }
	}
}
