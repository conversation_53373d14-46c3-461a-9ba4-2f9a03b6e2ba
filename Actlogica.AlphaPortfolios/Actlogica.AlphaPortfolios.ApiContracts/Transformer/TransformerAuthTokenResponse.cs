﻿using Microsoft.Azure.Documents;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Transformer
{
	public class TransformerAuthTokenResponse
	{
		public string? unique_id { get; set; } = string.Empty;
		public string? name { get; set; } = string.Empty;
		public string? roles { get; set; } = string.Empty;
		public string Token { get; set; } = string.Empty;
		public string RefreshToken { get; set; } = string.Empty;
		public DateTime TokenExpires { get; set; }
		public long id { get; set; }
		public Permission? permission { get; set; }
	}

	public class TransformerLoginResponse
	{
		public bool Status { get; set; }
		public List<string> Errors { get; set; }
		public string Email { get; set; }
		public string HashedOTP { get; set; }
	}
}
