﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ApiContracts.TransactionDrafts
{
    public class CapitalRegisterTransactions
    {
        public string Id { get; set; }
        public string PortfolioId { get; set; }
        public double Amount { get; set; }
        public TransactionStatus Status { get; set; }
        public TransactionType TransactionType { get; set; }
        public string TransactionSubType { get; set; }
        public string Source { get; set; }
        public DateTime TransactionDate { get; set; }
        public DateTime? SettlementDate { get; set; }
        public string Description { get; set; }
    }
}
