﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.UserManagement
{
	public class ActlogicaUser
	{
		public ActlogicaUser()
		{
			UserRoles = new List<ActlogicaUserRole>();
			RoleId = new List<int>();
			AppName = new List<string>();
		}
		public int Id { get; set; }
		[Required]
		public string Username { get; set; }
		[Required]
		[EmailAddress(ErrorMessage = "Invalid Email Address")]
		public string Email { get; set; }
		[Required]
		public string FirstName { get; set; }
		[Required]
		public string LastName { get; set; }

		[Required]
		public DateTime Dob { get; set; } = DateTime.Now.AddYears(-18);
		[Required]
		public string Password { get; set; }
		[Required]
		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Phone Number")]
		public string Mobile { get; set; }
		[Required]
		[RegularExpression("^([A-Za-z]){5}([0-9]){4}([A-Za-z]){1}$", ErrorMessage = "Invalid PAN Number")]
		public string Pan { get; set; }
		public List<int> RoleId { get; set; }


		public int OrganisationId { get; set; }

		public DateTime LastLoginDate { get; set; }
		public DateTime CreatedDate { get; set; }

		public bool IsVerified { get; set; }
		public bool IsLocked { get; set; }
		public string ActivationCode { get; set; }
		public string ProviderId { get; set; }
		public string ProviderName { get; set; }

		public int OrgBranchId { get; set; }

		public string Ucc { get; set; }
		public string Euin { get; set; }


		public List<ActlogicaUserRole> UserRoles { get; set; }
		public int AdminId { get; set; }

		//added this column to insert new UserClaim
		public List<string> AppName { get; set; }
	}
}
