using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.IncomeExpense
{
    public class IncomeExpenseData
    {
        public string ClientName { get; set; }
        public string PortfolioName { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public DateTime TransactionDate { get; set; }
        public DateTime SettlementDate { get; set; }
        public TransactionType TransactionType { get; set; }
        public TransactionSubType TransactionSubType { get; set; }
        public double Amount { get; set; }
        public double RunningBalance { get; set; }
        public string Description { get; set; }
        public string TxnRefId { get; set; }
        public int TxnSequenceId { get; set; }
        public bool IsModelPortfolio { get; set; }
        public string PortfolioId { get; set; }
        public string ModelPortfolioId { get; set; }
    }
}
