using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.ReceivablePayables
{
    public class BookPayableReceivable
    {
        public string Id { get; set; }
        public TransactionType TransactionType { get; set; }
        public TransactionSubType TransactionSubType { get; set; }
        public DateTime SettlementDate { get; set; }
        public double Amount { get; set; }
        public double Tds { get; set; }
        public string Remarks { get; set; }
    }
}
