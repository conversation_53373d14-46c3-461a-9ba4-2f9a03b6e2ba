using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.ReceivablePayables
{
    public class ReceivablePayablePayload
    {
        public string Id { get; set; }
        public TransactionSubType TransactionSubType { get; set; }
        public string ClientName { get; set; }
        public string ClientStrategyCode { get; set; }
        public string InvestmentName { get; set; }
        public string PortfolioName { get; set; }
        public DateTime TransactionDate { get; set; }
        public DateTime SettlementDate { get; set; }
        public double Rate { get; set; }
        public double Quantity  { get; set; }
        public double Amount { get; set; }
        public string Status { get; set; }
        public string Remarks { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}
