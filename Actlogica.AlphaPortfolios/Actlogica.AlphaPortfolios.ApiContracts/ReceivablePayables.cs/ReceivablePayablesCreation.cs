using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.ReceivablePayables
{
    public class ReceivablePayablesCreation
    {
        public string PortfolioId { get; set; }
        public TransactionType TransactionType { get; set; }
        public TransactionSubType TransactionSubType { get; set; }
        public DateTime TransactionDate { get; set; }
        public double Amount { get; set; }
        // public string Remarks { get; set; }
    }
}
