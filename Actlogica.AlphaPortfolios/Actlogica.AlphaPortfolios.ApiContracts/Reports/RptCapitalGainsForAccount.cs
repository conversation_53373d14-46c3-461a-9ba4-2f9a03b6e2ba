﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
  public class RptCapitalGainsForAccount :BaseReportDataPayload
  {
    public List<ClientCapitalGainsContract> CapitalGainsFromInvestments { get; set; }
    public List<InvestmentTransaction> DividendTransactions { get; set; }
    public List<PortfolioCashLedger> InterestIncome { get; set; }
  }
}
