using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class RptCorporateBenefit : BaseReportDataPayload
    {
        public RptCorporateBenefitTotal Total { get; set; }
        public List<RptCorporateBenefitTransaction> Transactions { get; set; }
    }

    public class RptCorporateBenefitTotal
    {
        public string TotalSettlementAmount { get; set; }
        public double TotalSettlementAmountDouble { get; set; }
    }

    public class RptCorporateBenefitTransaction
    {
        public string Name { get; set; }
        public string ISIN { get; set; }
        public string ExDate { get; set; }
        public string CorporateActionType { get; set; }
        public string Entitlement { get; set; }
        public string Quantity { get; set; }
        public double QuantityDouble { get; set; }
        public string Amount { get; set; }
        public double AmountDouble { get; set; }


    }
}
