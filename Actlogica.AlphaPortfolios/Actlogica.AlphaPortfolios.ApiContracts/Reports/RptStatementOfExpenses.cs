using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptStatementOfExpenses : BaseReportDataPayload
	{
		public RptStatementOfExpensesTotal Total { get; set; }
		public List<RptStatementOfExpensesTransaction> Transactions { get; set; }
	}

	public class RptStatementOfExpensesTotal
	{
		public string TotalAmount { get; set; }
		public double TotalAmountDouble { get; set; }
	}
	public class RptStatementOfExpensesTransaction
	{
        public string Date { get; set; }
        public string SettlementDate { get; set; }
        public string Detail { get; set; }
        public string Description { get; set; }
        public string Amount { get; set; }
        public double AmountDouble { get; set; }
	}
}
