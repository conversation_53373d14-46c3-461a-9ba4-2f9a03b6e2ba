
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using System.Text.Json.Serialization;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class ReportsCronConfig
    {
        public string Id;
        public DateTime RequestDate { get; set; }

        public string RequestedBy { get; set; }

        [JsonConverter(typeof(ReportType))]
        public ReportType ReportType { get; set; }

        [JsonConverter(typeof(ReportFrequency))]
        public ReportFrequency Frequency { get; set; }
        public ReportsFormat ReportFormat { get; set; }
        public string PortfolioId { get; set; }
        public string ClientId { get; set; }
        public DateTime Time { get; set; }

        [JsonConverter(typeof(ScheduledReportStatus))]
        public ScheduledReportStatus Status { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime LastUpdatedDate { get; set; }
    }
}
