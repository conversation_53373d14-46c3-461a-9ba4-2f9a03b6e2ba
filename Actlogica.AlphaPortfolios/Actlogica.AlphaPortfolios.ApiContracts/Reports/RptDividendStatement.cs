﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptDividendStatement : BaseReportDataPayload
	{
		public RptDividendStatementTotal Total { get; set; }
		public List<RptDividendStatementTransaction> Transactions { get; set; }
	}
	public class RptDividendStatementTotal
	{
		public string TotalReceivableAmount { get; set; }
		public double TotalReceivableAmountDouble { get; set; }
		public string TotalReceivedAmount { get; set; }
		public double TotalReceivedAmountDouble { get; set; }
		public string TotalNetAmount { get; set; }
		public double TotalNetAmountDouble { get; set; }
	}
	public class RptDividendStatementTransaction
	{
		public string ExDate { get; set; }
		public string RecievedDate { get; set; }
		public string Security { get; set; }
		public double Quantity { get; set; }
		public string Rate { get; set; }
		public string RecievableAmount { get; set; }
		public string RecievedAmount { get; set; }
		public string NetAmount { get; set; }
		public string TDSAmount { get; set; }
		public double AmountDouble { get; set; }
		public double RecievableAmountDouble { get; set; }
		public double RecievedAmountDouble { get; set; }
		public double NetAmountDouble { get; set; }
		public double TDSAmountDouble { get; set; }
	}
}
