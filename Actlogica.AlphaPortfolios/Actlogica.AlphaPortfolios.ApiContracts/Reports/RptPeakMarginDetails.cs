using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class RptPeakMarginDetails
    {
        public string StrategyCustodyCode { get; set; }
        public string CPCode { get; set; }
        public string BankAccountNo { get; set; }
        public string NonPISAccount { get; set; }
        public string PISAccount { get; set; }
        public string DebitOrCredit { get; set; }
        public double TradeAmount { get; set; }
        public double PeakMarginAmount { get; set; }
        public double PeakMarginPct { get; set; }
        public string Narration;
        public DateTime TradeDate { get; set; }
        public DateTime ValueDate { get; set; }
        public string MarginType;

    }
}
