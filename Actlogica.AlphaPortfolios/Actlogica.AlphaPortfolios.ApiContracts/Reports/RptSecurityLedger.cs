using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class RptSecurityLedger : BaseReportDataPayload
    {
        public string AccountNumber {get; set;}
        public string ClientStrategyCode {get; set;}
        public string DpId {get; set;}
        public List<RptSecurityLedgerOverview> Securities { get; set; }
    }
    public class RptSecurityLedgerOverview
    {
        public SecurityDetails Details { get; set; }
        public List<RptSecurityLedgerTransactions> Transactions { get; set; }
        public RptSecurityLedgerTotal Total { get; set; }

    }
    public class SecurityDetails
    {
        public string Symbol { get; set; }
        public string SecurityName { get; set; }
        public string SecurityType { get; set; }
        public string CustodianFolioCode { get; set; }
    }

    public class RptSecurityLedgerTransactions
    {
        public string TxnDesc { get; set; }
        public string TxnDate { get; set; }
        public string SetDate { get; set; }
        public string DaysHeld { get; set; }
        public string Quantity { get; set; }
        public double QuantityDouble { get; set; }
        public string UnitPrice { get; set; }
        public double UnitPriceDouble { get; set; }
        public string Amount { get; set; }
        public double AmountDouble { get; set; }
        public string CumulativeQuantity { get; set; }
        public double CumulativeQuantityDouble { get; set; }
        public string PurchaseQuantity { get; set; }
        public double PurchaseQuantityDouble { get; set; }
        public string UnitCost { get; set; }
        public double UnitCostDouble { get; set; }
        public string PurchaseAmount { get; set; }
        public double PurchaseAmountDouble { get; set; }
        public string PurchaseDate { get; set; }
        public string GainLoss { get; set; }
        public double GainLossDouble { get; set; }
        public string STLT { get; set; }

    }


    public class RptSecurityLedgerTotal
    {
        public string TotalQuantity { get; set; }
        public double TotalQuantityDouble { get; set; }
        public string TotalUnitPrice { get; set; }
        public double TotalUnitPriceDouble { get; set; }
        public string TotalAmount { get; set; }
        public double TotalAmountDouble { get; set; }
        public string TotalGainLoss { get; set; }
        public double TotalGainLossDouble { get; set; }
    }
}
