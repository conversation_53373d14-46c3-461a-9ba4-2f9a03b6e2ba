using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptModelPortfolioHoldingsLatestDetailsForMis
	{
		public string ModelName { get; set; }
		public string Isin { get; set; }
		public string InvestmentName { get; set; }
		public double Holding { get; set; }
		public double AvgPrice { get; set; }
		public double Cost { get; set; }
		public double Rate { get; set; }
		public double InvestmentMv { get; set; }
		public double ModelCash { get; set; }
		public string  AsAtDate {get; set; }
	}
}
