﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptCapitalRegisterDetailsForMis
	{
		public string PortfolioCashLedgerId { get; set; }
		public string ClientCode { get; set; }
		public string ClientName { get; set; }
		public string StrategyName { get; set; }
		public string ClientStrategyCode { get; set; }
		public string CustodianPortfolioCode { get; set; }
		public string FAAccountNo { get; set; }
		public string TransactionDate { get; set; }
		public string TransactionType { get; set; }
		public string TransactionSubType { get; set; }
		public double Amount { get; set; }
		public string AsAtDate { get; set; }
		public string Description { get; set; }
	}
}
