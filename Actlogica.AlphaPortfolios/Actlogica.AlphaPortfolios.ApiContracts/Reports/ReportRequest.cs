﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Reports.Payloads;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class ReportRequest : BaseRequestPayload
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
		public DateTime LastUpdatedDate { get; set; } = DateTime.UtcNow;
		public DateTime RequestDate { get; set; } = DateTime.UtcNow;
		public string RequestedBy { get; set; } = "NA";
		public string ReportType { get; set; }
		public string RequestPayload { get; set; }
		public string Status { get; set; } = Enum.GetName(ReportStatus.Submitted);
		public string ReportPath { get; set; }
	}
}
