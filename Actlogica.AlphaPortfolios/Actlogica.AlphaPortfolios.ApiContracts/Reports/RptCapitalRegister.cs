﻿using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptCapitalRegister : BaseReportDataPayload
	{
		public RptCapitalRegisterTotals Total { get; set; }
		public List<RptCapitalRegisterTransaction> Transactions { get; set; }
	}

	public class RptCapitalRegisterTotals
	{
		public string CreditTotal { get; set; }
		public double CreditTotalDouble { get; set; }
		public string DebitTotal { get; set; }
		public double DebitTotalDouble { get; set; }
	}

	public class RptCapitalRegisterTransaction
	{
		public string TransactionDate { get; set; }
		public string SettlementDate { get; set; }
		public string TransactionDescription { get; set; }
		public string Credit { get; set; }
		public double CreditDouble { get; set; }
		public string Debit { get; set; }
		public double DebitDouble { get; set; }
		public string Balance { get; set; }
		public double BalanceDouble { get; set; }
		public string Remarks { get; set; }
	}
}
