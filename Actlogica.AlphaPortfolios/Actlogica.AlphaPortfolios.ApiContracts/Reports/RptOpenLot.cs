using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptOpenLot : BaseReportDataPayload
	{
		public RptOpenLotTotal Total { get; set; }
		public List<RptOpenLotTransaction> Holdings { get; set; }
	}

	public class RptOpenLotTotal
	{
		public string TotalCost { get; set; }
		public double TotalCostDouble { get; set; }
		public string TotalMarketValue { get; set; }
		public double TotalMarketValueDouble { get; set; }
		public string TotalGainLoss { get; set; }
		public double TotalGainLossDouble { get; set; }
	}
	public class RptOpenLotTransaction
	{
		public string Name { get; set; }
		public string ISIN { get; set; }
		public string BuyDate { get; set; }
		public string Quantity { get; set; }
		public double QuantityDouble { get; set; }
		public string UnrealisedHolding { get; set; }
		public string UnitCost { get; set; }
		public double UnitCostDouble { get; set; }
		public string TotalCost { get; set; }
		public double TotalCostDouble { get; set; }
		public string Price { get; set; }
		public double PriceDouble { get; set; }
		public string MarketValue { get; set; }
		public double MarketValueDouble { get; set; }
		public string URGainLoss { get; set; }
		public double URGainLossDouble { get; set; }
		public int NoOfDays { get; set; }
		public object TransactionDate { get; set; }
		public object SettlementDate { get; set; }
	}

	public class OpenLotTransaction
	{
		public string Name { get; set; }
		public string ISIN { get; set; }
		public DateTime BuyDate { get; set; }
		public double Quantity { get; set; }
		public double UnitCost { get; set; }
		public double TotalCost { get; set; }
		public double Price { get; set; }
		public double MarketValue { get; set; }
		public double URGainLoss { get; set; }
		public int NoOfDays { get; set; }
	}
}
