﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptHoldingDetailsForMis
	{
		public string PortfolioId { get; set; }
		public string InvestmentId { get; set; }
		public string ClientName { get; set; }
		public string StrategyName { get; set; }
		public string ClientStrategyCode { get; set; }
		public string CustodianPortfolioCode { get; set; }
		public string FAAccountNo { get; set; }
		public string Isin { get; set; }
		public string HoldingName { get; set; }
		public string Symbol { get; set; }
		public double UnrealisedQty { get; set; }
		public double TotalCost { get; set; }
		public double MarketValue { get; set; }
		public string AveragePrice { get; set; }
		public string Price { get; set; }
		public double AccruedIncome { get; set; }
		public double Receivable { get; set; }
		public double Payable { get; set; }
		public string AsAtDate { get; set; }
		public SecurityType SecurityType{ get; set; }
		public SecuritySubType SecuritySubType{ get; set; }
		public string AssetClass { get; set; }
	}
}
