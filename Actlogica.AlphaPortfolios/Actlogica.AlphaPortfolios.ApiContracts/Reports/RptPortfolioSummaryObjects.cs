using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class RptPortfolioSummaryObjects : BaseReportDataPayload
    {
        public RptPortfolioSummaryTransaction Transactions { get; set; }
        public string ClientStrategyCode { get; set; }
        public string AccountNumber { get; set; }
        public string FromDate { get; set; }
        public string ToDate { get; set; }
    }

    public class RptPortfolioSummaryTransaction
    {
        public string MarketValueFromDate { get; set; }
        public string CapitalInOut { get; set; }
        public string RealizedGain { get; set; }
        public string UnrealizedGain { get; set; }
        public string GainPriorToTakeOver { get; set; }
        public string Income { get; set; }
        public string Fees { get; set; }
        public string Expenses { get; set; }
        public string AccruedIncome { get; set; }
        public string MarketValueToDate { get; set; }
        public string AbsolutePortfolioRateOfReturn { get; set; }
        public string AbsoluteBenchmarkOne { get; set; }
        public string AbsoluteBenchmarkTwo { get; set; }
        public string AnnualizedPortfolioRateOfReturn { get; set; }
        public string AnnualizedBenchmarkOne { get; set; }
        public string AnnualizedBenchmarkTwo { get; set; }
        public string CashFlowBeginOfDay { get; set; }
    }

    public class InvestmentEntry
    {
        public double Realised { get; set; }
        public double CapitalInOut { get; set; }
        public double Unrealised { get; set; }
        public double Market_value { get; set; }
        public double Invested_capital { get; set; }
        public DateTime Date { get; set; }
        public double GainPriorToTakeOver { get; set; }
        public double Income { get; set; }
        public double Fees { get; set; }
        public double Expenses { get; set; }
        public double AccruedIncome { get; set; }
        public double MarketValueToDate { get; set; }
        public double AbsolutePortfolioRateOfReturn { get; set; }
        public double AbsoluteBenchmarkOne { get; set; }
        public double AbsoluteBenchmarkTwo { get; set; }
        public double AnnualizedPortfolioRateOfReturn { get; set; }
        public double AnnualizedBenchmarkOne { get; set; }
        public double AnnualizedBenchmarkTwo { get; set; }
        public double CashFlowBeginOfDay { get; set; }
    }

}
