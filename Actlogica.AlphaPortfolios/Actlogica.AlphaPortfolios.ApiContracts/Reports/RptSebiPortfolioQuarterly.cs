﻿using System.Collections.Generic;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
  public class RptAccountOverview
  {
    public string ClientName { get; set; }
    public string Pan { get; set; }
    public string Address { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public string UniqueClientCode { get; set; }
    public string AccountActivationDate { get; set; }
    public string PMSType { get; set; }
    public string InvestmentApproach { get; set; }
    public string PortfolioBenchmark { get; set; }
    public string InvestedCapital { get; set; }
    public string AUM { get; set; }
    public string ReportingPeriod { get; set; }
  }

  public class RptAggregateOfInvestmentApproach
  {
    public RptPeriodicReturnPoints ReturnPoints { get; set; }
  }

  public class RptAssetTypeAllocation
  {
    public string Type { get; set; }
    public string Cost { get; set; }
    public string MarketValue { get; set; }
    public string Weight { get; set; }
    public double CostDouble { get; set; }
    public double MarketValueDouble { get; set; }
    public double WeightDouble { get; set; }
  }

  public class RptBenchmark
  {
    public RptPeriodicReturnPoints ReturnPoints { get; set; }
  }

  public class RptCapitalRegisterSinceInception
  {
    public string Date { get; set; }
    public string Inflow { get; set; }
    public string Outflow { get; set; }
  }

  public class RptCash
  {
    public HoldingDetailsForReport CashHolding { get; set; }
  }

  public class RptCommodity
  {
    public HoldingDetailsForReport CommodityHolding { get; set; }
  }

  public class RptDebt
  {
    public HoldingDetailsForReport DebtHolding { get; set; }
  }

  public class RptEquity
  {
    public HoldingDetailsForReport EquityHolding { get; set; }
  }

  public class RptHoldingAsAtReportingDate
  {
    public List<RptEquity> Equity { get; set; }
    public List<RptDebt> Debt { get; set; }
    public List<RptMutualFund> MutualFunds { get; set; }
    public List<RptCommodity> Commodities { get; set; }
    public List<RptOtherAsset> OtherAssets { get; set; }
    public List<RptCash> Cash { get; set; }
    public RptTotal Total { get; set; }
  }

  public class RptMutualFund
  {
    public HoldingDetailsForReport MfHolding { get; set; }
  }

  public class RptOtherAsset
  {
    public HoldingDetailsForReport OtherAssetHolding { get; set; }
  }

  public class RptPortfolio
  {
    public RptPeriodicReturnPoints ReturnPoints { get; set; }
  }

  public class RptPortfolioDetails
  {
    public string ClientStrategyCode { get; set; }
    public List<RptAssetTypeAllocation> AssetTypeAllocation { get; set; }
    public RptSebiPortfolioSummary PortfolioSummary { get; set; }
    public RptPortfolioPerformance PortfolioPerformance { get; set; }
    public List<RptCapitalRegisterSinceInception> CapitalRegisterSinceInception { get; set; }
    public RptCapitalRegisterSinceInception TotalCapitalRegister { get; set; }
    public List<RptReportingPeriodInvestmentTransaction> ReportingPeriodInvestmentTransactions { get; set; }
    public RptHoldingAsAtReportingDate HoldingAsAtReportingDate { get; set; }
    public object ReportHoldings { get; set; }
    public ClientPortfolioPerformance ClientPortfolioPerformance { get; set; }
    public BenchmarkPerformance BenchmarkPerformance { get; set; }
  }

  public class RptPortfolioPerformance
  {
    public RptPortfolio Portfolio { get; set; }
    public RptAggregateOfInvestmentApproach AggregateOfInvestmentApproach { get; set; }
    public RptBenchmark Benchmark { get; set; }
  }

  public class RptSebiPortfolioSummary
  {
    public string MarketValueBeginning { get; set; }
    public string MarketValueEnding { get; set; }
    public string InvestedCapital { get; set; }
    public string Withdrawals { get; set; }
    public string Interest { get; set; }
    public string DividendIncome { get; set; }
    public string OtherIncome { get; set; }
    public string ManagementFee { get; set; }
    public string PerformanceFee { get; set; }
    public string Expenses { get; set; }
    public string OtherExpenses { get; set; }
    public string RealisedGainLoss { get; set; }
    public string UnrealisedGainLoss { get; set; }
    public string CommissionsPaid { get; set; }
    public string BrokeragePaid { get; set; }
  }

  public class RptReportingPeriodInvestmentTransaction
  {
    public string Name { get; set; }
    public string TransactionDate { get; set; }
    public string BuySell { get; set; }
    public string Quantity { get; set; }
    public string GrossRate { get; set; }
    public string NetRate { get; set; }
    public string NetAmount { get; set; }
  }

  public class RptSebiPortfolioQuarterly
  {
    public string Logo { get; set; }
    public RptAccountOverview AccountOverview { get; set; }
    public RptPortfolioDetails PortfolioDetails { get; set; }
  }

  public class RptTotal
  {
    public HoldingDetailsForReport HoldingTotal { get; set; }
  }

  public class RptPeriodicReturnPoints
  {
    public string OneYear { get; set; }
    public string ThreeYear { get; set; }
    public string FiveYear { get; set; }
    public string TenYear { get; set; }
    public string SI { get; set; }
  }
  public class ClientPortfolioPerformance
  {
    public string OneMonth { get; set; }
    public string ThreeMonth { get; set; }
    public string SixMonth { get; set; }
    public string OneYear { get; set; }
    public string ThreeYear { get; set; }
    public string FiveYear { get; set; }
    public string SinceInception { get; set; }
  }
  public class BenchmarkPerformance
  {
    public string OneMonth { get; set; }
    public string ThreeMonth { get; set; }
    public string SixMonth { get; set; }
    public string OneYear { get; set; }
    public string ThreeYear { get; set; }
    public string FiveYear { get; set; }
    public string SinceInception { get; set; }

  }
}
