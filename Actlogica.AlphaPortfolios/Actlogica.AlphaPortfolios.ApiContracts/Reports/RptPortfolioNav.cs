using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptPortfolioNav : BaseReportDataPayload
	{
		public RptPortfolioNavTotal Total { get; set; }
		public List<RptPortfolioNavTransaction> Transactions { get; set; }
	}

	public class RptPortfolioNavTotal
	{
		public double TotalCashFlow { get; set; }
		public double TotalCashDouble { get; set; }
		public double LastNAV { get; set; }
	}
	public class RptPortfolioNavTransaction
	{
        public string Date { get; set; }
        public string CorporateActionType { get; set; }
        public string Entitlement { get; set; }
        public string OpeningBalance { get; set; }
        public double OpeningBalanceDouble { get; set; }
        public string ClosingBalance { get; set; }
        public double ClosingBalanceDouble { get; set; }
        public string CashFlow { get; set; }
        public double CashFlowDouble { get; set; }
        public double RateOfReturn { get; set; }
        public double NAV { get; set; }


	}
}
