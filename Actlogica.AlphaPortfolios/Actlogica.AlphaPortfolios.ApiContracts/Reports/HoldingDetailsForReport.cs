﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
  public class HoldingDetailsForReport
  {
    public string PortfolioId { get; set; }
    public string PortfolioName { get; set; }
    public string InvestmentId { get; set; }
    public string Name { get; set; }
    public string Isin { get; set; }
    public double CurrentHolding { get; set; }
    public string AveragePrice { get; set; }
    public string InvestedCapital { get; set; }
    public double InvestedCapitalDouble { get; set; }
    public string CurrentPrice { get; set; }
    public string MarketValue { get; set; }
    public double MarketValueDouble { get; set; }
    public string UnRealisedGainLoss { get; set; }
    public double UnRealisedGainLossDouble { get; set; }
    public string RealisedGainLoss { get; set; }
    public double RealisedGainLossDouble { get; set; }
    public double GainLossPct { get; set; }
    public double Xirr { get; set; }
    public double Weight { get; set; }
    public string SecurityType { get; set; }
  }
}
