using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class PeakMarginLog
    {
        public string Id { get; set; }

        public string peakMarginId { get; set; }

        public string PortfolioId { get; set; }

        public virtual Portfolio Portfolio { get; set; }

        public double PeakMarginPct { get; set; }
        public double Amount
        {
            get; set;
        }

        public string Status { get; set; }

        public DateTime Date { get; set; }

        public string Type { get; set; }

    }
}
