﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
  public class RptPortfolioSummary
  {
    public string Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string ClientId { get; set; }
    public string ModelId { get; set; }
    public string Name { get; set; }
    public string ClientStrategyCode { get; set; }
    public DateTime StartDate { get; set; }
    public string TotalCapital { get; set; }
    public string InvestedCapital { get; set; }
    public string TotalCost { get; set; }
    public string MarketValue { get; set; }
    public string RealisedGainLoss { get; set; }
    public string UnRealisedGainLoss { get; set; }
    public string GainLossPct { get; set; }
    public double AnnualReturnIrr { get; set; }
    public double TwrrSinceInception { get; set; }
    public double AnnualPerformanceTwrr { get; set; }
    public string CurrentCashBalance { get; set; }
  }
}
