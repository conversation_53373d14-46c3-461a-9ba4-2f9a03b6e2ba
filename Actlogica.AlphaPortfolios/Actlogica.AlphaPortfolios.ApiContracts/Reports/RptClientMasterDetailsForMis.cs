﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptClientMasterDetailsForMis
	{
		public string ClientName { get; set; }
		public string DateOfBirth { get; set; }
		public string Pan { get; set; }
		public string Email { get; set; }
		public string Phone { get; set; }
		public string BseStarUcc { get; set; }
		public string StrategyName { get; set; }
		public string ClientStrategyCode { get; set; }
		public string CustodianPortfolioCode { get; set; }
		public string FAAccountNo { get; set; }
		public string ClientType { get; set; }
		public string Domicile { get; set; }
		public string AccountStatus { get; set; }
		public string AsAtDate { get; set; }
		public string ClientCode { get; set; }
	}
}
