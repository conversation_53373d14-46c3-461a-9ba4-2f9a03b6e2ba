using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptStatementOfInterest : BaseReportDataPayload
	{
		public RptStatementOfInterestTotal Total { get; set; }
		public List<RptStatementOfInterestTransaction> Transactions { get; set; }
	}

	public class RptStatementOfInterestTotal
	{
		public string TotalRecievableAmount { get; set; }
		public double TotalRecievableAmountDouble { get; set; }
		public string TotalRecievedAmount { get; set; }
		public double TotalRecievedAmountDouble { get; set; }
		public string TotalNetAmount { get; set; }
		public double TotalNetAmountDouble { get; set; }
		public string TotalTDSAmount { get; set; }
		public double TotalTDSAmountDouble { get; set; }
	}
	public class RptStatementOfInterestTransaction
	{
        public string RecievedDate { get; set; }
        public string ExDate { get; set; }
        public string Security { get; set; }
        public double Quantity { get; set; }
        public string Amount { get; set; }
        public double AmountDouble { get; set; }
        public string RecievableAmount { get; set; }
        public double RecievableAmountDouble { get; set; }
        public string RecievedAmount { get; set; }
        public double RecievedAmountDouble { get; set; }
        public string NetAmount { get; set; }
        public double NetAmountDouble { get; set; }
        public string TDSAmount { get; set; }
        public double TDSAmountDouble { get; set; }
	}
}
