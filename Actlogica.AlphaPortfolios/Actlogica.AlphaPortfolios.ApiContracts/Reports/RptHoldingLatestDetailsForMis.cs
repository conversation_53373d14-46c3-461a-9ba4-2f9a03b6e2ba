namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class RptHoldingLatestDetailsForMis
    {
        public string ClientName { get; set; }
        public string StrategyName { get; set; }
        public string ClientStrategyCode { get; set; }
        public string InvestmentId { get; set; }
        public string CustodianPortfolioCode { get; set; }
        public string FAAccountNo { get; set; }
        public string Isin { get; set; }
        public string HoldingName { get; set; }
        public string Symbol { get; set; }
        public double Holdings { get; set; }
        public double AveragePrice { get; set; }
        public double Cost { get; set; }
        public double CurrentPrice { get; set; }
        public double MarketValue { get; set; }
        public string Sector { get; set; }
        public string MarketCap { get; set; }
        public string PortfolioId { get; set; }
        public string AsAtDate { get; set; }
    }
}
