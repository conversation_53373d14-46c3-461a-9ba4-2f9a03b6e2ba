using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptFactSheet : BaseReportDataPayload
	{
		public RptFactSheetTotal Total { get; set; }
		public PortfolioSummary PortfolioSummary { get; set; }
		public List<AssetClassData> AssetClassData { get; set; }
		public List<MarketCapData> MarketCapData { get; set; }
		public List<Holdings> PortfolioHoldings { get; set; }
		public List<PerformanceChart> PortfolioPerformanceChart { get; set; }
		public List<SectorAllocation> SectorAllocationData { get; set; }
		public List<Performances> PortfolioTwrrPerformanceNumbers { get; set; }
		public List<Performances> BenchmarkTwrrPerformanceNumbers { get; set; }
		public List<Performances> PortfolioXirrPerformanceNumbers { get; set; }
		public List<Performances> BenchmarkXirrPerformanceNumbers { get; set; }
	}

	public class RptFactSheetTotal
	{
		public string TotalReceivableAmount { get; set; }
		public double TotalReceivableAmountDouble { get; set; }
		public string TotalReceivedAmount { get; set; }
		public double TotalReceivedAmountDouble { get; set; }
		public string TotalNetAmount { get; set; }
		public double TotalNetAmountDouble { get; set; }
	}
	public class PortfolioSummary
	{
		public string SinceInceptionDate { get; set; }
		public string NetInvestment { get; set; }
		public string PortfolioValue { get; set; }
		public string GainLoss { get; set; }
		public double GainLossDouble { get; set; }
		public double SinceInceptionReturns { get; set; }

	}
	public class AssetClassData
	{
		public int SrNo { get; set; }
		public string Sector { get; set; }
		public double Weight { get; set; }

	}
	public class MarketCapData
	{
		public int SrNo { get; set; }
		public string Sector { get; set; }
		public double Weight { get; set; }

	}
	public class Holdings
	{
		public int Sr { get; set; }
		public string Name { get; set; }
		public string Sector { get; set; }
		public string MarketCap { get; set; }
		public double Quantity { get; set; }
		public string MarketValue { get; set; }
		public double Weight { get; set; }

	}
	public class Data
	{
		public string RowLabel { get; set; }
		public List<List<double>> Values { get; set; }

	}
	public class PortfolioPerformance
	{
		public IList<string> Columns { get; set; }
		public IList<Data> Data { get; set; }
	}
	public class Performances
	{
		public string Period { get; set; }
		public double Performance { get; set; }
	}

	public class PerformanceChart
	{
		public string PortfolioLabel { get; set; }
		public string BenchmarkLabel { get; set; }
		public double PortfolioPerformance { get; set; }
		public double BenchmarkPerformance { get; set; }

	}
	public class SectorAllocation
	{
		public int SrNo { get; set; }
		public string Sector { get; set; }
		public double Weight { get; set; }

	}
}
