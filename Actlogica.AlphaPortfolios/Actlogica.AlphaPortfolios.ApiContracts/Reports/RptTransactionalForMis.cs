using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class RptTransactionalForMis
    {
        public string TransactionId { get; set; }
        public string ClientCode { get; set; }
        public string InvestorName { get; set; }
        public string ModelName { get; set; }
        public string Symbol { get; set; }
        public string Name { get; set; }
        public string Exchange { get; set; }
        public string Isin { get; set; }
        public string ClientStrategyCode { get; set; }
        public string CustodianPortfolioCode { get; set; }
        public string FAAccountNo { get; set; }
        public string TransactionDate { get; set; }
        public string SettlementDate { get; set; }
        public string TransactionType { get; set; }
        public string TransactionSubType { get; set; }
        public double Amount { get; set; }
        public double Quantity { get; set; }
        public double CurrentHolding { get; set; }
        public double UnrealisedHolding { get; set; }
        public double Price { get; set; }
        public double MarketRate { get; set; }
        public double Brokerage { get; set; }
        public double ServiceTax { get; set; }
        public double SttAmount { get; set; }
        public double TurnTax { get; set; }
        public double OtherTax { get; set; }
    }
}
