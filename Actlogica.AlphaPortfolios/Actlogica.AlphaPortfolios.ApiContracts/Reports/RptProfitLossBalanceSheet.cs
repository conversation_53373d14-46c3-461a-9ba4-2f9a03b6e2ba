using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptProfitLossBalanceSheet : BaseReportDataPayload
	{
		public string AccountNumber { get; set; }
		public IncomeClass IncomeClass { get; set; }
		public ExpenseClass ExpenseClass { get; set; }
		public Surplus Surplus { get; set; }
		public UnrealisedGainLoss UnrealisedGainLoss { get; set; }
		public Liabilities Liabilities { get; set; }
		public CurrentLiabilitiesAndProvisions CurrentLiabilitiesAndProvisions { get; set; }
		public Assets Assets { get; set; }
		public CurrentAssets CurrentAssets { get; set; }
		public string AsAtDate { get; set; }

	}
	public class IncomeClass
	{
		public string Dividends { get; set; }
		public double DividendsDouble { get; set; }
		public string RealisedGainLoss { get; set; }
		public double RealisedGainLossDouble { get; set; }
		public string Total { get; set; }
		public double TotalDouble { get; set; }

	}
	public class ExpenseClass
	{
		public string ManagementFees { get; set; }
		public double ManagementFeesDouble { get; set; }
		public string SecuritiesTransactionTax { get; set; }
		public double SecuritiesTransactionTaxDouble { get; set; }
		public string OtherExpenses { get; set; }
		public double OtherExpensesDouble { get; set; }
		public string Total { get; set; }
		public double TotalDouble { get; set; }
	}
	public class Surplus
	{
		public string Total { get; set; }
		public double TotalDouble { get; set; }
	}

	public class GainLoss
	{
		[JsonProperty("unrealised")]
		public string Unrealised {get; set;}
		[JsonProperty("realised")]
		public string Realised {get; set;}
		[JsonProperty("date")]
		public string Date {get; set;}
	}
	public class UnrealisedGainLoss
	{
		public string EndOfPeriod { get; set; }
		public double EndOfPeriodDouble { get; set; }
		public string BeginningOfPeriod { get; set; }
		public double BeginningOfPeriodDouble { get; set; }
		public string NetUnrealisedGainLoss { get; set; }
		public double NetUnrealisedGainLosssDouble { get; set; }
		public string Total { get; set; }
		public double TotalDouble { get; set; }
	}

	public class Liabilities
	{
		public string CapitalContributions { get; set; }
		public double CapitalContributionsDouble { get; set; }
		public string LessWithdrawals { get; set; }
		public double LessWithdrawalsDouble { get; set; }
		public AddReservesandSurplus AddReservesandSurplus { get; set; }
	}

	public class AddReservesandSurplus
	{
		public string Beginning { get; set; }
		public double BeginningDouble { get; set; }
		public string ForThePeriod { get; set; }
		public double ForThePeriodDouble { get; set; }
		public string Ending { get; set; }
		public double EndingDouble { get; set; }
	}
	public class CurrentLiabilitiesAndProvisions
	{
		public string PayableAgainstPurchase { get; set; }
		public double PayableAgainstPurchaseDouble { get; set; }
		public string ManagementFees { get; set; }
		public double ManagementFeesDouble { get; set; }
		public string OtherExpenses { get; set; }
		public double OtherExpensesDouble { get; set; }
		public string TotalCurrentLiabilitiesAndProvisions { get; set; }
		public double TotalCurrentLiabilitiesAndProvisionsDouble { get; set; }
		public string Total { get; set; }
		public double TotalDouble { get; set; }
	}

	public class Assets
	{
		public string InvestmentsAtCost { get; set; }
		public double InvestmentsAtCostDouble { get; set; }
	}
	public class CurrentAssets
	{
		public string BalanceWithBanks { get; set; }
		public double BalanceWithBanksDouble { get; set; }
		public string ReceivableAgainstSale { get; set; }
		public double ReceivableAgainstSaleDouble { get; set; }
		public string OutstandingDividend { get; set; }
		public double OutstandingDividendDouble { get; set; }
		public string TotalCurrentAssets { get; set; }
		public double TotalCurrentAssetsDouble { get; set; }
		public string Total { get; set; }
		public double TotalDouble { get; set; }

	}

	public class SurplusCalculation
	{
		public IncomeClass IncomeClass {get; set;}
		public ExpenseClass ExpenseClass {get; set;}
		public Surplus Surplus {get; set;}
	}

}
