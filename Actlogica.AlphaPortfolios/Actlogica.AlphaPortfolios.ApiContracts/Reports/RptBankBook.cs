﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptBankBook : BaseReportDataPayload
	{
		public RptBankBookTotal Total { get; set; }
		public List<RptBankBookTransaction> Transactions { get; set; }
	}
	public class RptBankBookTotal
	{
		public string TotalIncome { get; set; }
		public double TotalIncomeDouble { get; set; }
		public string TotalExpense { get; set; }
		public double TotalExpenseDouble { get; set; }
		public string TotalBalance { get; set; }
		public double TotalBalanceDouble { get; set; }
	}
	public class RptBankBookTransaction
	{
		public string SettlementDate { get; set; }
		public string TransactionDescription { get; set; }
		public string Security { get; set; }
		public string TransactionType { get; set; }
		public string TransactionSubType { get; set; }
		public string BuySell { get; set; }
		public string DepositWithdrawal { get; set; }
		public string Income { get; set; }
		public string Expense { get; set; }
		public string Balance { get; set; }
		public double IncomeDouble { get; set; }
		public double ExpenseDouble { get; set; }
		public double BuySellDouble { get; set; }
		public double DepositWithdrawalDouble { get; set; }
		public double BalanceDouble { get; set; }
		public string Notes { get; set; }


	}
}
