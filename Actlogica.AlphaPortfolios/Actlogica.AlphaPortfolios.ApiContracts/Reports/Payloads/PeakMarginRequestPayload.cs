using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports.Payloads
{
    public class PeakMarginRequestPayload : BaseRequestPayload
    {
        public double PeakMarginPct { get; set; }

        [Required]
        public List<string> Status { get; set; }

        [Required]
        public List<string> PortfolioIds { get; set; }

        public double Amount { get; set; }

        [Required]
        public string Type { get; set; }

        public DateTime FromDate { get; set; }

        public DateTime ToDate { get; set; }

    }
}
