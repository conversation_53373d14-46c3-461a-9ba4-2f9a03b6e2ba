


using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{

    public class RptTransactions : BaseReportDataPayload
    {
        public RptTransactionTotal Total { get; set; }
        public List<RptTransaction> Transactions { get; set; }
    }
    public class RptTransactionTotal
    {

        public string TotalSettlementAmount { get; set; }
        public double TotalSettlementAmountDouble { get; set; }
    }


    public class RptTransaction
    {
        public string Name { get; set; }
        public string Isin { get; set; }
        public string TransactionDate { get; set; }
        public string SettlementDate { get; set; }
        public string TransactionType { get; set; }
        public double QuantityDouble { get; set; }
        public string Quantity { get; set; }
        public double MarketRateDouble { get; set; }
        public string MarketRate { get; set; }
        public double BrokerageDouble { get; set; }
        public string Brokerage { get; set; }
        public double ServiceTaxDouble { get; set; }
        public string ServiceTax { get; set; }
        public double SttAmountDouble { get; set; }
        public string SttAmount { get; set; }
        public double AmountDouble { get; set; }
        public string Amount { get; set; }
    }
}
