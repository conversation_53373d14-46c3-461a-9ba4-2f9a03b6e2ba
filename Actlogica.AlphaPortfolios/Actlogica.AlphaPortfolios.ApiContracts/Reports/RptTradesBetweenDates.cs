﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptTradesBetweenDates
	{
		public string ClientCode { get; set; }
		public string ClientName { get; set; }
		public string StrategyCode { get; set; }
		public string StrategyName { get; set; }
		public string ClientStrategyCode { get; set; }
		public string <PERSON>ustodianPortfolioCode { get; set; }
		public string FAAccountNo { get; set; }
		public string Identifier { get; set; }
		public string Isin { get; set; }
		public string Exchange { get; set; }
		public string ScripName { get; set; }
		public string OrderDate { get; set; }
		public string TransactionType { get; set; }
		public string OrderType { get; set; }
		public double Price { get; set; }
		public double TransactionAmount { get; set; }
		public double Quantity { get; set; }
		public string SourceType { get; set; }
		public string OrderStatus { get; set; }
	}
}
