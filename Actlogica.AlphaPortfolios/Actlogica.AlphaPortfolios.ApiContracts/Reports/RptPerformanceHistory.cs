using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public class RptPerformanceHistory : BaseReportDataPayload
    {

        public string ClientStrategyCode { get; set; }
        public string AccountNumber { get; set; }
        public string ReportType { get; set; }
        public List<RptPerformanceHistoryTransactions> Transactions { get; set; }
        public RptPerformanceHistoryTotals Total { get; set; }
    }
    public class RptPerformanceHistoryTotals
    {
        public string AssetsTotal { get; set; }
        public string AssetsPctTotal { get; set; }
    }

    public class RptPerformanceHistoryTransactions
    {

        [JsonPropertyName("date")]
        public string InceptionDate { get; set; }

        [JsonPropertyName("oneMonth")]
        public string OneMonth { get; set; }

        [JsonPropertyName("threeMonth")]
        public string ThreeMonth { get; set; }

        [JsonPropertyName("sixMonth")]
        public string SixMonth { get; set; }

        [JsonPropertyName("oneYear")]
        public string OneYear { get; set; }

        [JsonPropertyName("twoYear")]
        public string TwoYear { get; set; }

        [JsonPropertyName("threeYear")]
        public string ThreeYear { get; set; }

        [JsonPropertyName("sevenYear")]
        public string SevenYear { get; set; }

        [JsonPropertyName("tenYear")]
        public string TenYear { get; set; }

        [JsonPropertyName("ytd")]
        public string Ytd { get; set; }

        [JsonPropertyName("fytd")]
        public string Fytd { get; set; }

        [JsonPropertyName("sinceInception")]
        public string SinceInception { get; set; }
        public string Assets { get; set; }
        public string AssetsDouble { get; set; }
        public string AssetsPct { get; set; }
        public string Account { get; set; }

    }

    public class BenchmarkResponse
    {
        public List<BenchmarkData> Data { get; set; }
    }

    public class BenchmarkData
    {
        public DateTime Date { get; set; }
        public XirrData Xirr { get; set; }
    }

    public class XirrData
    {
        public string OneMonth { get; set; }
        public string ThreeMonth { get; set; }
        public string SixMonth { get; set; }
        public string OneYear { get; set; }
        public string TwoYear { get; set; }
        public string ThreeYear { get; set; }
        public string SevenYear { get; set; }
        public string TenYear { get; set; }
        public string Ytd { get; set; }
        public string Fytd { get; set; }
        public string SinceInception { get; set; }
    }

    public class AnalyticsResponse
    {
        public List<AumData> Aum { get; set; }
    }

    public class AumData
    {
        public string MarketValue { get; set; }
    }


}
