using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class RptCurrentPortfolio : BaseReportDataPayload
	{
		public List<RptCurrentPortfolioTopTenHoldings> TopTenHoldings { get; set; }
		public List<RptCurrentPortfolioHoldings> PortfolioHoldings { get; set; }
		public List<CategoryAllocation> CategoryAllocation { get; set; }
		public List<MarketCapAllocation> MarketCapAllocation { get; set; }
		public RptCurrentPortfolioTotal GrandTotals { get; set; }
	}

	public class RptCurrentPortfolioTotal
	{
		public string TotalQuantity { get; set; }
		public string TotalUnitCost { get; set; }
		public string TotalCost { get; set; }
		public string TotalPrice { get; set; }
		public string TotalUnitPrice { get; set; }
		public string TotalMarketValue { get; set; }
		public string TotalGainLoss { get; set; }
		public string TotalGainLossPercentage { get; set; }
		public string TotalAssetsPercentage { get; set; }
		public string TotalReceivableAmountDouble { get; set; }
		public string TotalReceivedAmount { get; set; }
		public string TotalReceivedAmountDouble { get; set; }
		public string TotalNetAmount { get; set; }
		public string TotalNetAmountDouble { get; set; }
	}

	public class RptCurrentPortfolioTopTenHoldings
	{
		public int Sr { get; set; }
		public string Name { get; set; }
		public string Industry { get; set; }
		public string MarketCap { get; set; }
		public double Weight { get; set; }
	}

	public class RptCurrentPortfolioHoldings
	{
		public string Name { get; set; }
		public string ISIN { get; set; }
		public string Quantity { get; set; }
		public double QuantityDouble { get; set; }
		public string UnitCost { get; set; }
		public double UnitCostDouble { get; set; }
		public string Cost { get; set; }
		public double CostDouble { get; set; }
		public string Price { get; set; }
		public double PriceDouble { get; set; }
		public string MarketValue { get; set; }
		public double MarketValueDouble { get; set; }
		public string GainLoss { get; set; }
		public double GainLossDouble { get; set; }
		public double GainLossPct { get; set; }
		public double AssetsPct { get; set; }
		public string Xirr { get; set; }
		public double XirrDouble { get; set; }
		public double TotalQuantity { get; set; }
		public double TotalUnitCost { get; set; }
		public double TotalCost { get; set; }
		public double TotalPrice { get; set; }
		public double TotalMarketValue { get; set; }
		public double TotalGainLoss { get; set; }
		public double TotalGainLossPercentage { get; set; }
		public double TotalAssetsPercentage { get; set; }


	}

	public class PortfolioAllocationChart
	{
		public string Label { get; set; }
		public int Y { get; set; }
	}

	public class CategoryAllocation
	{
		public string Label { get; set; }
		public double Y { get; set; }
	}

	public class MarketCapAllocation
	{
		public string Label { get; set; }
		public double Y { get; set; }
	}



}
