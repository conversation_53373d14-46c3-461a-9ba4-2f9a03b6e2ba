namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
    public enum SendPortfolioReportBy
    {
        Email,
        Physical
    }

    public enum SendAlertBy
    {
        Email,
        SMS
    }

    public enum AcceptOrderRequest
    {
        <PERSON><PERSON>,
        R<PERSON>,
        Advisor,
        ExternalAdvisor
    }

    public enum FinancialMonthStartfrom
    {
        Jan01,
        April01
    }
}
