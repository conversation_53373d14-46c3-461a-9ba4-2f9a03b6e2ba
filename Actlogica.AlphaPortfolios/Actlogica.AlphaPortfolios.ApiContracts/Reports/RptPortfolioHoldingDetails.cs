﻿using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
  public class RptPortfolioHoldingDetails : BaseReportDataPayload
  {
    public RptPortfolioSummary Summary { get; set; }
    public List<HoldingsByAssetType> GroupedSecurityTypeHoldingDetails { get; set; }
  }

  public class HoldingsByAssetType
  {
    public string AssetType { get; set; }
    public AssetTypeTotal AssetTypeTotal { get; set; }
    public List<HoldingDetailsForReport> AssetTypeHoldings { get; set; }
  }

  public class AssetTypeTotal
  {
    public string AggregateValue { get; set; }
    public string Cost { get; set; }
    public string MarketValue { get; set; }
    public string UnRealisedGainLoss { get; set; }
    public string GainLossPct { get; set; }
    public double Weight { get; set; }
    public string AggregateType { get; set; }
  }
}
