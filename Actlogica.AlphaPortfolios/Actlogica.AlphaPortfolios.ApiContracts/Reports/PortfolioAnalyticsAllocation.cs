﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Reports
{
	public class PortfolioAnalyticsAllocation
	{
		public string ClientId { get; set; }
		public string ClientName { get; set; }
		public string PortfolioId { get; set; }
		public string ClientStrategyCode { get; set; }
		public string PortfolioName { get; set; }
		public DateTime AsAtDate { get; set; }
		public double MarketValue { get; set; }
		public double Weight { get; set; }
	}
}
