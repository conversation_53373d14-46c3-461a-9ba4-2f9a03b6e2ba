﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Azure.Documents;

namespace Actlogica.AlphaPortfolios.ApiContracts.Communications.Email
{
	public class To
	{
		public string email { get; set; }
		public string name { get; set; }
	}
  public class Cc
  {
    public string email { get; set; }
  }

  public class DynamicTemplateData
	{
		public string verb { get; set; }
		public string adjective { get; set; }
		public string noun { get; set; }
		public string currentDayofWeek { get; set; }
	}

	public class Personalization
	{
		public List<To> to { get; set; }
    public List<Cc> cc { get; set; }
    public object dynamic_template_data { get; set; }
		public string subject { get; set; }
	}

	public class From
	{
		public string email { get; set; }
		public string name { get; set; }
	}

	public class ReplyTo
	{
		public string email { get; set; }
		public string name { get; set; }
	}

	public class SendGridMailObject
	{
		public List<Personalization> personalizations { get; set; }
		public From from { get; set; }
		public ReplyTo reply_to { get; set; }
		public string template_id { get; set; }
		public List<Attachment> attachments { get; set; }
	}

	public class Attachment
	{
		public string Content { get; set; }
		public string FileName { get; set; }
	}

	public class EmailPayload
	{
		public string Subject { get; set; }
		public string To { get; set; }
    	public string ToCc { get; set; }
		public string TemplateId { get; set; }
    	public object DynamicData { get; set; }
		public List<EmailAttachmentPayload> Attachments { get; set; }
		public List<Attachment> SendGridAttachments { get; set; }
		public string From { get; set; }
		public string HtmlContent { get; set; }
		public string PlainTextContent { get; set; }
	}
}
