﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class ClientOrderEntry
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string ClientId { get; set; }
		public string ClientCode { get; set; }
		public string ClientStrategyCode { get; set; }
		public string ClientName { get; set; }
		public string StrategyCode { get; set; }
		public string StrategyName { get; set; }
		public string StrategyModelName { get; set; }
		public string StrategyCustodyCode { get; set; }
		public string StrategyTradingAccountNumber { get; set; }
		public string StrategyModelId { get; set; }
		public string Identifier { get; set; }
		public string Isin { get; set; }
		public string Exchange { get; set; }
		public string ScripName { get; set; }
		public SecurityType InvestmentType { get; set; }
		public TransactionType TransactionType { get; set; }
		public TransactionSubType TransactionSubType { get; set; }
		public double Quantity { get; set; }
		public double CurrentHolding { get; set; }
		public string Currency { get; set; }
		public double CurrencyConversionRate { get; set; }
		public double PendingQuantity { get; set; }
		public double TransactionQuantity { get; set; }
		public double Price { get; set; }
		public string OrderType { get; set; }
		public DateTime OrderDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public OrderStatus OrderStatus { get; set; }
		public double TransactionAmount { get; set; }
		public double TransactionAmountRequired { get; set; }
		public double PortfolioAvailableCash { get; set; }
		public double SettlementQuantity { get; set; }
		public double SettlementPrice { get; set; }
		public double SettlementMarketAmount { get; set; }
		public double SettlementBrokerageAmount { get; set; }
		public double SettlementServiceTax { get; set; }
		public double SettlementSttAmount { get; set; }
		public double SettlementNetRate { get; set; }
		public double SettlementTurnTax { get; set; }
		public double SettlementOtherTax { get; set; }
		public double SettlementNetAmount { get; set; }
		public string SourceType { get; set; }
		public string SourceReference { get; set; }
		public string PortfolioName { get; set; }
		public string BrokerId { get; set; }
		public string ValidityStatus { get; set; }
		public string ValidityReason { get; set; }
		public string CustodianName { get; set; }
		public string MfFolioNumber { get; set; }
		public string MfBuySellType { get; set; }
		public string MfAllUnitsRedemptionFlag { get; set; }
		public string MfClientBank { get; set; }
		public string MfOmsClientCode { get; set; }
		public string EUINNumber { get; set; }
		public DomicileType ClientDomicile { get; set; }
		public string ClientTradingAccount { get; set; }
		public string Remarks { get; set; }
		public string OrderRationale { get; set; }

		public string PortfolioId { get; set; }
		public string CreatedBy { get; set; }
		public string Series { get; set; }
		
	}
}
