﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class OrderSettlementInClientAccount
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string SrNo { get; set; }
		public string ContractNumber { get; set; }
		public string PartyCode { get; set; }
		public string ClientShortName { get; set; }
		public string CustodyClearingCode { get; set; }
		public string ClientCustodyCode { get; set; }
		public string ClientFaCode { get; set; }
		public string ClientDematNumber { get; set; }
		public string ScripCode { get; set; }
		public string Exchange { get; set; }
		public string Isin { get; set; }
		public string ScripName { get; set; }
		public string SecurityType { get; set; }
		public string FolioNo { get; set; }
		public string Series { get; set; }
		public string SettNo { get; set; }
		public string SettType { get; set; }
		public string SellBuy { get; set; }
		public DateTime TradeDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public double OriginalQuantityOrdered { get; set; }
		public double Quantity { get; set; }
		public double SettlementAllocationPercentage { get; set; }
		public double MarketRate { get; set; }
		public double MarketAmount { get; set; }
		public double ActualBrokerage { get; set; }
		public double ActualBrokeragePerUnit { get; set; }
		public double ExpectedBrokerage { get; set; }
		public double ExpectedBrokeragePerUnit { get; set; }
		public double ServiceTax { get; set; }
		public double NetRate { get; set; }
		public double SttAmount { get; set; }
		public double TurnTaxExchangeTxnTax { get; set; }
		public double StampDutyAndOtherCharges { get; set; }
		public double NetAmount { get; set; }
		public string ClientOrderEntryId { get; set; }
		public string StrategyModelid { get; set; }
		public string ClientId { get; set; }
		public string ClientStrategyCode { get; set; }
		public string BrokerName { get; set; }
		public string StrategyName { get; set; }
		public string TradeOrderSettlementFileId { get; set; }
	}
}
