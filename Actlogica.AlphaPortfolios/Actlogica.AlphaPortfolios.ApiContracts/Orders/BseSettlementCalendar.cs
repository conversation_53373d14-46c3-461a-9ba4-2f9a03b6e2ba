﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class BseSettlementCalendar
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string SettlementCalendar { get; set; }
		public string Year { get; set; }
		public string Month { get; set; }
		public string SettlementNo { get; set; }
		public string SettlementNoForDep { get; set; }
		public string TradingDate { get; set; }
		public string EntrySixASevenADataByMembers { get; set; }
		public string ConfOfSixASevenAByCustodians { get; set; }
		public string PayInPayOut { get; set; }
		public string AuctionSettNo { get; set; }
		public string SubmissionOfAuctionOffersOn { get; set; }
		public string AuctionPayInPayOut { get; set; }
	}
}
