﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class ClientOrderSettlement
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string ClientName { get; set; }
    public string Identifier { get; set; }
    public string Isin { get; set; }
    public string Exchange { get; set; }
    public string ScripName { get; set; }
    public string CustodyClearingCode { get; set; }
    public string BrokerAccountNumber { get; set; }
    public DateTime SettlementDate { get; set; }
    public double SettlementQuantity { get; set; }
    public double SettlementPrice { get; set; }
    public double SettlementMarketAmount { get; set; }
    public double SettlementBrokerageAmount { get; set; }
    public double SettlementServiceTax { get; set; }
    public double SettlementSttAmount { get; set; }
    public double SettlementNetRate { get; set; }
    public double SettlementTurnTax { get; set; }
    public double SettlementOtherTax { get; set; }
    public double SettlementNetAmount { get; set; }
    public string StrategyId { get; set; }
    public string ModelId { get; set; }
    public string ClientId { get; set; }
  }
}
