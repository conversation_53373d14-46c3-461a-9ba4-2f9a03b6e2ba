﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class DeploymentTracker
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string DeploymentSetupId { get; set; }
		public string SetupType { get; set; }
		public string Status { get; set; }
		public DateTime TriggerDate { get; set; }
		public string TriggeredBy { get; set; }
		public int InstallmentNo { get; set; }
		public string Rationale { get; set; }
		public string DeploymentType { get; set; }
		public string PreviousTrackerId { get; set; }	
		public bool IsTriggered { get; set; }
	}
}
