﻿using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders.TradeAllocationFiles
{
	public class AlphaTradeAllocationFile
	{
		[Display(Name = "Id")]
		public string Id { get; set; }
		[Display(Name = "SrNo")]
		public string SrNo { get; set; }
		[Display(Name = "ContractNumber")]
		public string ContractNumber { get; set; }
		[Display(Name = "PartyCode")]
		public string PartyCode { get; set; }
		[Display(Name = "ClientShortName")]
		public string ClientShortName { get; set; }
		[Display(Name = "CustodyClearingCode")]
		public string CustodyClearingCode { get; set; }
		[Display(Name = "ScripCode")]
		public string ScripCode { get; set; }
		[Display(Name = "Isin")]
		public string Isin { get; set; }
		[Display(Name = "ScripName")]
		public string ScripName { get; set; }
		[Display(Name = "Series")]
		public string Series { get; set; }
		[Display(Name = "SettNo")]
		public string SettNo { get; set; }
		[Display(Name = "SettType")]
		public string SettType { get; set; }
		[Display(Name = "SellBuy")]
		public string SellBuy { get; set; }
		[Display(Name = "TradeDate")]
		public string TradeDate { get; set; }
		[Display(Name = "Quantity")]
		public string Quantity { get; set; }
		[Display(Name = "SettlementAllocationPercentage")]
		public string SettlementAllocationPercentage { get; set; }
		[Display(Name = "MarketRate")]
		public string MarketRate { get; set; }
		[Display(Name = "MarketAmount")]
		public string MarketAmount { get; set; }
		[Display(Name = "ActualBrokerage")]
		public string ActualBrokerage { get; set; }
		[Display(Name = "ExpectedBrokerage")]
		public string ExpectedBrokerage { get; set; }
		[Display(Name = "ServiceTax")]
		public string ServiceTax { get; set; }
		[Display(Name = "NetRate")]
		public string NetRate { get; set; }
		[Display(Name = "SttAmount")]
		public string SttAmount { get; set; }
		[Display(Name = "TurnTaxExchangeTxnTax")]
		public string TurnTaxExchangeTxnTax { get; set; }
		[Display(Name = "StampDutyAndOtherCharges")]
		public string StampDutyAndOtherCharges { get; set; }
		[Display(Name = "NetAmount")]
		public string NetAmount { get; set; }
		[Display(Name = "ClientOrderEntryId")]
		public string ClientOrderEntryId { get; set; }
		[Display(Name = "StrategyModelid")]
		public string StrategyModelId { get; set; }
		[Display(Name = "ClientId")]
		public string ClientId { get; set; }
		[Display(Name = "StrategyName")]
		public string StrategyName { get; set; }
		[Display(Name = "TradeOrderSettlementFileId")]
		public string TradeOrderSettlementFileId { get; set; }
		[Display(Name = "CreatedDate")]
		public string CreatedDate { get; set; }
		[Display(Name = "LastUpdatedDate")]
		public string LastUpdatedDate { get; set; }
		[Display(Name = "Exchange")]
		public string Exchange { get; set; }
		[Display(Name = "FolioNo")]
		public string FolioNo { get; set; }
		[Display(Name = "ClientCustodyCode")]
		public string ClientCustodyCode { get; set; }
		[Display(Name = "ClientDematNumber")]
		public string ClientDematNumber { get; set; }
		[Display(Name = "ClientFaCode")]
		public string ClientFaCode { get; set; }
		[Display(Name = "SettlementDate")]
		public string SettlementDate { get; set; }
		[Display(Name = "OriginalQuantityOrdered")]
		public string OriginalQuantityOrdered { get; set; }
		[Display(Name = "ActualBrokeragePerUnit")]
		public string ActualBrokeragePerUnit { get; set; }
		[Display(Name = "ExpectedBrokeragePerUnit")]
		public string ExpectedBrokeragePerUnit { get; set; }
	}
}
