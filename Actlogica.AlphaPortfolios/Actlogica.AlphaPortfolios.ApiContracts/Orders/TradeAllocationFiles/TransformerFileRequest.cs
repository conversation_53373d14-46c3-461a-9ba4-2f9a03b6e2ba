﻿using Actlogica.AlphaPortfolios.ApiContracts.Transformer;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders.TradeAllocationFiles
{
	public class TransformerFileRequest
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string RequestedBy { get; set; }
		public string MetaDataRefId { get; set; }
		public int TransformerProjectCode { get; set; }
		public string AlphaTransformerIntegrationId { get; set; }
		public string AlphaFilePath { get; set; }
		public string OutputFilePath { get; set; }
		public TransformType TransformType { get; set; }
		public string ProcessingStatus { get; set; }
	}
}
