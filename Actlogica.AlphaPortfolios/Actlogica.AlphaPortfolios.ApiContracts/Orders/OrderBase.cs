﻿using System;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class OrderBase
	{
		public string Id { get; set; }
		public string PoolOrderId { get; set; }
		public DateTime Date { get; set; }
		public TransactionType Type { get; set; }
		public TransactionSubType SubType { get; set; }
		public double Units { get; set; }
		public double Price { get; set; }
		public double Amount { get; set; }
		public OrderStatus Status { get; set; }
	}
}
