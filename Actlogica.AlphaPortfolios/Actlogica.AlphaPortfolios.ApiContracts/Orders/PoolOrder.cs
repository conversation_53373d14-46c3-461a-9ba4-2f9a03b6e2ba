﻿using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class PoolOrder
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
    public int OrderNumber { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime SettlementDate { get; set; }
    public OrderStatus OrderStatus { get; set; }
    public string CustodianId { get; set; }
    public string BrokerId { get; set; }

    public string ModelId { get; set; }

    public IEnumerable<PoolOrderEntry> OrdersInPool { get; set; }
  }
}
