﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class TradeOrder
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string SourceOrderSettlementFileEntryId { get; set; }
		public string PlacedBy { get; set; }
		public string LastSettledBy { get; set; }
		public int OrderNo { get; set; }
		public string StrategyCode { get; set; }
		public string StrategyId { get; set; }
		public string Identifier { get; set; }
		public string Series { get; set; }
		public string Isin { get; set; }
		public string Type { get; set; }
		public double Price { get; set; }
		public string BuySell { get; set; }
		public double OrderQuantity { get; set; }
		public double OrderAmount { get; set; }
		public double LastSettledQuantity { get; set; }
		public double PendingOrderQuantity { get; set; }
		public double PendingOrderPercentage { get; set; }
		public DateTime OrderDate { get; set; }
		public string CustodianDpCode { get; set; }
		public string TradingAccountNo { get; set; }
		public string SettlementStatus { get; set; }
		public string CustodianId { get; set; }
		public string BrokerId { get; set; }
		public string MfFolioNumber { get; set; }
		public string MfBuySellType { get; set; }
		public string MfAllUnitsRedemptionFlag { get; set; }
		public string MfClientBank { get; set; }
		public string MfOmsClientCode { get; set; }
		public string EUINNumber { get; set; }
		public string SecurityName { get; set; }

		public string Exchange { get; set; }
		public Custodian Custodian { get; set; }
		public Broker Broker { get; set; }
	}
}
