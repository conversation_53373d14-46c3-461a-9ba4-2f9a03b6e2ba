﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class TradeOrderSettlementFileUploadEntry
	{
    public string TradeOrderSettlementFileEntryId { get; set; }
    public string SrNo { get; set; }
    public string ContractNo { get; set; }
    public string PartyCode { get; set; }
    public string ClientShortName { get; set; }
    public string CustodyClearingCode { get; set; }
    public string ScripCode { get; set; }
    public string Isin { get; set; }
    public string ScripName { get; set; }
    public string Series { get; set; }
    public string Exchange { get; set; }
    public string SettNo { get; set; }
    public string SettType { get; set; }
    public string SellBuy { get; set; }
    public string TradeDate { get; set; }
    public double TradeQty { get; set; }
    public double MarketRate { get; set; }
    public double MarketAmount { get; set; }
    public double Brokerage { get; set; }
    public double ServiceTax { get; set; }
    public double NetRate { get; set; }
    public double SttAmount { get; set; }
    public double TurnTax { get; set; }
    public double StampDuty { get; set; }
    public double NetAmount { get; set; }
    public double BrokeragePerUnit { get; set; }
    public string DematPhysical { get; set; }
    public string DPFolioNo { get; set; }
    public string FolioNo { get; set; }
    public string BuySellType { get; set; }
    public string OrderRemarks { get; set; }
    public double ExpectedTradeQuantity { get; set; }
    public double ExpectedMarketAmount { get; set; }
    public double ExpectedBrokerage { get; set; }
    public double BrokerageDifference { get; set; }
    public double ExpectedServiceTax { get; set; }
    public double ExpectedNetRate { get; set; }
    public double ExpectedSttAmount { get; set; }
    public double ExpectedTurnTax { get; set; }
    public double ExpectedStampDuty { get; set; }
    public double ExpectedNetAmount { get; set; }
  }
}
