﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class SystematicDeployment
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string ClientId { get; set; }

		[Required]
		public string SourcePortfolioId { get; set; }
		[Required]
		public string DestinationPortfolioId { get; set; }
		[Required]
		public string Type { get; set; }

		public string DeploymentType { get; set; }
		[Required]
		public string Frequency { get; set; }
		public string Status { get; set; }
		[Required]
		public DateTime StartDate { get; set; }
		public DateTime EndDate { get; set; }
		public DateTime InstalmentDate { get; set; }
		public DateTime RedemptionDate { get; set; }
		[Required]
		public int NoOfInstallments { get; set; }
		public int CompletedInstallments { get; set; }
		public int PendingInstallments { get; set; }
		public string CreatedBy { get; set; }
		public double InstallmentAmount { get; set; }
		public double InstallmentPercentage { get; set; }
		[Required]
		public int SellTriggerPeriod { get; set; }
		public bool IsActive { get; set; }
		public bool IsAutoSellTrigger { get; set; }
		public bool IsAutoBuyTrigger { get; set; }
		public string LastBuyTrackerId { get; set; }
		public string LastSellTrackerId { get; set; }
		public Client ForClient { get; set; }
		public Portfolio SourcePortfolio { get; set; }
		public Portfolio DestinationPortfolio { get; set; }

	}
}
