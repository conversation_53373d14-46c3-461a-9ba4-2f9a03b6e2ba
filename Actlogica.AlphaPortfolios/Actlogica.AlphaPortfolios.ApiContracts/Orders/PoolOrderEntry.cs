﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class PoolOrderEntry
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
    public string Identifier { get; set; }
    public string Isin { get; set; }
    public string Exchange { get; set; }
    public string ScripName { get; set; }
    public SecurityType InvestmentType { get; set; }
    public TransactionType TransactionType { get; set; }
    public TransactionSubType TransactionSubType { get; set; }
    public double Quantity { get; set; }
    public double PendingQuantity { get; set; }
    public double Price { get; set; }
    public string OrderType { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime SettlementDate { get; set; }
    public OrderStatus OrderStatus { get; set; }
    public double TransactionAmount { get; set; }
    public double SettlementQuantity { get; set; }
    public double SettlementPrice { get; set; }
    public double SettlementMarketAmount { get; set; }
    public double SettlementBrokerageAmount { get; set; }
    public double SettlementServiceTax { get; set; }
    public double SettlementSttAmount { get; set; }
    public double SettlementNetRate { get; set; }
    public double SettlementTurnTax { get; set; }
    public double SettlementOtherTax { get; set; }
    public double SettlementNetAmount { get; set; }
    public string PoolOrderId { get; set; }
  }
}
