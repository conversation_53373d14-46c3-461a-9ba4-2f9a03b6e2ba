﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class ClientOrder
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime SettlementDate { get; set; }
    public OrderStatus OrderStatus { get; set; }
    public string CustodianId { get; set; }
    public string BrokerId { get; set; }
    public string ClientId { get; set; }
    public string PortfolioId { get; set; }
    public string PoolOrderId { get; set; }
    public IEnumerable<ClientOrderEntry> OrderEntries { get; set; }
    public Client Client { get; set; }
  }
}
