﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class TradeSettlementLog
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string TradeOrderId { get; set; }
		public string TradeOrderSettlementId { get; set; }
		public string TradeOrderSettlementFileLogId { get; set; }
		public string AppliedSettlementBuySell { get; set; }
		public DateTime AppliedSettlementTradeDate { get; set; }
		public double AppliedSettlementQty { get; set; }
		public double AppliedSettlementMktRate { get; set; }
		public double AppliedSettlementMktAmount { get; set; }
		public double AppliedSettlementBrkg { get; set; }
		public double ApplidSettlementSerTax { get; set; }
		public double AppliedSettlementNetRate { get; set; }
		public double AppliedSettlementSttAmount { get; set; }
		public double AppliedSettlementTurnTax { get; set; }
		public double AppliedSettlementStampDuty { get; set; }
		public double AppliedSettlementNetAmount { get; set; }
	}
}
