using Actlogica.AlphaPortfolios.ApiContracts.Common;
using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class ClientOrderEntryForReceivables
	{
		public string TradeOrderUnSettledAmountsId { get; set; }
		public string Id { get; set; }
		public string Identifier { get; set; }
		public string Isin { get; set; }
		public string Exchange { get; set; }
		public string ScripName { get; set; }
		public SecurityType InvestmentType { get; set; }
		public TransactionType TransactionType { get; set; }
		public TransactionSubType TransactionSubType { get; set; }
		public double Quantity { get; set; }
		public double PendingQuantity { get; set; }
		public double Price { get; set; }
		public string ClientId { get; set; }
		public string Series { get; set; }
		
	}
}
