﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class InvestmentSearchResult
	{
		public string ClientId { get; set; }
		public string PortfolioId { get; set; }
		public string ModelId { get; set; }
		public string InvestmentId { get; set; }
		public string InvestmentName { get; set; }
		public string Isin { get; set; }
		public string Symbol { get; set; }
		public string Exchange { get; set; }
		public bool IsMutualFund { get; set; }
		public string ClientName { get; set; }
		public string ClientStrategyCode { get; set; }
		public double CurrentHolding { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double Weight { get; set; }
		public double Return { get; set; }
		public double PortfolioValue { get; set; }
		public double Cash { get; set; }
		public double CashWeight { get; set; }
		public string Sector { get; set; }
		public double SectorTotalWeight { get; set; }
		public string TradeType { get; set; }
		public double TradeQuantity { get; set; }
		public double TradeAmount { get; set; }
		public double TradePrice { get; set; }
		public string OrderRemarks { get; set; }
		public bool IsOrderValid { get; set; }
	}
}
