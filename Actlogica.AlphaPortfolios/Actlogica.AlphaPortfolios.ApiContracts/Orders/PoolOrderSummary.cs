﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using System.Collections;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class PoolOrderSummary
	{
		public string PortfolioName { get; set; }
		public string StrategyCustodyCode { get; set; }
		public TransactionType TransactionType { get; set; }
		public string CustodyName { get; set; }
		public string Exchange {get;set;}
		public double NoOfOrders { get; set; }
		public bool IsMutualFund { get; set; }
		public List<string> ClientOrdersInPool { get; set; }
		public List<StrategyBroker> StrategyBrokers { get; set; }
	}
}
