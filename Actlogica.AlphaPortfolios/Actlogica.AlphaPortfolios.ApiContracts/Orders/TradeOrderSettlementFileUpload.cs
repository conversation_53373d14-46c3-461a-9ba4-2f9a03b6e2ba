﻿using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class TradeOrderSettlementFileUpload
	{
		public string CustodianId { get; set; }
		public string PoolOrderId { get; set; }
		public string SettlementSourceFileId { get; set; }
		public IEnumerable<TradeOrderSettlementFileUploadEntry> OrderEntries { get; set; }
		public IEnumerable<ClientOrder> ClientOrders { get; set; }
	}
}
