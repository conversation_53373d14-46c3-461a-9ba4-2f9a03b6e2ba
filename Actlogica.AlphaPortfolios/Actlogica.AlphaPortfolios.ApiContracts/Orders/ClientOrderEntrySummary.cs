﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class ClientOrderEntrySummary
	{
		public string StrategyCode { get; set; }
		public string StrategyName { get; set; }
		public string StrategyModelName { get; set; }
		public string StrategyModelId { get; set; }
		public int OrderCount { get; set; }
		public string OrderStatus { get; set; }
		public string ValidityStatus { get; set; }
		public string ValidityReason { get; set; }
        public DateTime RecentOrderDate { get; set; }
    }
}
