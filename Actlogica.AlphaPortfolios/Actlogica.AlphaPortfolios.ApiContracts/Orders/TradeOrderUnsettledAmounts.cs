﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Orders
{
	public class TradeOrderUnsettledAmounts
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public DateTime TradeDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public string Type { get; set; }
		public double Amount { get; set; }
		public double Quantity { get; set; }
		public string Status { get; set; }
		public string SettlementCalendarId { get; set; }
		public string ClientId { get; set; }
		public Client Client { get; set; }
		public string ClientOrderEntryId { get; set; }
		public virtual ClientOrderEntry ClientOrderEntry { get; set; }
		public string PortfolioId { get; set; }
		public Portfolio Portfolio { get; set; }
		public string StrategyModelId { get; set; }
		public virtual Model StrategyModel { get; set; }
	}
}
