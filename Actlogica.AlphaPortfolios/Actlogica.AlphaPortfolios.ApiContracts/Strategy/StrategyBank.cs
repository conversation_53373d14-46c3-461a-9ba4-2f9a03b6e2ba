﻿using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Strategy
{
	public class StrategyBank
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		[Required(ErrorMessage = "Bank name is required.")]
		public string Name { get; set; }
		public string AddressLine1 { get; set; }
		public string AddressLine2 { get; set; }
		public string City { get; set; }
		public string State { get; set; }
		public string Postcode { get; set; }
		[Required(ErrorMessage = "Bank account name is required.")]
		public string AccountName { get; set; }
		[Required(ErrorMessage = "Bank account number is required.")]
		public string AccountNumber { get; set; }
		public string Ifsc { get; set; }
		public string Micr { get; set; }
		public BankAccountType BankAccountType { get; set; }
		public string BranchName { get; set; }

		public Currency? Currency { get; set; }

		public string SwiftCode { get; set; }

		public AccountStatus AccountStatus { get; set; }

		public DateTime? FromDate { get; set; }
		public DateTime? ToDate { get; set; }

	}
}
