﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Strategy
{
	public class ModelDeviationReport
	{
		public string ClientId { get; set; }
		public string PortfolioId { get; set; }
		public string ClientName { get; set; }
		public string ClientStrategyCode { get; set; }
		public string Isin { get; set; }
		public string Symbol { get; set; }
		public string SecurityName { get; set; }
		public bool IsMutualFund { get; set; }
		public string Exchange { get; set; }
		public double PortfolioWeight { get; set; }
		public double PortfolioHolding { get; set; }
		public double PortfolioMv { get; set; }
		public double PortfolioCash { get; set; }
		public double ModelWeight { get; set; }
		public double DeviationPct { get; set; }
		public string Action { get; set; }
	}
}
