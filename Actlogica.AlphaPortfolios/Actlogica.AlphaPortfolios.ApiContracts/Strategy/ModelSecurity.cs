﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Strategy
{
	public class ModelSecurity
	{
		public string Id { get; set; }
		public string ModelId { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string Isin { get; set; }
		public string Name { get; set; }
		public string CompName { get; set; }
		public string Symbol { get; set; }
		public string Exchange { get; set; }
		public string Scripcode { get; set; }
		public string Industry { get; set; }
		public string SchemeName { get; set; }
		public string AmfiCode { get; set; }
		public string FundClass { get; set; }
		public double Weight { get; set; }
		public bool IsMutualFund { get; set; }
		public int InstrumentType { get; set; }
		public double LatestPrice { get; set; }
		public double MarketValue { get; set; }
    public double Quantity { get; set; }
		public string ModelName { get; set; }
  }
}
