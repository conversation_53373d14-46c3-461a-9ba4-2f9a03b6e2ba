﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Strategy
{
	public class Strategy
	{
		public string Id { get; set; }
		public string StrategyCode { get; set; }
		public string Name { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public DateTime StartDate { get; set; }
		public bool IsOpen { get; set; }
		public bool IsMfDemat { get; set; }
		public string StrategyBankId { get; set; }
		public string BankName { get; set; }
		public string AccountNumber { get; set; }
		public string BrokersCount { get; set; }
		public string CustodiansCount { get; set; }
		public StrategyBank StrategyBank { get; set; }
		public IEnumerable<StrategyBroker> StrategyBrokers { get; set; }
		public IEnumerable<StrategyCustodian> StrategyCustodians { get; set; }
		public IEnumerable<StrategyUser> StrategyUsers { get; set; }
	}
}
