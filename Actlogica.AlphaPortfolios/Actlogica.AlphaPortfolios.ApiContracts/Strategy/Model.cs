﻿using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Strategy
{
	public class Model
	{
		public string Id { get; set; }
		public string Name { get; set; }
		public bool IsOpen { get; set; }
		public string ModelCode { get; set; }
		public string StrategyId { get; set; }
		public Strategy Strategy { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public IEnumerable<ModelSecurity> SecuritiesInModel { get; set; }
	}
}
