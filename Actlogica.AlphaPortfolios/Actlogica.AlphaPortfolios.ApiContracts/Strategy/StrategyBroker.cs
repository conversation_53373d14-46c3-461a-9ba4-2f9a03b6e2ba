﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Strategy
{
	public class StrategyBroker
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string TradingAccountNumber { get; set; }
		public string BrokerId { get; set; }
		public string StrategyId { get; set; }
        public string StrategyCustodianId { get; set; }
        public virtual Broker Broker { get; set; }
		public virtual Strategy Strategy { get; set; }
	}
}
