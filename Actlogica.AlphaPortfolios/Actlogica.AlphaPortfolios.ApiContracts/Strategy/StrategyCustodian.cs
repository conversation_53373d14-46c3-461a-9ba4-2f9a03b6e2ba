﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Strategy
{
    public class StrategyCustodian
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string DpID { get; set; }
        public string CustodianId { get; set; }
        public string StrategyId { get; set; }
        public virtual Custodian Custodian { get; set; }
        public virtual Strategy Strategy { get; set; }
    }
}