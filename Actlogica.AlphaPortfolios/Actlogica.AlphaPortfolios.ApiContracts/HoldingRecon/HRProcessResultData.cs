﻿namespace Actlogica.AlphaPortfolios.ApiContracts.HoldingRecon
{
    public class HRProcessResultData
    {
        public string ClientCode { get; set; }
        public string ClientName { get; set; }
        public string Strategy { get; set; }
        public string Model { get; set; }
        public string Symbol { get; set; }
        public string SCRIP { get; set; }
        public string ISIN { get; set; }
        public string PortfolioHolding { get; set; }
        public string ReportedCustFileHolding { get; set; }
        public string ReportedFAFileHolding { get; set; }
        public string ProcessingStatus { get; set; }
        public string HoldingReconProcessId { get; set; }

    }
}
