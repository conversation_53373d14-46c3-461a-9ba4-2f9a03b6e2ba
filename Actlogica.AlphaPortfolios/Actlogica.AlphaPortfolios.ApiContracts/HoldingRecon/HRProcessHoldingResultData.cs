﻿namespace Actlogica.AlphaPortfolios.ApiContracts.HoldingRecon
{
    public class HRProcessHoldingResultData
    {
        public string CliendCode { get; set; }
        public string ClientName { get; set; }
        public string ClientSchemeCode { get; set; }
        public string InstrumentCode { get; set; }
        public string InstrumentName { get; set; }
        public string ISINCode { get; set; }
        public double PortfolioHolding { get; set; }
        public double ReportedCustFileHolding { get; set; }
        public double ReportedFAFileHolding { get; set; }
        public string ProcessingStatus { get; set; }
    }
}
