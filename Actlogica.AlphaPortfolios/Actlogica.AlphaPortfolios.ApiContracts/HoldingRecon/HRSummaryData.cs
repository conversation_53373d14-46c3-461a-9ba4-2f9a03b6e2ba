﻿namespace Actlogica.AlphaPortfolios.ApiContracts.HoldingRecon
{
    public class HRSummaryData
    {
        public string ClientCode { get; set; }
        public string ClientName { get; set; }
        public string Strategy { get; set; }
        public string Model { get; set; }
        public int ALFAMatchCount { get; set; }
        public int ALFANotMatchCount { get; set; }
        public int ALCUMatchCount { get; set; }
        public int ALCUNotMatchCount { get; set; }
        public int FACUMatchCount { get; set; }
        public int FACUNotMatchCount { get; set; }

    }
}
