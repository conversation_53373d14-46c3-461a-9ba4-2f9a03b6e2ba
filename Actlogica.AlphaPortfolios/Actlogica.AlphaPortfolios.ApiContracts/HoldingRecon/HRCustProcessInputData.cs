﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation;
using Actlogica.AlphaPortfolios.Utils.Dates;
using Actlogica.AlphaPortfolios.Utils.Double;

namespace Actlogica.AlphaPortfolios.ApiContracts.HoldingRecon
{
    public class HRCustProcessInputData
    {
        public DateTime HoldingDate { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Assetclass { get; set; }
        public string Symbolcode { get; set; }
        public string Symbolname { get; set; }
        public string Isin { get; set; }
        public string Position { get; set; }
        public double Quantity { get; set; }
        public double Unitcost { get; set; }
        public double Totalcost { get; set; }
        public double Unitprice { get; set; }
        public double Accruedincome { get; set; }
        public double Marketvalue { get; set; }
        public double AssetsPercentage { get; set; }
        public double Totalportfoliovalue { get; set; }
        public double Gainloss { get; set; }
        public double GainlossPercentage { get; set; }
        public string Custody { get; set; }
        public string Sectordescription { get; set; }
        public string Dimension { get; set; }
        public double Sharecapital { get; set; }
        public double Facevalue { get; set; }
        public double Market { get; set; }
        public double HoldingasPercentageofmarketcapital { get; set; }
        public double Receivable { get; set; }
        public double Payable { get; set; }
        public string Parentsymbol { get; set; }
        public string Optiontype { get; set; }
        public string Series { get; set; }
        public DateTime Expirydate { get; set; }
        public double Strikeprice { get; set; }
        public static HRCustProcessInputData FromFile(string csvLine)
        {
            try
            {
                string[] values = csvLine.Trim().Split(',');
                if (values.Length != 32) throw new ArgumentException($"Invalid Row found {nameof(csvLine)}");
                HRCustProcessInputData dailyValues = new()
                {
                    HoldingDate = values[0].ToDate(),
                    Code = values[1],
                    Name = values[2],
                    Assetclass = values[3],
                    Symbolcode = values[4],
                    Symbolname = values[5],
                    Isin = values[6],
                    Position = values[7],
                    Quantity= values[8].ToDouble(),
                    Unitcost= values[9].ToDouble(),
                    Totalcost= values[10].ToDouble(),
                    Unitprice= values[11].ToDouble(),
                    Accruedincome= values[12].ToDouble(),
                    Marketvalue= values[13].ToDouble(),
                    AssetsPercentage= values[14].ToDouble(),
                    Totalportfoliovalue= values[15].ToDouble(),
                    Gainloss= values[16].ToDouble(),
                    GainlossPercentage= values[17].ToDouble(),
                    Custody= values[18],
                    Sectordescription= values[19],
                    Dimension= values[20],
                    Sharecapital= values[21].ToDouble(),
                    Facevalue= values[22].ToDouble(),
                    Market= values[23].ToDouble(),
                    HoldingasPercentageofmarketcapital= values[24].ToDouble(), 
                    Receivable= values[25].ToDouble(),
                    Payable= values[26].ToDouble(),
                    Parentsymbol = values[27],
                    Optiontype = values[28],
                    Series= values[29],
                    Expirydate= values[30].ToDate(),
                    Strikeprice= values[31].ToDouble()
                };
                return dailyValues;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw new Exception();
            }
        }
    }
}
