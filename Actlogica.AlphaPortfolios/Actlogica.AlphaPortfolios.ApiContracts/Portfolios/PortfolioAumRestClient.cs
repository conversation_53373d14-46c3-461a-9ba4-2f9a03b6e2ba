using System;
using Newtonsoft.Json;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
public class PortfolioAumRestClient
{
    [JsonProperty("cash")]
    public double CashBalance { get; set; }

    [JsonProperty("market_value")]
    public double MarketValue { get; set; }

    [JsonProperty("nav")]
    public double Nav { get; set; }

    [JsonProperty("change")]
    public double Change { get; set; }

    [JsonProperty("total_aum")]
    public double InvestedCapital { get; set; }

    [JsonProperty("units")]
    public double Units { get; set; }

    [JsonProperty("net_cash_flows")]
    public double NetCashFlow { get; set; }
    
    [JsonProperty("date")]
    public DateTime Date { get; set; }

}