
using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class EditPortfolioNomineeDetailsBody
    {
        [Required]
        public string Id { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string NomineeName { get; set; }
        public bool? IsMinor { get; set; }
        public DateTime? NomineeDOB { get; set; }
        public NomineeRelationship? Relationship { get; set; }

        [Range(0, 100)]
        public double? NomineeSharePercentage { get; set; }

        [StringLength(100)]
        public string NomineeNo { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string GuardianName { get; set; }

        [StringLength(10, MinimumLength = 10)]
        public string GuardianPAN { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string GuardianAddress { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string TPAName { get; set; }

    }

}