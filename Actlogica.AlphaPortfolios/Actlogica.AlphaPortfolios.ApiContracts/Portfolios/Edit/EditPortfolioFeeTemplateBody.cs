

using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Fees;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class EditPortfolioFeeTemplateBody
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public double FixedFeePercentage { get; set; }
        public FixedFeeFrequency? FixedFeeFrequency { get; set; }
        public PerformanceFeeFrequency? PerformanceFeeFrequency { get; set; }
        public double PerformanceFeeSharingPercentage { get; set; }
        public double PerformanceFeeHurdleRate { get; set; }
        public DateTime AppliedFromDate { get; set; }
        public DateTime AppliedToDate { get; set; }
        public string ApprovedBy { get; set; }

    }

}
