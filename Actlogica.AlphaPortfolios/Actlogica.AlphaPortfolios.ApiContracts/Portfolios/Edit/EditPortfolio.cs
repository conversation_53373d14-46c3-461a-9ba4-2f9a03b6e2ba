using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios.Create;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class EditPortfolio
    {

        [Required]
        public EditPortfolioBody Portfolio { set; get; }

        [Required]
        public IEnumerable<EditPortfolioNomineeDetailsBody> PortfolioNomineeDetailsBody { set; get; }

        [Required]
        public IEnumerable<AddPortfolioNomineeDetailsBody> AddPortfolioNomineeDetailsBody { set; get; }

        public EditClientBrokerBody Broker { set; get; }

        [Required]
        public EditClientBankBody ClientBankBody { set; get; }

        [Required]
        public EditPortfolioTransactionsPreferenceBody PortfolioTransactionsPreference { set; get; }

        public EditPortfolioFeeTemplateBody PortfolioFeeTemplateBody { set; get; }

        [Required]
        public IEnumerable<EditPortfolioRMDetailsBody> PortfolioRMDetail { set; get; }

        [Required]
        public IEnumerable<AddPortfolioRMDetailsBody> AddPortfolioRMDetail { set; get; }

        [Required]
        public EditPortfolioPreferenceBody PortfolioPreference { set; get; }
        public string DistributorMasterId { set; get; }
        public EditPortfolioDistributorSharing EditPortfolioDistributorSharing{ set; get; }


    }

}