using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class EditPortfolioRMDetailsBody
    {
        [Required]
        public string Id { get; set; }

        [StringLength(200, MinimumLength = 1)]
        public string RMName { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string UserID { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public string RMType { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string Branch { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string BusinessUnit { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string Introducer { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string Consultant { get; set; }
        public string RMHierarchy { get; set; }    

    }

}