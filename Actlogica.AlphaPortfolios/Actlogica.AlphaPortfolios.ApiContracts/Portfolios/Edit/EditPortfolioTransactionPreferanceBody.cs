

using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class EditPortfolioTransactionsPreferenceBody
    {
        public bool? FixedIncome { get; set; }

        public bool? Equity { get; set; }
        public bool? MutualFund { get; set; }
        public bool? IPO { get; set; }
        public bool? Derivative { get; set; }
        public bool? Others { get; set; }

    }

}