

using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class EditPortfolioBody
    {
        
        [StringLength(50, MinimumLength = 3)]
        public string FAAccountNo { get; set; }

        [Required]
        public PortfolioType PortfolioType { get; set; }

        [Required]
        public AccountStatus AccountStatus { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string StockSettlementMode { get; set; }


    }

}
