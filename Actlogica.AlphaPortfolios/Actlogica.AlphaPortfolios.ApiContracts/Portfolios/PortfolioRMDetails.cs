
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Dynamic;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class PortfolioRMDetail
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string PortfolioId { get; set; }

        public string RMName { get; set; }

        public string UserID { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        public string RMType { get; set; }

        public string Branch { get; set; }

        public string BusinessUnit { get; set; }

        public string Introducer { get; set; }

        public string Consultant { get; set; }
        public string RMHierarchy { get; set ;}
        public string DistributorName { get; set; }
        public string DistributorRMName { get; set; }
        public string DistributorRMHierarchy { get; set; }
        public DateTime ValidFromDate { get; set; }
        public DateTime ValidToDate { get; set; }

    }

}