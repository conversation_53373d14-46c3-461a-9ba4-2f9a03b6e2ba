﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.CorporateActions;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class PortfolioReceivable
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string ClientId { get; set; }
		public string PortfolioId { get; set; }
		public string InvestmentId { get; set; }
		public string Isin { get; set; }
		public string Symbol { get; set; }
		public string InvestmentName { get; set; }
		public string Exchange { get; set; }
		public CorporateActionType CorporateActionType { get; set; }
		public TransactionType TransactionType { get; set; }
		public TransactionSubType TransactionSubType { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public DateTime CgtDate { get; set; }
		public double Amount { get; set; }
		public double Quantity { get; set; }
		public double Price { get; set; }
		public ReceivableStatusType ReceivableStatus { get; set; }
		public string Remarks { get; set; }
	}
}
