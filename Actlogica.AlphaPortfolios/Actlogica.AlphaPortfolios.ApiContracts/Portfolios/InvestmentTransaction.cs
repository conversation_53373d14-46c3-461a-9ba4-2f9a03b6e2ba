﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class InvestmentTransaction
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string InvestmentId { get; set; }
		public string PortfolioId { get; set; }
		public string ClientId { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime SettlementDate { get; set; }
		public DateTime CGTDate { get; set; }
		public double Quantity { get; set; }
		public double UnrealisedHolding { get; set; }
		public double CurrentHolding { get; set; }
		public double Price { get; set; }
		public double MarketRate { get; set; }
		public double Amount { get; set; }
		public double Brokerage { get; set; }
		public double ServiceTax { get; set; }
		public double SttAmount { get; set; }
		public double TurnTax { get; set; }
		public double OtherTax { get; set; }
		public TransactionType Type { get; set; }
		public TransactionSubType SubType { get; set; }
		public string Symbol { get; set; }
		public string Exchange { get; set; }
		public string Isin { get; set; }
		public string Name { get; set; }
		public string SecurityType { get; set; }
		public string SecuritySubType { get; set; }
		public string ClientOrderEntryId { get; set; }
		public string MFFolio { get; set; }
		public bool IsMutualFund { get; set; }
		public string Currency { get; set; }
		public double CurrencyConversionRate { get; set; }
		public double AcquisitionRate { get; set; }
	}
}
