
using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios.Create
{
    public class AddPortfolioNomineeDetailsBody
    {

        [StringLength(100, MinimumLength = 3)]
        public string NomineeName { get; set; }
        public bool IsMinor { get; set; }
        public DateTime? NomineeDOB { get; set; }
        public NomineeRelationship? Relationship { get; set; }
        public string NomineeSharePercentage { get; set; }
        public string NomineeNo { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string GuardianName { get; set; }

        [StringLength(10, MinimumLength = 10)]
        public string GuardianPAN { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string GuardianAddress { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string TPAName { get; set; }

    }

}