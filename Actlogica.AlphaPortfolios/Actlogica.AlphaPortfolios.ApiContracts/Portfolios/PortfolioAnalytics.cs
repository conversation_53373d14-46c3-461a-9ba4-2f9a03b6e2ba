﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class PortfolioAnalytics
	{
		public string InvestmentId { get; set; }
		public string PortfolioId { get; set; }
		public string ClientId { get; set; }
		public string ModelPortfolioId { get; set; }
		public DateTime AsAtDate { get; set; }
		public string AggregateType { get; set; }
		public string Isin { get; set; }
		public string Symbol { get; set; }
		public string MfRtaCode { get; set; }
		public string Series { get; set; }
		public string AmfiCode { get; set; }
		public string Exchange { get; set; }
		public string InvestmentName { get; set; }
		public string AmcName { get; set; }
		public string LogoImgUrl { get; set; }
		public string AssetClass { get; set; }
		public string AssetType { get; set; }
		public string FundClass { get; set; }
		public string CreditRating { get; set; }
		public string IndexName { get; set; }
		public string Industry { get; set; }
		public string MarketCap { get; set; }
		public double ClosePrice { get; set; }
		public DateTime ClosePriceDate { get; set; }
		public double ClosePriceChange { get; set; }
		public double AbsoluteReturnPercentage { get; set; }
		public double AbsoluteReturnValue { get; set; }
		public double AveragePurchasePrice { get; set; }
		public double AllUnits { get; set; }
		public double LongTermUnits { get; set; }
		public double ShortTermUnits { get; set; }
		public double BenchmarkReturn { get; set; }
		public double Cagr { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double Withdrawals { get; set; }
		public double MarketValue { get; set; }
		public double Weight { get; set; }
		public double MarketValueChange { get; set; }
		public double MarketValueChangePercentage { get; set; }
		public double DividendPaid { get; set; }
		public double DividendReinvested { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double Xirr { get; set; }
		public double XirrUnrealised { get; set; }
    public double Twrr { get; set; }
		public DateTime FirstDateOfInvestment { get; set; }
		public DateTime DateOfMaturity { get; set; }
		public double GrowthAt10000 { get; set; }
		public double GrowthAt10000Percentage { get; set; }
		public string UniqueKey { get; set; }
		public double ComparePrice { get; set; }
		public DateTime ComparePriceDate { get; set; }
		public int NumberOfInvestments { get; set; }
	}
}
