﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
  public class PortfolioCashPosition
  {
    public string PortfolioId { get; set; }
    public double CurrentCashBalance { get; set; }
    public double AvailableCashBalance { get; set; }
    public double SubmittedBuyAmount { get; set; }
    public double SubmittedSellAmount { get; set; }
    public double HeldBuyAmount { get; set; }
    public double HeldSellamount { get; set; }
    public double Receivables { get; set; }
    public double Payables { get; set; }
    public double AssetsLiabilities { get; set; }
  }
}
