﻿using System;
using System.Collections.Generic;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using Actlogica.AlphaPortfolios.ApiContracts.Fees;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class Portfolio
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }

		public string ClientId { get; set; }
		public virtual Client Client { get; set; }

		public string ClientName { get; set; }
		public AccountStatus AccountStatus { get; set; }
		public string ModelId { get; set; }

		public string ClientStrategyCode { get; set; }

		public string CustodianPortfolioCode { get; set; }

		public string FAAccountNo { get; set; }
		public virtual Model Model { get; set; }
		public string Name { get; set; }
		public PortfolioType PortfolioType { get; set; }
		public DateTime StartDate { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double AnnualReturnIrr { get; set; }
		public double AnnualReturnIrrUnrealised { get; set; }
		public double TwrrSinceInception { get; set; }
		public double AnnualPerformanceTwrr { get; set; }
		public double CurrentCashBalance { get; set; }
		public double UnsettledBuyOrderAmount { get; set; }
		public double UnsettledCashAmount { get; set; }
		public PortfolioCashPosition CashPosition { get; set; }
		public virtual ICollection<Investment> Investments { get; set; }

		public ModeOfOperation ModeOfOperation { get; set; }

		public TradingMode TradingMode { get; set; }

		public FundSettlementMode FundSettlementMode { get; set; }

		public string StockSettlementMode { get; set; }

		public bool POAOnBank { get; set; }
		public bool POAOnDemat { get; set; }
		public bool POAOnMF { get; set; }
		public IEnumerable<PortfolioNomineeDetails> PortfolioNomineeDetails { get; set; }

		public PortfolioPreference PortfolioPreference { get; set; }

		public IEnumerable<PortfolioRMDetail> PortfolioRMDetails { get; set; }

		public virtual PortfolioTransactionsPrefernce PortfolioTransactionsPreference { get; set; }
		public PortfolioFeeTemplate PortfolioFeeTemplate { get; set; }

		public ClientBank ClientBank { get; set; }

		public ClientBroker ClientBroker { get; set; }

		public ClientCustodian ClientCustodian { get; set; }
		public int BenchmarkIndexCode { get; set; }
		public string BenchmarkIndexName { get; set; }
		public PortfolioDistributorSharingConfigurations PortfolioDistributorSharingConfiguration { get; set; }
		public string DistributorMasterId { get; set; }

	}

}
