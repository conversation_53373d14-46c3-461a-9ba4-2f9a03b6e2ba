
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class PortfolioTransactionsPrefernce
    {

        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string PortfolioId { get; set; }
        public bool? FixedIncome { get; set; }
        public bool? Equity { get; set; }
        public bool? MutualFund { get; set; }
        public bool? IPO { get; set; }
        public bool? Derivative { get; set; }
        public bool? Others { get; set; }
        public bool? UnListedEquity { get; set; }
        public bool? DebtInstruments { get; set; }
        public DateTime ValidFromDate { get; set; }
        public DateTime ValidToDate { get; set; }

    }
}
