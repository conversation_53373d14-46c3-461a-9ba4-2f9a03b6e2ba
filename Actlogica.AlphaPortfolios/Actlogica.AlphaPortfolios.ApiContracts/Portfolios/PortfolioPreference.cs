
using System;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
    public class PortfolioPreference
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }

        public string PortfolioId { get; set; }

        public SendPortfolioReportBy? SendClientReport { get; set; }

        public SendAlertBy? SendAlert { get; set; }

        public string SendTo { get; set; }

        public AcceptOrderRequest? AcceptOrderRequest { get; set; }

        public FinancialMonthStartfrom? FinancialMonthStartfrom { get; set; }

    }

}
