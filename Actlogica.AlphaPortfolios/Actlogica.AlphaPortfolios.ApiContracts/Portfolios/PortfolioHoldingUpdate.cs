﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class PortfolioHoldingUpdate
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string FAName { get; set; }
		public string FilePath { get; set; }
		public string UploadedBy { get; set; }
		public string Status { get; set; }
		public DateTime UploadDate { get; set; }
		public int NoOfRecords { get; set; }

		public string UploadType { get; set; }
	}
}
