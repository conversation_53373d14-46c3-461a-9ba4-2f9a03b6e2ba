﻿using Actlogica.AlphaPortfolios.ApiContracts.DirectEquity;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class ModelPortfolio
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string Name { get; set; }
		public DateTime StartDate { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double AnnualReturnIrr { get; set; }
		public double TwrrSinceInception { get; set; }
		public double AnnualPerformanceTwrr { get; set; }
		public double CurrentCashBalance { get; set; }
		public string ModelId { get; set; }
		public Model StrategyModel { get; set; }
		public IEnumerable<Investment> Investments { get; set; }
	}
}
