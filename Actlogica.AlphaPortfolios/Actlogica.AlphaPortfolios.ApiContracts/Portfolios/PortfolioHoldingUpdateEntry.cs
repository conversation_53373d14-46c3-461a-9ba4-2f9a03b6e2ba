﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class PortfolioHoldingUpdateEntry
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string FAAccountNo { get; set; }
		public string ClientName { get; set; }
		public string AssetClass { get; set; }
		public string Identifier { get; set; }
		public string SecurityName { get; set; }
		public string Isin { get; set; }
		public string Sector { get; set; }
		public string MarketCap { get; set; }
		public double Quantity { get; set; }
		public double AveragePrice { get; set; }
		public double TotalCost { get; set; }
		public double CurrentPrice { get; set; }
		public double AccruedIncome { get; set; }
		public double MarketValue { get; set; }
		public double Weight { get; set; }
		public double GainLoss { get; set; }
		public double GainLossPct { get; set; }
		public double Receivable { get; set; }
		public double Payable { get; set; }
		public DateTime HoldingDate { get; set; }
		public int NoOfRecords { get; set; }
		public string Status { get; set; }
		public string InvestmentId { get; set; }
		public string PortfolioHoldingUpdateId { get; set; }
	}
}
