﻿using Actlogica.AlphaPortfolios.ApiContracts.Orders;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class AdditionalCapitalDeploymentModel
	{
		public string PortfolioId { get; set; }
		public double CapitalToDeploy { get; set; }
		public IEnumerable<ClientOrderEntry> Orders { get; set; }
		public bool ShouldCreateCapitalRegisterEntry { get; set; }
		public double NewCapital { get; set; }
	}
}
