﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Portfolios
{
	public class Investment
  {
		public string Id { get; set; }
		public string PortfolioId { get; set; }
		public string ModelportfolioId { get; set; }
		public string ClientId { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string Symbol { get; set; }
		public string Name { get; set; }
		public string Isin { get; set; }
		public string Exchange { get; set; }
		public double CurrentPrice { get; set; }
		public DateTime CurrentPriceDate { get; set; }
		public double CurrentHolding { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double AveragePrice { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double Dividends { get; set; }
		public double IrrSinceInception { get; set; }
		public double IrrCurrent { get; set; }
		public DateTime FirstTransactionDate { get; set; }
		public DateTime LastTransactionDate { get; set; }
		public string MarketCap { get; set; }
		public string Sector { get; set; }
		public string FundClass { get; set; }
		public string Category { get; set; }
		public string AssetClass { get; set; }
    public string AmfiCode { get; set; }
    public double Weight { get; set; }
		public string Rating { get; set; }
		public string SecuritySubType { get; set; }
		public SecurityType SecurityType { get; set; }
		public virtual IEnumerable<InvestmentTransaction> Transactions { get; set; }
		public double ReceivableQuantity { get; set; }
		public double ReceivableAmount { get; set; }
	}
}
