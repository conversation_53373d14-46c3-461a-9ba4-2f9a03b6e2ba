﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientBroker
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string CPCode { get; set; }
		public string TradingAccountNumber { get; set; }
		public string PortfolioId { get; set; }
		public string BrokerId { get; set; }
		public string ClientId { get; set; }
		public string ClientCustodianId { get; set; }
		public virtual Broker Broker { get; set; }
	}
}
