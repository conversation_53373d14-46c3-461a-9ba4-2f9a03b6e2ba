﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
    public class RestrictedStocksForClient
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string RestrictedSecurityIdentifier { get; set; }
        public string AlternativeSecurityIdentifier { get; set; }
        public string Rationale { get; set; }
        public string SecurityType { get; set; }
        public string ClientId { get; set; }
        public virtual Client Client { get; set; }
        public string ExchangeRestrictedSecurity { get; set; }
        public string ExchangeAlternativeSecurity { get; set; }
        public string IsinRestrictedSecurity { get; set; }
        public string IsinAlternativeSecurity { get; set; }
        public string Status { get; set; }
    }
}
