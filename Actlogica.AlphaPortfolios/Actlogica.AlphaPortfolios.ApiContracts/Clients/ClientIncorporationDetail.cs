﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientIncorporationDetail
	{
		public string ClientId { get; set; }
		public DateTime DateOfIncorporation { get; set; }
		public DateTime DateOfCommencementOfBusiness { get; set; }
		public string CityOfIncorporation { get; set; }
		public string CountryOfIncorporation { get; set; }
		public string TINOfIssuingCountry { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }
	}
}
