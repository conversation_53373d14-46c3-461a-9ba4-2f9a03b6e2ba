using System;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
    public class PortfolioForeignBankDetails
    {
        public string PortfolioId { get; set; }
        public virtual Portfolio Portfolio { get; set; }
        public string IntermittentBankAccount { get; set; }
        public string NOSTRODetails { get; set; }
        public string SenderCorrespondentBankDetails { get; set; }
        public string ReceiverCorrespondentBankDetails { get; set; }
        public string SwiftCode { get; set; }
        public string Currency { get; set; }
        public DateTime ValidFrom { get; set; }
        public DateTime ValidTo { get; set; }
    }
}
