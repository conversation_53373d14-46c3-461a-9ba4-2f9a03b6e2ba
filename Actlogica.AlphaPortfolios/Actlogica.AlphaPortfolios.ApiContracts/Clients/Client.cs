﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class Client
	{
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string ClientCode { get; set; }
		public string FirstName { get; set; }
		public string MiddleName { get; set; }
		public string LastName { get; set; }
		public DateTime DateOfBirth { get; set; }
		public string Pan { get; set; }
		public string Aadhar { get; set; }
		public string Email { get; set; }
		public string Phone { get; set; }
		public string ClientBankId { get; set; }
		public string DisplayName { get; set; }
		public ClientType ClientType { get; set; }
		public DomicileType Domicile { get; set; }
		public string BseStarUcc { get; set; }
		public ClientTitle Title { get; set; }

		public string UserName { get; set; }

		public string UserId { get; set; }
		public ClientPersonalDetail ClientPersonalDetail { get; set; }
		public IEnumerable<ClientBank> BankAccounts { get; set; } = new List<ClientBank>();
		public ClientContactDetail ClientContactDetail { get; set; }
		public string CKYCNo { get; set; }
		public string FullName { get; set; }
		public string NameAsPerPan { get; set; }
		public bool IsAccreditedInvestor { get; set; }
		public virtual ClientFamilyDetail ClientFamilyDetail { get; set; }
		public virtual ICollection<ClientIdentity> ClientIdentities { get; set; }
		public virtual ClientDueDiligence ClientDueDiligence { get; set; }
		public virtual ClientAccreditedInvestorDetail ClientAccreditedInvestorDetail { get; set; }
		public virtual ClientOverseasDetail ClientOverseasDetail { get; set; }
		public virtual ClientIncorporationDetail ClientIncorporationDetail { get; set; }
		public virtual PortfolioHolder PortfolioHolder { get; set; }
	}
}
