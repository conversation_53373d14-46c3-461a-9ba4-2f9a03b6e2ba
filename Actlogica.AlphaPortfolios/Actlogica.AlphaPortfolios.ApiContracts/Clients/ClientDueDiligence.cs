﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientDueDiligence
	{
		public string ClientId { get; set; }
		public string AnnualIncome { get; set; }
		public double NetworthInRs { get; set; }
		public double NetworthAsOnDate { get; set; }
		public string PepRPep { get; set; }
		public bool PEPDeclarationReceived { get; set; }
		public bool AMLCheckDone { get; set; }
		public string AMLCheckResult { get; set; }
		public DateTime AMLDoneDate { get; set; }
		public string AMLReviewPeriodicity { get; set; }
		public DateTime NextAMLReviewDate { get; set; }
		public bool RiskProfilingDone { get; set; }
		public string ClientRiskCategory { get; set; }
		public bool AdditionalDueDiligenceRequired { get; set; }
		public string AdditionalDueDiligenceStatus { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }
	}
}
