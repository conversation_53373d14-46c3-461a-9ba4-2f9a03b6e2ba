﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientOverseasDetail
	{
		public string ClientId { get; set; }
		public string Country { get; set; }
		public string TaxIdentificationNo { get; set; }
		public string IdentificationType { get; set; }
		public string ISO3166Code { get; set; }
		public string OverseasAddressline1 { get; set; }
		public string OverseasAddressline2 { get; set; }
		public string OverseasAddressline3 { get; set; }
		public string OverseasCity { get; set; }
		public string OverseasDistrict { get; set; }
		public string OverseasState { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }
	}
}
