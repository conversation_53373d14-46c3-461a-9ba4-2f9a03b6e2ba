using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
    public class EditClientContactDetailsBody
    {
        
        [Required]
        [StringLength(150, MinimumLength = 1)]
        public string AddressLine1 { get; set; }

        [StringLength(150, MinimumLength = 1)]
        public string AddressLine2 { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 1)]
        public string City { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string State { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string Country { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 6)]
        public string PinCode { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string MobileCountryCode { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string MobileSecondary { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        [EmailAddress(ErrorMessage = "Invalid Email")]
        public string EmailSecondary { get; set; }
    }

}