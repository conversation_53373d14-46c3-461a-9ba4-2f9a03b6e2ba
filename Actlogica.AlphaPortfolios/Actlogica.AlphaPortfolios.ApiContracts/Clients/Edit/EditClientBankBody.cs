using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class EditClientBankBody
	{

		[Required]
		public string Id { get; set; }

		[Required]
		[StringLength(250)]
		public string Name { get; set; }

		[Required]
		[StringLength(250)]
		public string AddressLine1 { get; set; }

		[StringLength(250)]
		public string AddressLine2 { get; set; }

		[Required]
		[StringLength(100)]
		public string City { get; set; }

		[Required]
		[StringLength(100)]
		public string State { get; set; }

		[Required]
		[StringLength(20)]
		public string Postcode { get; set; }

		[Required]
		[StringLength(200)]
		public string AccountName { get; set; }

		[Required]
		[StringLength(250)]
		public string AccountNumber { get; set; }

		[Required]
		[StringLength(100)]
		public string Ifsc { get; set; }

		[Required]
		[StringLength(250)]
		public string Micr { get; set; }

		[Required]
		public BankAccountType BankAccountType { get; set; }

		[Required]
		[StringLength(100)]
		public string BranchName { get; set; }

		public Currency? Currency { get; set; }

		[StringLength(100)]
		public string SwiftCode { get; set; }

		[Required]
		public AccountStatus AccountStatus { get; set; }

		[StringLength(100)]
		public string SecondHolderName { get; set; }

		[StringLength(100)]
		public string ThirdHolderName { get; set; }

	}

}