

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients.AddClients;


namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
    public class EditClient
    {
        [Required]
        public EditClientDetails Client { get; set; }

        [Required]
        public EditClientPersonalDetailsBody ClientPersonalDetail { get; set; }

        [Required]
        public EditClientContactDetailsBody ClientContactDetail { get; set; }

        [Required]
        public IEnumerable<EditClientBankBody> ClientBanks { get; set; }

        [Required]
        public IEnumerable<AddClientBankBody> AddClientBanks { get; set; }
    }


}