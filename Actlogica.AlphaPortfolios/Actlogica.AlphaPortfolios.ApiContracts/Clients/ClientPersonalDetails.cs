using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Microsoft.VisualBasic;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientPersonalDetail
	{
		public string ClientId { get; set; }

		public MaritalStatus? MaritalStatus { get; set; }

		public ClientOccupation? Occupation { get; set; }

		public Nationality? Nationality { get; set; }

		public string FamilyOrGroup { get; set; }

		public bool? HeadOfFamily { get; set; }

		public bool KYCValid { get; set; }

		public ClientTaxStatus TaxStatus { get; set; }

		public ClientCategory Category { get; set; }

		public Currency? ReportingCurreny { get; set; }

		public DateTime? AnniversaryDate { get; set; }
		public string Spouse { get; set; }
		public DateTime? SpouseDOB { get; set; }
		public ClientQualification? Qualification { get; set; }
		public ClientWorkExperience? WorkExpererience { get; set; }
		public string OrganizationName { get; set; }
		public string EmployerID { get; set; }
		public IndustryType? IndustryType { get; set; }
		public string AddressOrganisation { get; set; }

		public string PlaceOfBirth { get; set; }

		public string CountryOfBirth { get; set; }

		public ClientGrossAnnualIncome? GrossAnnualIncome { get; set; }

		public string SourceOfWealth { get; set; }

		public ClientEstimatedFinancialWealth? EstimatedFinancialWealth { get; set; }

		public bool PoliticalExposure { get; set; }

		public bool? Amlcertified { get; set; }

		public Gender Gender { get; set; }
		public string ResidentialStatus { get; set; }
		public bool IsPioOciCardHolder { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }
	}
}
