using System;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
    public class PortfolioCustodian
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string Name { get; set; }
        public string CustodyAccountNumber { get; set; }

        public string ClientStrategyCode { get; set; }
        public DpType DPType { get; set; }

        public string CustodianId { get; set; }

        public string ClientId { get; set; }

        public string PortfolioId { get; set; }
        public ModeOfHolding ModeofHolding { get; set; }

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

        public string SecondHolderName { get; set; }
        public string ThirdHolderName { get; set; }

        public string SecondHolderPAN { get; set; }

        public string ThirdHolderPAN { get; set; }
        public string SecondHolderPhone { get; set; }
        public string ThirdHolderPhone { get; set; }
        public string SecondHolderEmail { get; set; }
        public string ThirdHolderEmail { get; set; }
        public string MFUCCDemat { get; set; }
        public string MFUCCPhysical { get; set; }

    }
}
