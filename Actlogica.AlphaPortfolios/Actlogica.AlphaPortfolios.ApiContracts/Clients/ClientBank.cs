﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.CompilerServices;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientBank
	{
		
		public string Id { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string Name { get; set; }

		public string AddressLine1 { get; set; }

		public string AddressLine2 { get; set; }

		public string City { get; set; }

		public string State { get; set; }

		public string Postcode { get; set; }

		public string AccountName { get; set; }

		public AccountStatus AccountStatus { get; set; }

		public string AccountNumber { get; set; }

		public string Ifsc { get; set; }

		public string Micr { get; set; }
		public BankAccountType BankAccountType { get; set; }

		public string ClientId { get; set; }
		public string PortfolioId { get; set; }
		public string Accountlink { get; set; }

		public string BranchName { get; set; }

		public Currency? Currency { get; set; }

		public string SwiftCode { get; set; }
		public DateTime? FromDate { get; set; }
		public DateTime? ToDate { get; set; }

		public string SecondHolderName { get; set; }
		public string ThirdHolderName { get; set; }
        public bool ForeignBankDetails { get; set; }
		public string BankAccountCategory { get; set; }
	}
}
