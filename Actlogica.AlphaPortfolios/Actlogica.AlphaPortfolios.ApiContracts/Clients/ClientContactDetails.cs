
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientContactDetail
	{
		public string ClientId { get; set; }
		public string AddressLine1 { get; set; }

		public string AddressLine2 { get; set; }

		public string City { get; set; }

		public string State { get; set; }

		public string Country { get; set; }

		public string PinCode { get; set; }

		public string MobileCountryCode { get; set; }

		public string MobileSecondary { get; set; }

		public string EmailSecondary { get; set; }
		public string AddressType { get; set; }
		public string AddressTypeIndividual { get; set; }
		public bool UsedForCommunication { get; set; }
		public string AddressLine3 { get; set; }
		public string District { get; set; }
		public string GSTNo { get; set; }
		public string GSTNoPlaceOfSupply { get; set; }
		public string TelephoneOffice { get; set; }
		public string TelephoneResidence { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }
	}

}

