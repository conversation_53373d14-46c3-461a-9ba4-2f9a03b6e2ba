﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class ClientFamilyDetail
	{
		public string ClientId { get; set; }
		public string Relationtype { get; set; }
		public string FatherSpouseTitle { get; set; }
		public string FatherSpouseFirstName { get; set; }
		public string FatherSpouseMiddleName { get; set; }
		public string FatherSpouseLastName { get; set; }
		public string MotherTitle { get; set; }
		public string MotherFirstName { get; set; }
		public string MotherMiddleName { get; set; }
		public string MotherLastName { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime ValidToDate { get; set; }
	}
}
