﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
    public class BRStatementProcessResultData
    {
        public string Id { get; set; }
        public string ClientCode { get; set; }
        public string ClientName { get; set; }
        public string BankAccountNumber { get; set; }
        public string Branch { get; set; }
        public DateTime? TransactionDate { get; set; }
        public DateTime? PostingDate { get; set; }
        public DateTime? PortfolioCashLedgerDate { get; set; }
        public double BankInputAmount { get; set; }
        public double PortfolioCashLedgerAmount { get; set; }
        public string? TransactionTypeBank { get; set; }
        public string? TransactionTypeLedger { get; set; }
        public bool FoundInBank { get; set; }
        public bool FoundInPortfolioCashLedger { get; set; }
        public string? MatchStatus { get; set; }
        public string? BRProcessId { get; set; }
        public string? BRProcessInputDataId { get; set; }
        public string? PortfolioCashLedgerId { get; set; }
        public string? PortfolioId { get; set; }
        public string? StrategyCode { get; set; }
        public string? StrategyModelId { get; set; }
    }
}
