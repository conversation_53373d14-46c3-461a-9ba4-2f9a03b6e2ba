﻿using System;
using System.Linq;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
    public class BRAdjustmentData
    {
        public string ClientStrategyCode { get; set; }
        public string BrokerCode { get; set; }
        public string ISIN { get; set; }
        public string Exchange { get; set; }
        public string TransactionType { get; set; }
        public string TransactionSubType { get; set; }

        public DateTime AcquisitionDate { get; set; }
        public DateTime SettlementDate { get; set; }
        public double Quantity { get; set; }
        public double Nav { get; set; }
        public double Brokerage { get; set; }
        public double ServiceTax { get; set; }
        public string SettlementDateFlag { get; set; }
        public double MarketRate{ get; set; }
        public string CashSymbol  { get; set; }
        public double STTAmount { get; set; }
        public double AccruedInterest { get; set; }
        public string BlockRef{ get; set; }
        public string TransRef { get; set; }
        public string Remarks { get; set; }
        public bool IsCapitalEntry { get; set; }
        public string CapitalLedgerTransactionSubType { get; set; }



        public static BRAdjustmentData FromFile(string csvLine)
        {
            string[] values = csvLine.Trim().Split(',');
            if (values.Length != 19) throw new ArgumentException($"Invalid Row found {nameof(csvLine)}");
            var adjRemarks = new AdjustmentRemarkList();
            var adjRemark = adjRemarks.AdjustmentRemarks.FirstOrDefault(x => x.Code == values[4]);
            if (adjRemark == null) throw new ApplicationException($"Invalid Adjustment transaction type {values[4]}");
            BRAdjustmentData dailyValues = new()
            {
                BrokerCode = values[0],
                ClientStrategyCode = values[1],
                ISIN = values[2],
                Exchange = values[3],
                TransactionType = adjRemark.LedgerTransactionType,
                TransactionSubType= adjRemark.LedgerTransactionSubType,
                CapitalLedgerTransactionSubType = adjRemark.CapitalLedgerTransactionSubType,
                IsCapitalEntry = adjRemark.IsCapitalEntry,
                AcquisitionDate = Convert.ToDateTime(values[5]==string.Empty?DateTime.MinValue.ToString(): values[5]),
                SettlementDate = Convert.ToDateTime(values[6] == string.Empty ? DateTime.MinValue.ToString():values[6]),
                Quantity = Convert.ToDouble(values[7] == string.Empty ? "0" : values[7]),
                Nav = Convert.ToDouble(values[8] == string.Empty ? "0": values[8]),
                Brokerage = Convert.ToDouble(values[9] == string.Empty ? "0": values[9]),
                ServiceTax = Convert.ToDouble(values[10] == string.Empty ? "0":values[10]),
                SettlementDateFlag = values[11],
                MarketRate = Convert.ToDouble(values[12] == string.Empty ? "0": values[12]),
                CashSymbol = values[13],
                STTAmount = Convert.ToDouble(values[14] == string.Empty ? "0": values[14]),
                AccruedInterest = Convert.ToDouble(values[15] == string.Empty ? "0": values[15]),
                BlockRef = values[16],
                TransRef = values[17],
                Remarks = $"Recon - {values[18]}"               
            };
            return dailyValues;
        }
    }
}
