﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
  public class BankRecon_TransactionInputDate
  {
		public string Id { get; set; }
		public DateTime TransactionInputFileDate { get; set; }
		public DateTime TransactionDate { get; set; }
		public DateTime PostingDate { get; set; }
		public string TransactionType { get; set; }
		public string BankAccountNumber { get; set; }
		public string BankBranch { get; set; }
		public double Amount { get; set; }
		public string Description { get; set; }
		public string ClientCode { get; set; }
		public string ClientId { get; set; }
		public string BankReconProcessId { get; set; }
		public string ReconStatus { get; set; }
	}
}
