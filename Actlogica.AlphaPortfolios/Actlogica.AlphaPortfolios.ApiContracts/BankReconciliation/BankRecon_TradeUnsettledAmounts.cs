﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
  public class BankRecon_TradeUnsettledAmounts
  {
    public string ClientId { get; set; }
    public string PortfolioId { get; set; }
    public string ClientName { get; set; }
    public string PortfolioName { get; set; }
    public string ModelId { get; set; }
    public string ClientCode { get; set; }
    public string BankAccountNumber { get; set; }
    public string TransactionType { get; set; }
    public double TransactionAmount { get; set; }
    public DateTime SettlementDate { get; set; }
    public string ReconStatus { get; set; }

  }
}
