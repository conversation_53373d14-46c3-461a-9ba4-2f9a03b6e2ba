﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
    public class BRBankProcessInputData
    {
        public DateTime BalanceInputFileDate { get; set; }= DateTime.Now;
        public DateTime AsAtDate { get; set; } = DateTime.Now;
        public string BankAccountNumber { get; set; }
        public string StatusCode { get; set; }
        public string BankBranch { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; }
        public string ClientCode { get; set; }
        public string ClientId { get; set; }
        public string BankReconProcessId { get; set; }
        public static BRBankProcessInputData FromFile(string csvLine)
        {
            try
            {
                string[] values = csvLine.Trim().Split(' ');
                if (values.Length != 4) throw new ArgumentException($"Invalid Row found {nameof(csvLine)}");
                BRBankProcessInputData dailyValues = new()
                {
                    BankAccountNumber = values[0],
                    Amount = Convert.ToDecimal(values[1]),
                    StatusCode = values[2],
                    Description = values[3]
                };
                return dailyValues;
            }catch(Exception ex)
            {
                Console.WriteLine(ex.Message);
                throw ex;
            }
        }
    }
}
