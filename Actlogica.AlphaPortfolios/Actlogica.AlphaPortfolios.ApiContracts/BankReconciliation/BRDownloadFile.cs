﻿namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
    public class BRDownloadFile
    {
        public string BROKERCODE { get; set; }
        public string <PERSON><PERSON><PERSON><PERSON>CODE { get; set; }
        public string ISIN { get; set; }
        public string EXCHANGE { get; set; }
        public string TRANSACTIONTYPE { get; set; }
        public string ACQUISITIONDATE { get; set; }
        public string SE<PERSON><PERSON>MENTDATE { get; set; }
        public string QUANTITY { get; set; }
        public string GROSSPRICE { get; set; }
        public string BROKERAGE { get; set; }
        public string SERVICETAX { get; set; }
        public string SETTLEMENTDATEFLAG { get; set; }
        public string MARKETRATEASONSECURITYINDATE { get; set; }
        public string CASHSYMBOL { get; set; }
        public double STTAMOUNT { get; set; }
        public string ACCRUEDINTEREST { get; set; }
        public string BLOCKREF { get; set; }
        public string TRANSREF { get; set; }
        public string REMARKS { get; set; }

    }
}
