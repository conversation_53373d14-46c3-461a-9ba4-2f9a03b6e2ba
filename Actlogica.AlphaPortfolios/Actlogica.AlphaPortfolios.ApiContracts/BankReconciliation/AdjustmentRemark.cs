﻿using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
    public class AdjustmentRemark
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public string Relation { get; set; }
        public string LedgerTransactionType { get; set; }
        public string LedgerTransactionSubType { get; set; }
        public string CapitalLedgerTransactionSubType { get; set; } = string.Empty;

        public bool IsCapitalEntry { get; set; } = false;
    }
    public class AdjustmentRemarkList
    {
        public AdjustmentRemarkList()
        {
            AdjustmentRemarks = new List<AdjustmentRemark>
            {
                new AdjustmentRemark
                {
                    Code="CS+",
                    Description="For Corpus In",
                    Relation="Capital Register",
                    LedgerTransactionType="Credit",
                    LedgerTransactionSubType="InFlow",
                    CapitalLedgerTransactionSubType="CapitalIn",
                    IsCapitalEntry=true
                },
                new AdjustmentRemark
                {
                    Code="CS-",
                    Description="For Corpus Out",
                    Relation="Capital Register",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="OutFlow",
                    CapitalLedgerTransactionSubType="CapitalOut",
                    IsCapitalEntry=true

                },
                new AdjustmentRemark
                {
                    Code="BY-",
                    Description="For Buy",
                    Relation="MF",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Buy"
                },
                new AdjustmentRemark
                {
                    Code="MGF",
                    Description="For Management Fees",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Fees"
                },
                new AdjustmentRemark
                {
                    Code="TMF",
                    Description="For Tds On Management Fees",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Tds"
                },
                new AdjustmentRemark
                {
                    Code="MGE",
                    Description="For Other Expenses",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Other"
                },
                new AdjustmentRemark
                {
                    Code="CUS",
                    Description="For Custody Charges",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Charges"
                },
                new AdjustmentRemark
                {
                    Code="ELF",
                    Description="For Exit Load",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Charges"
                },
                new AdjustmentRemark
                {
                    Code="E01",
                    Description="For Stp Ft Fees",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Fees"
                },
                new AdjustmentRemark
                {
                    Code="E02",
                    Description="For Upfront Fees",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Fees"
                },
                new AdjustmentRemark
                {
                    Code="E03",
                    Description="For Audit Fees",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Fees"
                },
                new AdjustmentRemark
                {
                    Code="E04",
                    Description="For Account Opening Charges",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Charges"
                },
                new AdjustmentRemark
                {
                    Code="E05",
                    Description="For Bank Charges",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Charges"
                },
                new AdjustmentRemark
                {
                    Code="ENF",
                    Description="For Entry Load",
                    Relation="Expense",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Charges"
                },
                new AdjustmentRemark
                {
                    Code="BY-",
                    Description="For Purchase",
                    Relation="Trade",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Buy"
                },
                new AdjustmentRemark
                {
                    Code="SL+",
                    Description="For Redemption",
                    Relation="MF",
                    LedgerTransactionType="Credit",
                    LedgerTransactionSubType="Sell"
                },
                new AdjustmentRemark
                {
                    Code="RD0",
                    Description="For Reinvestment",
                    Relation="MF",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="DividendReinvestment"
                },
                new AdjustmentRemark
                {
                    Code="E37",
                    Description="Stamp Duty",
                    Relation="MF",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="Charges"
                },
                new AdjustmentRemark
                {
                    Code="OPI",
                    Description="For Security In",
                    Relation="MF",
                    LedgerTransactionType="Credit",
                    LedgerTransactionSubType="SecurityIn"
                },
                new AdjustmentRemark
                {
                    Code="OPO",
                    Description="For Securityout",
                    Relation="MF",
                    LedgerTransactionType="Debit",
                    LedgerTransactionSubType="SecurityOut"
                },
                new AdjustmentRemark
                {
                    Code="SY+",
                    Description="For Sale",
                    Relation="Trade",
                    LedgerTransactionType="Credit",
                    LedgerTransactionSubType="Sell"
                }

            };
        }
        public IEnumerable<AdjustmentRemark> AdjustmentRemarks { get; set; }
    }
}
