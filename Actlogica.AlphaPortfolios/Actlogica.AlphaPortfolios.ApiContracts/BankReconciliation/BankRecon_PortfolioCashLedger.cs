﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
  public class BankRecon_PortfolioCashLedger
  {
    public DateTime SettlementDate { get; set; }
    public string TransactionType { get; set; }
    public string AccountNumber { get; set; }
    public string PortfolioId { get; set; }
    public string ClientId { get; set; }
    public string ClientName { get; set; }
    public double Amount { get; set; }
    public string PortfolioName { get; set; }
    public string ModelId { get; set; }
    public string ClientCode { get; set; }
    public string ReconStatus { get; set; }
  }
}
