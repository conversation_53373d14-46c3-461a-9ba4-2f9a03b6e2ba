﻿using Actlogica.AlphaPortfolios.Utils.Dates;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation
{
    public class BRProcessInputData
    {
        public string Id { get; set; }
        public DateTime TransactionDate { get; set; }
        public DateTime TransactionInputFileDate { get; set; }


        public DateTime PostingDate { get; set; }
        public string TransactionType { get; set; }
        public string BankAccountNumber { get; set; }
        public string BankBranch { get; set; }
        public decimal Amount { get; set; }
        public string Description { get; set; }
        public string ClientCode { get; set; }
        public string ClientId { get; set; }
        public string BankReconProcessId { get; set; } = $"{Guid.NewGuid().ToString().Replace("-", string.Empty)}";
        public static BRProcessInputData FromFile(string csvLine)
        {
            try
            {
                string[] values = csvLine.Trim().Split(' ');
                if (values.Length != 8) throw new ArgumentException($"Invalid Column found {nameof(csvLine)}");
                BRProcessInputData dailyValues = new()
                {
                    BankAccountNumber = values[0],
                    BankBranch = values[1],
                    Amount = Convert.ToDecimal(values[2]),
                    TransactionType = values[3],
                    TransactionInputFileDate = values[4].ToDate(),
                    TransactionDate = values[5].ToDate(),
                    PostingDate = values[6].ToDate(),
                    Description = values[7]
                };
                return dailyValues;
            }catch(Exception ex) 
            {
                Console.WriteLine(ex.Message);
                throw new Exception(ex.Message); 
            }
        }

    }
}
