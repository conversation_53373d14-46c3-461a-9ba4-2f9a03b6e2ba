﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Accounting
{
	public class PoolCapitalRegister
  {
    public string Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public TransactionType TransactionType { get; set; }
    public TransactionSubType TransactionSubType { get; set; }
    public double TransactionAmount { get; set; }
    public double RunningBalance { get; set; }
    public string Description { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime SettlementDate { get; set; }
    public bool IsModelPortfolio { get; set; }
    public string TxnRefId { get; set; }
    public string StrategyId { get; set; }
  }
}
