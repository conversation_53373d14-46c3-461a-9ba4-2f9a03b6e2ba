﻿using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
  public class TradeSellIdea
  {
    public string Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string StrategyModelName { get; set; }
    public string StrategyId { get; set; }
    public ApiContracts.Strategy.Strategy Strategy { get; set; }
    public string SecurityName { get; set; }
    public string Symbol { get; set; }
    public double Change { get; set; }
    public int StoplossLevel { get; set; }
    public string Description { get; set; }
    public string FilePath { get; set; }
    public string ModelId { get; set; }
    public Model Model { get; set; }
    public string Isin { get; set; }
    public string Exchange { get; set; }
    public int Status { get; set; }
    public int TradeType { get; set; }
  }
}

