﻿using Actlogica.AlphaPortfolios.ApiContracts.Strategy;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
    public class BuyTradeIdeaModelSecurity : ModelSecurity
  {
    public double BuyNetChange { get; set; }
    public double Change { get; set; }
    public double Lcp { get; set; }
    public bool IsSecurityModel { get; set; }
    public double Quantity { get; set; }
    public double TradeAmount { get; set; }
    public double CurrentHolding { get; set; }
    public double TradeQuantity { get; set; }
    public double NewWeight { get; set; }
  }
}
