﻿using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
	public class TradeIdeaCalculateWeight
	{
		[Required]
		public string ModelId { get; set; }
		public string Symbol { get; set; }
		[Required]
		public double Change { get; set; }
		[Required]
		public double PricePointToBuyOrSell { get; set; }
		[Required]
		public bool IsBuyTradeIdea { get; set; }
		[Required]
		public string Exchange { get; set; }
		[Required]
		public string Isin { get; set; }
		public bool IsSellAll { get; set; }
	}
}
