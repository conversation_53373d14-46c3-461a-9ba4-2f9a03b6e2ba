﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
  public class BuyTradeIdea
  {
    public string Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string SecurityName { get; set; }
    public string Symbol { get; set; }
    public string Isin { get; set; }
    public string Exchange { get; set; }
    public string SecurityType { get; set; }
    public double BuyPriceFrom { get; set; }
    public double BuyPriceTo { get; set; }
    public double ExitPrice { get; set; }
    public double BuyNetChange { get; set; }
    public string Description { get; set; }
    public string FilePath { get; set; }
    public int Status { get; set; }
    public string ApprovedBy { get; set; }
    public DateTime? ApprovedTime { get; set; }
    public string UpdatedBy { get; set; }
    public DateTime? UpdatedTime { get; set; }
    public string CreatedBy { get; set; }
    public DateTime? CreatedTime { get; set; }
    public string SubmittedBy { get; set; }
    public DateTime? SubmittedTime { get; set; }
    public string ExecutedBy { get; set; }
    public DateTime? ExecutedTime { get; set; }
    public string RejectedBy { get; set; }
    public DateTime? RejectedTime { get; set; }
    public string AbandonedBy { get; set; }
    public DateTime? AbandonedTime { get; set; }
    public string StrategyId { get; set; }
    public ApiContracts.Strategy.Strategy Strategy { get; set; }
    public string ModelId { get; set; }
    public ApiContracts.Strategy.Model Model { get; set; }
  }
}
