﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
    public class GetTradeIdeByStatusRequest: PaginationFilter
    {
        public GetTradeIdeByStatusRequest(string status, int pageNumber = 1, int pageSize = 10, string orderBy = "CreatedDate DESC", string filter = ""):base(pageNumber,pageSize,orderBy,filter)
        {
            Status = status;
        }
        public string Status { get; set; }
    }
}
