﻿using Actlogica.AlphaPortfolios.ApiContracts.Strategy;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
	public class SellTradeIdeaModelSecurity : ModelSecurity
	{
		public double SellNetChange { get; set; }
		public double Change { get; set; }
		public double Lcp { get; set; }
		public bool IsSecurityModel { get; set; }
		public double Quantity { get; set; }
		public double NewWeight { get; set; }
		public double TradeAmount { get; set; }
		public double TradeQuantity { get; set; }
	}
}
