﻿using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
    public class BuyTradeIdeaModel
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string SecurityName { get; set; }
        public string Symbol { get; set; }
        public string Scripcode { get; set; }
        [Required]
        public string Isin { get; set; }
        [Required]
        public string Exchange { get; set; }
        [Required]
        public string SecurityType { get; set; }
        [Required]
        public double BuyPriceFrom { get; set; }
        [Required]
        public double BuyPriceTo { get; set; }
        public double? ExitPrice { get; set; }
        public double BuyNetChange { get; set; }
        public string Description { get; set; }
        public string FilePath { get; set; }
        [Required]
        public string Status { get; set; }
        public string ApprovedBy { get; set; }
        public DateTime? ApprovedTime { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime? UpdatedTime { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? CreatedTime { get; set; }
        public string SubmittedBy { get; set; }
        public DateTime? SubmittedTime { get; set; }
        public string ExecutedBy { get; set; }
        public DateTime? ExecutedTime { get; set; }
        public string RejectedBy { get; set; }
        public DateTime? RejectedTime { get; set; }
        public string AbandonedBy { get; set; }
        public DateTime? AbandonedTime { get; set; }
        public string StrategyId { get; set; }
        public double? StopLoss { get; set; }
        public double Change { get; set; }
        public Strategy.Strategy Strategy { get; set; }
        [Required]
        public string ModelId { get; set; }
        public Model Model { get; set; }
    }
}
