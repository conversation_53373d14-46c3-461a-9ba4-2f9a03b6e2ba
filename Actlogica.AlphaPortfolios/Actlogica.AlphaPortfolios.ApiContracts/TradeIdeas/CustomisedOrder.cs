﻿namespace Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas
{
  public class CustomisedOrder
  {
    public string Id { get; set; }
    public string IsIn { get; set; }
    public string PortfolioId { get; set; }
    public string ClientId { get; set; }
    public string Name { get; set; }
    public string Industry { get; set; }
    public string Exchange { get; set; }
    public bool IsBuy { get; set; }
    public bool IsSell { get; set; }
    public string Symbol { get; set; }
    public double CurrentHolding { get; set; }
    public double BuyQuantity { get; set; }
    public double BuyPrice { get; set; }
    public double SellQuantity { get; set; }
    public double SellPrice { get; set; }
    public double Weight { get; set; }
    public double NewWeight { get; set; }
    public double TradeAmount { get; set; }
    public double CurrentPrice { get; set; }
    public bool IsMutualFund { get; set; }
    public string Remarks { get; set; }
  }
}
