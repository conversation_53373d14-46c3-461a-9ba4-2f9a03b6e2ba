﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.Bonds
{
	public class BondInvestment
	{
		public string Id { get; set; }
		public string PortfolioId { get; set; }
		public string ModelportfolioId { get; set; }
		public string ClientId { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime LastUpdatedDate { get; set; }
		public string Symbol { get; set; }
		public string Isin { get; set; }
		public string Exchange { get; set; }
		public double CurrentPrice { get; set; }
		public DateTime CurrentPriceDate { get; set; }
		public double CurrentHolding { get; set; }
		public double TotalCapital { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double Dividends { get; set; }
		public double IrrSinceInception { get; set; }
		public double IrrCurrent { get; set; }
		public DateTime FirstTransactionDate { get; set; }
		public DateTime LastTransactionDate { get; set; }
		public string Type { get; set; }
		public string Rating { get; set; }
		public virtual IEnumerable<BondTransaction> Transactions { get; set; }
	}
}
