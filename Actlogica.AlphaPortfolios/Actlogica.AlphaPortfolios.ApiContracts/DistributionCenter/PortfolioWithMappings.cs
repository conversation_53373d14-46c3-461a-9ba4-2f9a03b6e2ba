
using System;
using DocumentFormat.OpenXml.Office2016.Drawing.Command;

public class PortfolioWithMappings
{
    public string PortfolioId { get; set; }
    public string Name { get; set; }
    public double InvestedCapital { get; set; }
    public double MarketValue { get; set; }
    public double TwrrSinceInception { get; set; }
    public double AnnualPerformanceTwrr { get; set; }
    public string Id { get; set; }
    public string DistributorMasterId { get; set; }
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string ClientId { get; set; }

}