using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class DistributorMasterSharingConfiguration
    {
        public string DistributorMasterId { get; set; }
        // public string PortfolioId { get; set; }
        public string StratergyId { get; set; }
        public double FixedFeeSharingPercentage { get; set; }
        public double PerformanceFeeSharingPercentage { get; set; }
        public double UpFrontFeeSharingPercentage { get; set; }
        public double ExitLoadSharingPercentage { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Remarks { get; set; }
        public double AMCMinimumRetention { get; set; }
        public bool SharingConfigExist {get; set; }
        
    }

}