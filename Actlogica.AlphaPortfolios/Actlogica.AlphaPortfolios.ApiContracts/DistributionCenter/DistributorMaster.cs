using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class DistributorMaster
    {
        
        public string Name { get; set; }
        public string Type { get; set; }
        public TaxStatus TaxStatus { get; set; }
        public string Website { get; set; }
        public string GSTNo { get; set; }
        public DateTime BrokerCodeValidateUpto { get; set; }
        public DateTime DMStartDate { get; set; } 
        public DateTime DMEndDate { get; set; }
        public string Remarks { get; set; }
        public string UniqueDistributorCode { get; set; }
        public string DistributorCodeWs { get; set; }
        public string BranchId { get; set; }
        public string RegionId { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
		public string Status { get; set; }
		public DateTime AgreementDate { get; set; }
		public DateTime EffectiveFrom { get; set; }
		public string AgSignMode { get; set; }
		public string AgStyle { get; set; }
		public string SignedBy { get; set; }
		public DateTime SignedAt { get; set; }

    }
}