using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class EditDistributorRm
    {
        public string Salutation { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Gender { get; set; }
        public string Designation { get; set; }
        public string Department { get; set; }
        public string Role { get; set; }
        public string Branch { get; set; }
        public string Region { get; set; }
        public DateTime FromDate { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string Email2 { get; set; }
        public string Mobile2 { get; set; }
        public string Remarks { get; set; }
        public string ReportTo { get; set; }

    }
}