
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class EditFeeSharingConfiguration
    {

        public double FixedFeeSharingPercentage { get; set; }
        public double PerformanceFeeSharingPercentage { get; set; }
        public double UpFrontFeeSharingPercentage { get; set; }
        public double ExitLoadSharingPercentage { get; set; }
        public DateTime ToDate { get; set; }
        public string Remarks { get; set; }
        public double AMCMinimumRetention { get; set; }
        public string ProductType { get; set; }
        public string EmpanelmentType { get; set; }

    }

}
