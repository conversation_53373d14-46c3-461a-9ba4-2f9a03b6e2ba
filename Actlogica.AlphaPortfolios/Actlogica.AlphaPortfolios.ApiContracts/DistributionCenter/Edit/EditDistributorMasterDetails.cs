
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class EditDistributorMasterDetails
    {
        public DateTime AgreementDate { get; set; }
        public string AgSignMode { get; set; }
        public string AgStyle { get; set; }
        public DateTime BrokerCodeValidateUpto { get; set; }
		public string BranchId { get; set; }
        public string DistributorCodeWS { get; set; }
        public string DistributorUserRemarks { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public TaxStatus TaxStatus { get; set; }
        public string Website { get; set; }
        // public string GSTNo { get; set; }
        public DateTime DMStartDate { get; set; }
        public DateTime DMEndDate { get; set; }
        public string DistributorMasterRemarks { get; set; }
        public string UniqueDistributorCode { get; set; }
        // public string Status { get; set; }
        public DateTime EffectiveFrom { get; set; }
        public string SignedBy { get; set; }
        public string Salutation { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Gender { get; set; }
        public string Mobile { get; set; }
        public DateTime FromDate { get; set; }
        public string Email { get; set; }
        public DateTime SignedAt { get; set; }
        public string DistributorId { get; set; }
		public string RegionId { get; set; }
		public string Status { get; set; }
		

    }
}