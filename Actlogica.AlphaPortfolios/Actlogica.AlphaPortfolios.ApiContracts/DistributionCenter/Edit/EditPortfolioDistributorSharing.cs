
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
using System.Collections.Generic;
namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class EditPortfolioDistributorSharing
    {

        public string Id { get; set; }
        public string DistributorMasterSharingConfigurationId { get; set; }
        public string PortfolioId { get; set; }
        public double FixedFeeSharingPercentage { get; set; }
        public double PerformanceFeeSharingPercentage { get; set; }
        public double UpFrontFeeSharingPercentage { get; set; }
        public double ExitLoadSharingPercentage { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Remarks { get; set; }

    }
}