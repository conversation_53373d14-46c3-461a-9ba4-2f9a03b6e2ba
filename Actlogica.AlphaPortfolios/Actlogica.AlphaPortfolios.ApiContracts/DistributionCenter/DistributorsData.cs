using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class DistributorsData
    {
        
        public string Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public TaxStatus TaxStatus { get; set; }
        public string DistributorMasterEmail { get; set; }
        public string DistributorMobile { get; set; }
        public string Website { get; set; }
        public DateTime BrokerCodeValidateUpto { get; set; }
        public DateTime DMStartDate { get; set; } 
        public DateTime DMEndDate { get; set; }
        public string DistributorMasterRemarks { get; set; }
        public string Address1 { get; set; }
        public string Address2 { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string GSTNo { get; set; }
		public string Status { get; set; }
		public string BranchName { get; set; }
		public string RegionName { get; set; }
		public string BranchEmail { get; set; }
		public string BranchMobile { get; set; }
		public string AgSignMode { get; set; }
		public string AgStyle { get; set; }
		public DateTime AgreementDate { get; set; }
		public DateTime EffectiveFrom { get; set; }
		public DateTime SignedAt { get; set; }
		public string SignedBy { get; set; }
        public string UniqueDistributorCode { get; set; }
        public string TeamMemberName { get; set; }
        public string NISMCertification { get; set; }
        public string NISMCertificationType { get; set; }
        public string NISMCertValidfrom { get; set; }
        public string NISMCertValidto { get; set; }
        public string CerticationRemarks { get; set; }
        public string DistributorMasterId { get; set; }
        public string UserId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string AccessLevel { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string ReportTo { get; set; }
        public string Department { get; set; }
        public string Designation { get; set; }
        public string Email2 { get; set; }
        public string Email { get; set; }
        public string Gender { get; set; }
        public string LoginEnabled { get; set; }
        public string Mobile2 { get; set; }
        public DateTime ReginedOn { get; set; }
        public string Salutation { get; set; }
        public string DistrbutorUserStatus { get; set; }
        public string Role { get; set; }
        public string BranchId { get; set; }
        public string RegionId { get; set; }
        public string DistributorUserRemarks { get; set; }
        public bool CanDelete { get; set; }

    }
}