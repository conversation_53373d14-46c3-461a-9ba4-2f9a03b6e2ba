using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class DistributionCenterClients
    {

        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string ClientCode { get; set; }
        public string FirstName { get; set; }
        public string MiddleName { get; set; }
        public string LastName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string Pan { get; set; }
        public string Aadhar { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string ClientBankId { get; set; }
        public string DisplayName { get; set; }
        public ClientType ClientType { get; set; }
        public DomicileType DomicileType { get; set; }        
        public  string BseStarUcc { get; set; }        
        public ClientTitle ClientTitle  { get; set; } 
        public string UserName { get; set ;}
        public List<Portfolio> Portfolios { get; set; }

    }
}