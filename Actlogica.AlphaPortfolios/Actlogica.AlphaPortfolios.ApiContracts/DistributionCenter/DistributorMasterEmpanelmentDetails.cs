
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
using System.Collections.Generic;
namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
  public class DistributorMasterEmpanelmentDetails
  {

    public string Id { get; set; }
    public string DistributorMasterId { get; set; }
    public List<string> StratergyId { get; set; }
    public string EmpanelmentType { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }


  }
}
