using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class DistributorClientsData
    {

		public string DistributorMasterId { get; set; }
		public int UserId { get; set; }
		public string EmployeeId { get; set; }
		public string Id { get; set; }
		public string FirstName { get; set; }
		public string LastName { get; set; }
		public string Email { get; set; }
		public string Email2 { get; set; }
		public string Mobile { get; set; }
		public string Mobile2 { get; set; }
		public string UserName { get; set; }
		public string AccessLevel { get; set; }
		public DateTime FromDate { get; set; }
		public DateTime CreatedDate { get; set; }
		public DateTime? ToDate { get; set; }
		public string Remarks { get; set; }
		public string Branch { get; set; }
		public string Region { get; set; }
		public string BranchId { get; set; }
		public string BranchName { get; set; }
		public string RegionName { get; set; }
		public string RegionId { get; set; }
		public string ReportTo { get; set; }
		public string ReportToName { get; set; }
		public string Salutation { get; set; }
		public string Gender { get; set; }
		public string Designation { get; set; }
		public string Department { get; set; }
		public string Role { get; set; }

	}
}
