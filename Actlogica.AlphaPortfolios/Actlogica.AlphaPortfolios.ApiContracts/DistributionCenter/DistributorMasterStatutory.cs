using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class DistributorMasterStatutory
    {
        public string DistributorMasterId { get; set; }
        public string PAN { get; set; }
        public string APMIRegNo { get; set; }
        public DateTime APMIRegValidfrom { get; set; }
        public DateTime APMIRegValidto { get; set; }
        public string AMFIRegNo { get; set; }
        public DateTime AMFIRegValidFrom { get; set; }
        public DateTime AMFIRegValidto { get; set; }
        public string FirmNo { get; set; }
        public DateTime? DateofIncorporation { get; set; }
    }
}