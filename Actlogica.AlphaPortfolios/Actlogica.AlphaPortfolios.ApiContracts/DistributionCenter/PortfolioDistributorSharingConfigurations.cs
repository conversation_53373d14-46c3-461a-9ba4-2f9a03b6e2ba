using System;
namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
	public class PortfolioDistributorSharingConfigurations
	{
		public string PortfolioId { get; set; }
		public string DistributorMasterSharingConfigurationId { get; set; }
		public double FixedFeeSharingPercentage { get; set; }
		public double PerformanceFeeSharingPercentage { get; set; }
		public double UpFrontFeeSharingPercentage { get; set; }
		public double ExitLoadSharingPercentage { get; set; }
		public DateTime FromDate { get; set; }
		public DateTime? ToDate { get; set; }
		public string Remarks { get; set; }
		public string Id { get; set; }

        public double AMCMinRetentionInFixedFees { get; set; }
        public double ExitLoadWithin1YSharingPercentage { get; set; }
        public double ExitLoadWithin2YSharingPercentage { get; set; }
        public double ExitLoadWithin3YSharingPercentage { get; set; }
		public DateTime ValidFromDate { get; set; }
		public DateTime? ValidToDate { get; set; }
    }
}
