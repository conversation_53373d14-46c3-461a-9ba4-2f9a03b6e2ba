using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class DistributionUser
    {
        public string DMUserId { get; set; }
        public string DistributorMasterId { get; set; }
        public int UserId { get; set; }
        public string EmployeeId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string Mobile { get; set; }
        public string UserName { get; set; }
        public string AccessLevel { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public string Remarks { get; set; }
        public string Branch { get; set; }
        public double Aum { get; set; }
        public int TotalPortfolios { get; set; }
        public string Region { get; set; }
        public string ReportTo { get; set; }
        public int Clients { get; set; }
        public List<DistributorRmDetails> DistributorRmDetails { get; set; }
        public string Email2 { get; set; }
        public string Mobile2 { get; set; }
        public DateTime? ResignedOn { get; set; }
		public bool LoginEnabled { get; set; }
        public string Status { get; set; }
		public string Designation { get; set; }
		public string Department { get; set; }
		public Gender Gender { get; set; }
		public string Salutation { get; set; }
		public string Role { get; set; }

    }

    public class DistributorRmDetails
    {

        public string RmId { get; set ;}
        public string RmName { get; set ;}
        public int TotalPortfolios { get; set ;}
        public string ReportsTo { get; set ;}
        public string Region { get; set ;}
        public string Branch { get; set ;}
        
    }
}