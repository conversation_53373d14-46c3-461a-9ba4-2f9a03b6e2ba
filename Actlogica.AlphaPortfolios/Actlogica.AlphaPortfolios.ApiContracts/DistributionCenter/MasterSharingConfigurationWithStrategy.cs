using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
    public class MasterSharingConfigurationWithStrategy
    {
        public string StrategyName { get; set; }
        public string Id { get; set; }
        public string DistributorMasterId { get; set; }

        public string StratergyId { get; set; }

        public double FixedFeeSharingPercentage { get; set; }
        public double PerformanceFeeSharingPercentage { get; set; }
        public double UpFrontFeeSharingPercentage { get; set; }
        public double ExitLoadSharingPercentage { get; set; }

        public DateTime FromDate { get; set; }
        public DateTime? ToDate { get; set; }

        public string Remarks { get; set; }

        public double AMCMinimumRetention { get; set; }
    }
}