
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
using System.Collections.Generic;
namespace Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter
{
  public class EmpanelmentsData
  {

    public string Id { get; set; }
    public string DistributorMasterId { get; set; }
    public string StratergyId { get; set; }
    public string EmpanelmentType { get; set; }
    public string StrategyName { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }


  }
}
