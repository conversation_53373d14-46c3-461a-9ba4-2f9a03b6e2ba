﻿using System;

public class BenchmarkReturn
{
    public int IndexCode { get; set; }
    public DateTime AsAtDate { get; set; }
    public DateTime PrevousDate { get; set; }
    public double ClosePrice { get; set; }
    public double PreviousClose { get; set; }
    public double OneDayRet { get; set; }
    public DateTime OneWeekDate { get; set; }
    public double OneWeekClose { get; set; }
    public double OneWeekReturn { get; set; }
    public DateTime OneMonthDate { get; set; }
    public double OneMonthClose { get; set; }
    public double OneMonthReturn { get; set; }
    public DateTime ThreeMonthDate { get; set; }
    public double ThreeMonthClose { get; set; }
    public double ThreeMonthReturn { get; set; }
    public DateTime SixMonthDate { get; set; }
    public double SixMonthClose { get; set; }
    public double SixMonthReturn { get; set; }
    public DateTime YtdDate { get; set; }
    public double YtdClose { get; set; }
    public double YtdReturn { get; set; }
    public DateTime OneYearDate { get; set; }
    public double OneYearClose { get; set; }
    public double OneYearReturn { get; set; }
    public DateTime ThreeYearDate { get; set; }
    public double ThreeYearClose { get; set; }
    public double ThreeYearReturn { get; set; }
    public DateTime FiveYearDate { get; set; }
    public double FiveYearClose { get; set; }
    public double FiveYearReturn { get; set; }
}