﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Benchmarks
{
	public class BenchmarkDetails
	{
		public IndexMaster IndexMaster { get; set; }
		public List<MarketIndexData> CloseValues { get; set; }
		public BenchmarkReturn AnnualisedReturn { get; set; }
		public BenchmarkReturn AbsoluteReturn { get; set; }
	}
}
