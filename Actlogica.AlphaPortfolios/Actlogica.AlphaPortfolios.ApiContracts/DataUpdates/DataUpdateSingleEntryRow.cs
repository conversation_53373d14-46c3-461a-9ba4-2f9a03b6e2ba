

using System;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ApiContracts.DataUpdates
{
	public class DataUpdateSingleEntryRow
	{
		
		public string PortfolioName  { get; set; }
		public DateTime TransactionDate { get; set; }
		public string TransactionType { get; set; }
		public string TransactionSubType { get; set; }
		public double Amount { get; set; }
		public string  Description {get; set; }
		public string  ClientName {get; set; }
		public string  ClientCode {get; set; }
		
	}
}
