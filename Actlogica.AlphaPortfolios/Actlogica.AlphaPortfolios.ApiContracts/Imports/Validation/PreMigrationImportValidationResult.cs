﻿using Actlogica.AlphaPortfolios.ApiContracts.CorporateActions;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Imports.Validation
{
	public class PreMigrationImportValidationResult
	{
		public string SecurityType { get; set; }
		public string ImportIsin { get; set; }
		public string MasterIsin { get; set; }
		public string Scripcode { get; set; }
		public string Symbol { get; set; }
		public string ImportSecurityName { get; set; }
		public string MasterSecurityName { get; set; }
		public bool IsinCheckValid { get; set; }
		public bool IsListedInBse { get; set; }
		public bool IsListedInNse { get; set; }
		public DateTime FirstTransactionDate { get; set; }
		public DateTime LastTransactionDate { get; set; }
		public List<EquityCorporateAction> BseCorporateActions { get; set; }
		public List<EquityCorporateAction> NseCorporateActions { get; set; }
		public List<Tuple<DateTime?, DateTime?>> BsePriceRecon { get; set; }
		public List<Tuple<DateTime?, DateTime?>> NsePriceRecon { get; set; }
	}
}
