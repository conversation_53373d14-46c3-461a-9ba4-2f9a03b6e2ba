﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Imports.LegacyData
{
	public class ClientMasterDataImport
	{
		public string ClientCode { get; set; }
		public string FirstName { get; set; }
		public string MiddleName { get; set; }
		public string LastName { get; set; }
		public string Pan { get; set; }
		public string DateOfBirth { get; set; }
		public string Phone { get; set; }
		public string Email { get; set; }
		public string BankName { get; set; }
		public string AccountName { get; set; }
		public string AccountNumber { get; set; }
		public string AccountType { get; set; }
		public string IFSC { get; set; }
		public string MICR { get; set; }
		public string BankAddress1 { get; set; }
		public string BankAddress2 { get; set; }
		public string City { get; set; }
		public string State { get; set; }
		public string Postcode { get; set; }
		public string CustodianId { get; set; }
		public string CustodyDpId { get; set; }
	}
}
