﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Imports.LegacyData
{
	public class TransactionImportDataBase
	{
		public string APClientCode { get; set; }
		public string ClientCode { get; set; }
		public string ClientName { get; set; }
		public string StrategyCode { get; set; }
		public string StrategyName { get; set; }
		public string ModelName { get; set; }
		public string Symbol { get; set; }
		public string SecurityName { get; set; }
		public string Isin { get; set; }
		public string Exchange { get; set; }
		public string TransactionDate { get; set; }
		public string SettlementDate { get; set; }
		public string CGTDate { get; set; }
		public string TransactionType { get; set; }
		public string TransactionSubType { get; set; }
		public double MarketRate { get; set; }
		public double AcquisitionRate { get; set; }
		public double Quantity { get; set; }
		public double TransactionAmount { get; set; }
		public double BrokeragePerUnit { get; set; }
		public double ServiceTax { get; set; }
		public double SttAmount { get; set; }
		public double TurnTax { get; set; }
		public double OtherTax { get; set; }
		public string SecurityType { get; set; }
		public string MFFolio { get; set; }
		public bool IsValid { get; set; }
		public string SystemRemarks { get; set; }
	}
}
