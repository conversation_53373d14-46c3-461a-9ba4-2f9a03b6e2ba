﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Imports.LegacyData
{
	public class IncomeExpenseTransactionImport
	{
		public string APClientCode { get; set; }
		public string ClientCode { get; set; }
		public string ClientName { get; set; }
		public string StrategyCode { get; set; }
		public string StrategyName { get; set; }
		public string ModelName { get; set; }
		public string TransactionDate { get; set; }
		public string SettlementDate { get; set; }
		public string TransactionType { get; set; }
		public string TransactionSubType { get; set; }
		public double Amount { get; set; }
		public double Balance { get; set; }
		public string Remarks { get; set; }
		public bool IsValid { get; set; }
		public string SystemRemarks { get; set; }
	}

	public class ReRunCashLedgerImport
	{
		public string ClientStrategyCode;
		public string ClientName;
		public DateTime AsAtDate;
	}

	public class DeleteCashLedgerTransactionImport
	{
		public string ClientStrategyCode;
		public string ClientName;
		public DateTime AsAtDate;
		public string Id;
		public string Table;

	}
}
