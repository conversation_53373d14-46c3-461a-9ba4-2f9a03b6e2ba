using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.TradeCompliance;
public class PreTradeRules
{
    public string RuleTypeId { get; set; }
    public string RuleTypeName { get; set; }

    public string RuleSubTypeId { get; set; }
    public string SubTypeName { get; set; }

    public string Level { get; set; }

    public string LevelValue { get; set; }
    public string LevelValueName { get; set; }

    public string ValueType { get; set; }

    public string Value { get; set; }
    public string Id { get; set; }

    public bool IsActive { get; set; }
    public string RuleSubTypeValueExplicit { get; set; }
    public DateTime CreatedDate { get; set; }
}