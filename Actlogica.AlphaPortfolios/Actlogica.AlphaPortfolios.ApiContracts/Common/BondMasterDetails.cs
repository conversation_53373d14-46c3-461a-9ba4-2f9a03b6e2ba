﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
	public class BondMasterDetails
	{
		//public int Id { get; set; }
		public string Fincode { get; set; }
		public string Scripcode { get; set; }
		public string Scrip_name { get; set; }
		public string Scrip_group { get; set; }
		public string CompName { get; set; }
		public string Ind_Code { get; set; }
		public string Hse_Code { get; set; }
		public string Symbol { get; set; }
		public string Series { get; set; }
		public string Isin { get; set; }
		public string S_Name { get; set; }
		public string Status { get; set; }
		public string Sublisting { get; set; }
		public string Flag { get; set; }
		public string Sector { get; set; }
		public string Industry { get; set; }
		public string Ind_Shortname { get; set; }
		public string Marketcap { get; set; }
		public double LatestPrice { get; set; }
		public string MigrationInputIsin { get; set; }
	}
}
