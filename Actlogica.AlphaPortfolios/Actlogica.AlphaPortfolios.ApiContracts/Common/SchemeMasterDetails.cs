﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
    public class SchemeMasterDetails
  {
		public string Isin { get; set; }
		public string AmfiCode { get; set; }
		public string BseStarSchemeCode { get; set; }
		public string SettlementType { get; set; }
		public string SchemeType { get; set; }
		public string SchemeName { get; set; }
		public string PurchaseAllowed { get; set; }
		public double MinimumPurchaseAmount { get; set; }
		public double AdditionalPurchaseAmount { get; set; }
		public double MaximumPurchaseAmount { get; set; }
		public string RedemptionAllowed { get; set; }
		public double RedemptionAmountMinimum { get; set; }
		public double RedemptionAmountMaximum { get; set; }
		public double MaximumRedemptionQty { get; set; }
		public string PurchaseMode { get; set; }
		public string DistributionStatus { get; set; }
		public string InvestmentPlan { get; set; }
		public string FundName { get; set; }
		public string NetExpenseRatio { get; set; }
		public string ExchangeTradedShare { get; set; }
		public string FundManagerName { get; set; }
		public string FundManagerStartDate { get; set; }
		public string InceptionDate { get; set; }
		public string RTACode { get; set; }
		public string FundLegalName { get; set; }
		public string AssetClass { get; set; }
		public string FundClass { get; set; }
		public string CategoryName { get; set; }
		public string Amc { get; set; }
		public string AmcCode { get; set; }
		public string Benchmark { get; set; }
		public string MorningstarBenchmarkId { get; set; }
		public string MorningstarBenchmarkName { get; set; }
		public string DebtAllocation { get; set; }
		public string EquityAllocation { get; set; }
		public string CashAllocation { get; set; }
		public string FundStatus { get; set; }
		public double LatestPrice { get; set; }
	}
}
