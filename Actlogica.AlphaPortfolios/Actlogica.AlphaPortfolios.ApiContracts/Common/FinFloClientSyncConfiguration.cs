﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
  public class FinFloClientSyncConfiguration
  {
    public string Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public string ClientId { get; set; }
    public Client Client { get; set; }
    public string PortfolioId { get; set; }
    public Portfolio Portfolio { get; set; }
    public string LicenseNumber { get; set; }
    public DateTime LastTransactionSyncDateTime { get; set; }
    public DateTime LastCapitalRegisterSyncDateTime { get; set; }
    public string LastSyncStatus { get; set; }
    public string LastSyncRemarks { get; set; }
    public bool IsEnabled { get; set; }
  }
}
