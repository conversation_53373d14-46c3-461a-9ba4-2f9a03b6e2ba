﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
	public class Transaction
	{
		public string Id { get; set; }
		public string PortfolioId { get; set; }
		public string Identifier { get; set; }
		public string Isin { get; set; }
		public InvestmentType InvestmentType { get; set; }
		public DateTime Date { get; set; }
		public TransactionType Type { get; set; }
		public TransactionSubType SubType { get; set; }
		public double Units { get; set; }
		public double UnitPrice { get; set; }
		public double Amount { get; set; }
		public double BrokeragePerUnit { get; set; }
		public double Stt { get; set; }
		public double Gst { get; set; }
		public double Tds { get; set; }
	}
}
