﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
    public class PaginationFilter
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string OrderBy { get; set; } = "LastUpdatedDate DESC";
        public string FilterBy { get; set; } = string.Empty;
        public string FilterValue { get; set; } = string.Empty;

        public PaginationFilter()
        {

        }
        public PaginationFilter(int pageNumber=1, int pageSize=10, string orderBy= "LastUpdatedDate DESC", string filterby="", string filterValue = "")
        {
            this.PageNumber = pageNumber < 1 ? 1 : pageNumber;
            this.PageSize = pageSize > 10 ? 10 : pageSize;
            this.OrderBy = orderBy;
            this.FilterBy = filterby;
            this.FilterValue = filterValue;
        }
    }
}
