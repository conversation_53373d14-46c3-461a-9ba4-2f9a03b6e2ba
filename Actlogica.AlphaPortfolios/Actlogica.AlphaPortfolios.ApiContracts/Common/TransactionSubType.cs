﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
	public enum TransactionSubType
	{
		Buy,
		BuyAdjustment,
		Sell,
		SellAdjustment,
		Purchase,
		Redemption,
		DividendReinvestment,
		Dividend,
		SwitchIn,
		SwitchOut,
		TransferIn,
		TransferOut,
		Split,
		BuySplitAdjustment,
		Bonus,
		BuyBonusAdjustment,
		SecurityIn,
		SecurityOut,
		CapitalIn,
		CapitalOut,
		Fees,
		PerformanceFees,
		Charges,
		Tds,
		Other,
		Receipt,
		Payment,
		Interest,
		Tax,
		FractionPayment,
		SplitPartial,
		BonusPartial,
		ReturnOnCapital,
		IncomeSurplus,
		Merger,
		Demerger,
		Amalgamation,
		MergerPartial,
		DemergerPartial,
		AmalgamationPartial,
		SpinOff,
		SpinOffPartial,
		CashBalance,
		PeakMargin,
		OtherAssetsLiabilities,
		Stt,
		ExitLoad,
		CustodyCharges,
		FundAccountingCharges,
		AccountOpeningCharges,
		OperatingExpenses,
		AuditCharges,
		ContraEntry,
		Ipo,
		BankCharges,
		OtherExpenses
	}
}
