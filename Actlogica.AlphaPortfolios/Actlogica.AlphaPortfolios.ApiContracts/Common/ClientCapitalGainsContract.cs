﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
  public class ClientCapitalGainsContract
  {
    //public string CustomerId { get; set; }
    public string SecurityName { get; set; }
    public string Isin { get; set; }
    public CgtAssetClassType AssetClassType { get; set; }
    public CgtExposureType ExposureType { get; set; }
    public DateTime AsAtDate { get; set; }
    public string SellDate { get; set; }
    public double SellUnits { get; set; }
    public double SellPrice { get; set; }
    public double SellTransactionAmount { get; set; }
    public string PurchaseDate { get; set; }
    public DateTime PurchaseAsAtDate { get; set; }
    public double PurchasePrice { get; set; }
    public string PriceOnGrandFatherDate { get; set; }
    public double AdjustedPurchasePrice { get; set; }
    public double AdjustedPurchaseCost { get; set; }
    public double CapitalGainLoss { get; set; }
    public string FinancialYear { get; set; }
    public string FolioNumber { get; set; }
    public string PortfolioName { get; set; }
  }
}
