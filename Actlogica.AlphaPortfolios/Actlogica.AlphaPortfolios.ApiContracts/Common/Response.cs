﻿using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
    public class Response<T>
    {
        public Response()
        {
        }
        public Response(T data)
        {
            Data = data;
        }
        public T Data { get; set; }
        public bool Succeeded { get; set; } = true;
        public List<string> Errors { get; set; }=null;
        public string Message { get; set; } = string.Empty;
    }
}
