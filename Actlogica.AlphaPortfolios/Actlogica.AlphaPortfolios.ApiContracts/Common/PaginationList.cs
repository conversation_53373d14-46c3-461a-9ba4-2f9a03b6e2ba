﻿using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
    public class PaginatedList<T> where T : class
    {
        public PaginatedList()
        {

        }
        public PaginatedList(List<T> items, int iotalItems)
        {
            Items = items;
            TotalItems = iotalItems;    
        }
        public int TotalItems { get; set; }
        public List<T> Items { get; set; }
    }
}
