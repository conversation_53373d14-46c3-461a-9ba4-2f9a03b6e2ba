﻿namespace Actlogica.AlphaPortfolios.ApiContracts.Common
{
  public class FinFloMultiTenancySettings
  {
    public string TenantName { get; set; }
    public string StorageConnectionString { get; set; }
    public string MarketDataConnectionString { get; set; }
    public string ApplicationDbConnectionString { get; set; }
    public int UserId { get; set; }
    public int AdminId { get; set; }
    public string LoggedInUserEmail { get; set; }
    public string LoggedInUserName { get; set; }
    public string EUIN { get; set; }
    public string SubBrokerArn { get; set; }
    public string OrgLegalEmail { get; set; }
    public string OrgLegalName { get; set; }
  }
}
