﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.RestrictedStockORGs
{
    public class RestrictedStockORG
    {
        public string Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string RestrictedSecurityIdentifier { get; set; }
        public string AlternativeSecurityIdentifier { get; set; }
        public string Rationale { get; set; }
        
        [Required]
        public string ExchangeRestrictedSecurity { get; set; }

        public string ExchangeAlternativeSecurity { get; set; }

        [Required]
        public string IsinRestrictedSecurity { get; set; }


        public string IsinAlternativeSecurity { get; set; }
        public string Status { get; set; }
    }
}
