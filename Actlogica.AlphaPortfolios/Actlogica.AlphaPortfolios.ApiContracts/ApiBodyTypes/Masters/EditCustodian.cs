using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Masters
{
	public class EditCustodian
	{

		[Required]
		public string DPId { get; set; }
        
		[Required]
		public string AddressLine1 { get; set; }

		public string AddressLine2 { get; set; }

		[Required]
		public string City { get; set; }
		[Required]
		public string State { get; set; }
		[Required]
		public string Postcode { get; set; }

		[Required]
		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
		public string Phone { get; set; }

		[Required]
		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
		public string Mobile { get; set; }

		[Required(ErrorMessage = "Support email is required")]
		[EmailAddress(ErrorMessage = "Invalid Email Address")]
		public string SupportEmail { get; set; }

		[Required(ErrorMessage = "Operations email is required")]
		[EmailAddress(ErrorMessage = "Invalid Email Address")]
		public string OperationsEmail { get; set; }
	}
}
