

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;


namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class CreateClient
    {
        [Required]
        public ClientDetails Client { get; set; }

        [Required]
        public ClientPersonalDetailsBody ClientPersonalDetail { get; set; }

        [Required]
        public ClientContactDetailsBody ClientContactDetail { get; set; }

        public IEnumerable<ClientBankBody> ClientBanks { get; set; }

        public ClientLogin ClientLogin { get; set; }

    }


}