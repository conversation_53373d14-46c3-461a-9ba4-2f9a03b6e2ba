using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class ClientPersonalDetailsBody
    {
        public MaritalStatus? MaritalStatus { get; set; }

        public ClientOccupation? Occupation { get; set; }

        [Required]
        public Nationality Nationality { get; set; }

        [Required]
        public string FamilyOrGroup { get; set; }

        [Required]
        public bool HeadOfFamily { get; set; }

        [Required]
        public bool KYCValid { get; set; }

        [Required]
        public ClientTaxStatus TaxStatus { get; set; }

        [Required]
        public ClientType ClientType { get; set; }

        [Required]
        public ClientCategory Category { get; set; }

        public Currency? ReportingCurreny { get; set; }

        public DateTime? AnniversaryDate { get; set; }

        [StringLength(50, MinimumLength = 2)]
        public string Spouse { get; set; }

        public DateTime? SpouseDOB { get; set; }

        public ClientQualification? Qualification { get; set; }

        public ClientWorkExperience? WorkExpererience { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string OrganizationName { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string EmployerID { get; set; }

        public IndustryType? IndustryType { get; set; }

        [StringLength(50, MinimumLength = 2)]
        public string AddressOrganisation { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string PlaceOfBirth { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string CountryOfBirth { get; set; }

        public ClientGrossAnnualIncome? GrossAnnualIncome { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string SourceOfWealth { get; set; }

        public ClientEstimatedFinancialWealth? EstimatedFinancialWealth { get; set; }

        [Required]
        public bool PoliticalExposure { get; set; }

        public bool? Amlcertified { get; set; }

        [Required]
        public Gender Gender { get; set; }

    }

}