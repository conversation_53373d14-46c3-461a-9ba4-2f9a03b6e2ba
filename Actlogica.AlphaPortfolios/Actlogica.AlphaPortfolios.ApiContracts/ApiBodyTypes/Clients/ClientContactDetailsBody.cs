using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class ClientContactDetailsBody
    {
        [Required]
        [StringLength(150, MinimumLength = 1)]
        public string AddressLine1 { get; set; }

        [StringLength(150, MinimumLength = 1)]
        public string AddresLine2 { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 1)]
        public string City { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string State { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string Country { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 6)]
        public string PinCode { get; set; }

        [StringLength(100, MinimumLength = 1)]
        public string MobileCountryCode { get; set; }

        [StringLength(20, MinimumLength = 1)]
        [RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Phone Number")]
        public string MobileSecondary { get; set; }

        [StringLength(100, MinimumLength = 1)]
        [EmailAddress(ErrorMessage = "Invalid Email")]
        public string EmailSecondary { get; set; }
    }

}