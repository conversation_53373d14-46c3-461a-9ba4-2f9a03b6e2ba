using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class ClientDetails
    {
        [Required]
        [StringLength(20, MinimumLength = 2)]
        public string ClientCode { get; set; }

        [Required]
        [StringLength(200, MinimumLength = 1)]
        public string FirstName { get; set; }

        public string MiddleName { get; set; }
        [Required]
        [StringLength(200, MinimumLength = 1)]

        public string LastName { get; set; }
        [Required]
        public DateTime DateOfBirth { get; set; }

        [Required]
        [StringLength(10, MinimumLength = 10)]
        public string Pan { get; set; }

        [StringLength(12, MinimumLength = 12)]
        public string Aadhar { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        [EmailAddress(ErrorMessage = "Invalid Email Address")]
        public string Email { get; set; }

        [Required]
        [StringLength(20, MinimumLength = 1)]
        [RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
        public string Phone { get; set; }

        [Required]
        [StringLength(200, MinimumLength = 1)]
        public string DisplayName { get; set; }

        [Required]
        public ClientType ClientType { get; set; }

        [Required]
        public DomicileType Domicile { get; set; }

        [StringLength(20, MinimumLength = 1)]
        public string BseStarUcc { get; set; }

        [Required]
        public ClientTitle Title { get; set; }

    }

}