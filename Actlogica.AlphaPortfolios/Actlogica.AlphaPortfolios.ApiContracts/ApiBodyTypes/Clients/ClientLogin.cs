
using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class ClientLogin
    {

        [Required]
        public string Username { get; set; }

        [Required]
        [EmailAddress(ErrorMessage = "Invalid Email Address")]
        public string Email { get; set; }

        [Required]
        public string FirstName { get; set; }

        [Required]
        public string LastName { get; set; }

        [Required]
        public DateTime Dob { get; set; } = DateTime.Now.AddYears(-18);

        [Required]
        public string Password { get; set; }

        [Required]
        [RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Phone Number")]
        public string Mobile { get; set; }
        
        [Required]
        [RegularExpression("^([A-Za-z]){5}([0-9]){4}([A-Za-z]){1}$", ErrorMessage = "Invalid PAN Number")]
        public string Pan { get; set; }
    }

}

