using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class ClientNewBody
    {
		public string ClientCode { get; set; }
		public string FirstName { get; set; }
		public string MiddleName { get; set; }
		public string LastName { get; set; }
		public DateTime DateOfBirth { get; set; }
		public string Pan { get; set; }
		public string Aadhar { get; set; }
		public string Email { get; set; }
		public string Phone { get; set; }
		// public string ClientBankId { get; set; }
		// public string DisplayName { get; set; }
		public ClientType ClientType { get; set; }
		public DomicileType Domicile { get; set; }
		public string BseStarUcc { get; set; }
		public ClientTitle Title { get; set; }
		public string UserName { get; set; }
		public string UserId { get; set; }
		public string CKYCNo { get; set; }
		public string FullName { get; set; }
		public string NameAsPerPan { get; set; }
		public bool IsAccreditedInvestor { get; set; }
    }
}