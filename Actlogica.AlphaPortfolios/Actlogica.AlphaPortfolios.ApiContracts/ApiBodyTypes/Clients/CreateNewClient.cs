using Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
	public class CreateNewClient
	{

		public ClientNewBody ClientNewBody { get; set; }
		public ClientContactDetail ClientContactDetail { get; set; }
		public ClientPersonalDetail ClientPersonalDetail { get; set; }
		public virtual ClientFamilyDetail ClientFamilyDetail { get; set; }
		public virtual ICollection<ClientIdentity> ClientIdentities { get; set; }
		public virtual ClientDueDiligence ClientDueDiligence { get; set; }
		public virtual ClientAccreditedInvestorDetail ClientAccreditedInvestorDetail { get; set; }
		public virtual ClientOverseasDetail ClientOverseasDetail { get; set; }
		public virtual ClientIncorporationDetail ClientIncorporationDetail { get; set; }
		public virtual PortfolioHolder PortfolioHolder { get; set; }
		public virtual PortfolioNomineeDetails PortfolioNomineeDetails { get; set; }
		// public ClientLogin ClientLogin { get; set; }
	}
}
