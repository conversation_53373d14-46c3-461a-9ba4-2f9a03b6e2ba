using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class ClientBankBody
    {

        [Required]
        [StringLength(250, MinimumLength = 1)]
        public string Name { get; set; }

        [Required]
        [StringLength(250, MinimumLength = 1)]
        public string AddressLine1 { get; set; }

        [StringLength(250, MinimumLength = 1)]
        public string AddressLine2 { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string City { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string State { get; set; }

        [Required]
        [StringLength(20, MinimumLength = 1)]
        public string Postcode { get; set; }

        [Required]
        public AccountStatus AccountStatus { get; set; }

        [Required]
        [StringLength(200, MinimumLength = 2)]
        public string AccountName { get; set; }

        [Required]
        [StringLength(250, MinimumLength = 2)]
        public string AccountNumber { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 2)]
        public string Ifsc { get; set; }

        [Required]
        [StringLength(250, MinimumLength = 2)]
        public string Micr { get; set; }

        [Required]
        public BankAccountType BankAccountType { get; set; }

        [StringLength(50, MinimumLength = 2)]
        public string Accountlink { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 2)]
        public string BranchName { get; set; }

        public Currency? Currency { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string SwiftCode { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string SecondHolderName { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string ThirdHolderName { get; set; }

    }

}