using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients
{
    public class ClientBrokerBody
    {
        [Required]
        public string TradingAccountNumber { get; set; }

        [Required]
        public string BrokerId { get; set; }
        
        [StringLength(200, MinimumLength = 2)]
        public string CPCode { get; set; }

    }

}