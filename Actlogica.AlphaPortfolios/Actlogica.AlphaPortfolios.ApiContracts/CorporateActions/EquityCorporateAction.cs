﻿using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.CorporateActions
{
	public class EquityCorporateAction
	{
		public string RowKey { get; set; }
		public string Symbol { get; set; }

		public string Series { get; set; }

		public string Ind { get; set; }

		public string FaceVal { get; set; }

		public string Subject { get; set; }

		public string ExDate { get; set; }

		public string RecDate { get; set; }

		public string BcStartDate { get; set; }

		public string BcEndDate { get; set; }

		public string NdStartDate { get; set; }

		public string NdEndDate { get; set; }

		public string Comp { get; set; }

		public string CaBroadcastDate { get; set; }

		public string Isin { get; set; }
		public string CAType { get; set; }
		public string InterestPerShare { get; set; }
		public string DividendPerShare { get; set; }
		public string SplitUnits { get; set; }
		public string SplitMultiple { get; set; }
		public string BonusUnits { get; set; }
		public string BonusMultiple { get; set; }
		public string CapitalReturnPerShare { get; set; }
		public string IncomeSurplusPerShare { get; set; }
		public string MergerDemergerAmalgamationRatios { get; set; }
		public string MergerDemergerAmalgamationPriceRatios { get; set; }
		public string DestinationSymbols { get; set; }
		public string OrderOfMultipleActions { get; set; }
	}
}
