﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.CorporateActions
{
	public class CorporateActionPayload
	{
		public CorporateActionType Type { get; set; }
		public double DividendRatePerUnit { get; set; }
		public double SplitForUnit { get; set; }
		public double SplitMultiple { get; set; }
		public double ReverseSplitForUnit { get; set; }
		public double ReverseSplitMultiple { get; set; }
		public double BonusNoOfUnits { get; set; }
		public double BonusMultiple { get; set; }
		public double MergeNoOfUnits { get; set; }
		public double MergeMultiple { get; set; }
		public double DemergeNoOfUnits { get; set; }
		public double DemergeMultiple { get; set; }
		public DateTime Date { get; set; }
		public string Symbol { get; set; }
	}
}
