using System;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Fees
{
	public class PortfolioFeeReports
	{
		public string Id { get; set; }		
        public string ClientName { get; set; }
        public string ClientId { get; set; }
        public string StrategyName { get; set; }
        public string ClientStrategyCode { get; set; }
        public DateTime PeriodFrom { get; set; }
        public DateTime PeriodTo { get; set; }
		public string Type { get; set; }
		public double AverageAum{ get; set; }
		public double ClosingAum{ get; set; }
		public string GST{ get; set; }
		public double TotalAmount{ get; set; }
		public double FixedFeeAmount{ get; set; }
		public double FixedFeePercentageApplied{ get; set; }
		public double PerformanceFeeAmount{ get; set; }
		public double ExitLoad{ get; set; }
		public double LastFeesDeducted{ get; set; }

	}
}