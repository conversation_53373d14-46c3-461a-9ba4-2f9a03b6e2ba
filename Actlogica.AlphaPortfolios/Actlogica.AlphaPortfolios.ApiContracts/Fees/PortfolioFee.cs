using System;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.ApiContracts.Fees
{
	public class PortfolioFee
	{
		public string Id { get; set; }
		public DateTime PeriodFrom { get; set; }
		public DateTime PeriodTo { get; set; }
		public double FixedFeeAmount { get; set; }
		public double FixedFeePercentageApplied { get; set; }
		public double PerformanceFeeAmount { get; set; }
		public double HurdleRateApplied { get; set; }
		public double SharingPercentage { get; set; }
		public double WatermarkApplied { get; set; }
		public double PortfolioReturnForPeriod { get; set; }
		public double OpeningAum { get; set; }
		public double ClosingAum { get; set; }
		public double NetCashFlow { get; set; }
		public double AverageAum { get; set; }
		public string Status { get; set; }
		public string PortfolioFeeTemplateId { get; set; }
		public virtual PortfolioFeeTemplate PortfolioFeeTemplate { get; set; }
		public string PortfolioId { get; set; }
		public virtual Portfolio Portfolio { get; set; }
		public string PortfolioFeeTriggeredLogId { get; set; }
		public string Remarks { get; set; }
		public string ReasonForEdit { get; set; }
		public string UserName { get; set; }
	}
}