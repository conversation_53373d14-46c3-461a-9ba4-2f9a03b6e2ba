
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Fees
{
    public class PortfolioFeeTriggeredLog
    {
        public string Id { get; set; }
        public DateTime TriggeredDate { get; set; }
        public string TriggerBy { get; set; }
        public int NoOfPortfolios { get; set; }
        public DateTime AsAtDate { get; set; }
        public double TotalCapital { get; set; }
        public string PortfolioIdList { get; set; }
        public string Purpose { get; set; }
        public string Remarks { get; set; }
        public string Status { get; set; }
        public string CompletionPercentage { get; set; }
    }

}