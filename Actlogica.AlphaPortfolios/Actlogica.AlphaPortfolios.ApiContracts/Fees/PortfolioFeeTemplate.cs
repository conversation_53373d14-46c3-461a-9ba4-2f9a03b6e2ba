
using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Fees
{
    public class PortfolioFeeTemplate
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public double FixedFeePercentage { get; set; }
        public string FixedFeeFrequency { get; set; }  //to enum
        public string PerformanceFeeFrequency { get; set; } //to enum
        public double PerformanceFeeSharingPercentage { get; set; }
        public double PerformanceFeeHurdleRate { get; set; }
        public DateTime AppliedFromDate { get; set; }
        public DateTime AppliedToDate { get; set; }
        public string ApprovedBy { get; set; }
        public string StrategyModelFeeTemplateId { get; set; }
        public string PortfolioId { get; set; }
        public string AMCMinRetentionInFixedFees { get; set; }
        public string HurdleRateRedeemedWithin1Year { get; set; }
        public string HurdleRateRedeemedWithin2Year { get; set; }
        public bool WithCatchup { get; set; }
        public string ExitLoadWithin1Y { get; set; }
        public string ExitLoadWithin2Y { get; set; }
        public string ExitLoadWithin3Y { get; set; }
        public DateTime ValidFromDate { get; set; }
        public DateTime ValidToDate { get; set; }
        public string FixedFeeType { get; set; }
        public double FixedFeeAbsolute { get; set; }
        public string FeesLevyMechansim { get; set; }
		public double HurdleRate { get; set; }
		public double PerformanceFeeLevyRate { get; set; }
		public double PerformanceFeeRetention { get; set; }
    }

}