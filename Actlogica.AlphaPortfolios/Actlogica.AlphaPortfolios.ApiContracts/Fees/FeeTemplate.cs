using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.Fees
{
    public class FeeTemplate
    {
        public string Id { get; private set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public double FixedFeePercentage { get; set; }
        public string FixedFeeFrequency { get; set; }
        public string PerformanceFeeFrequency { get; set; }
        public double PerformanceFeeSharingPercentage { get; set; }
        public double PerformanceFeeHurdleRate { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        public string AMCMinRetentionInFixedFees { get; set; }
        public string HurdleRateRedeemedWithin1Year { get; set; }
        public string HurdleRateRedeemedWithin2Year { get; set; }
        public bool WithCatchup { get; set; }
        public string ExitLoadWithin1Y { get; set; }
        public string ExitLoadWithin2Y { get; set; }
        public string ExitLoadWithin3Y { get; set; }
        public DateTime ValidFromDate { get; set; }
        public DateTime ValidToDate { get; set; }
        public string FixedFeeType { get; set; }
        public double FixedFeeAbsolute { get; set; }
        public string FeesLevyMechansim { get; set; }
        public double HurdleRate { get; set; }
        public double PerformanceFeeLevyRate { get; set; }
        public double PerformanceFeeRetention { get; set; }
    }

}