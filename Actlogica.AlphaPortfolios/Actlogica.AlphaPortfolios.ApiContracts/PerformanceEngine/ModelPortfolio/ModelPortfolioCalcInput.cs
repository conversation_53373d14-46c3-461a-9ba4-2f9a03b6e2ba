﻿using Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Bonds;
using Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.DirectEquity;
using Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.MutualFund;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.ModelPortfolio
{
	public class ModelPortfolioCalcInput
	{
		public List<DirectEquityInvestmentCalcInput> DirectEquityInvestmentsInput { get; set; }
		public List<MutualFundInvestmentCalcInput> MutualFundInvestmentsInput { get; set; }
		public List<BondInvestmentCalcInput> BondInvestmentsInput { get; set; }
		public List<ModelPortfolioCapitalRegister> CapitalRegister { get; set; }
		public List<ModelPortfolioLedger> Ledger { get; set; }
		public Portfolios.ModelPortfolio ModelPortfolio { get; set; }
		public DateTime AsOnDate { get; set; }
		public DateTime CalculationDate { get; set; }
		public string TenantName { get; set; }
	}
}
