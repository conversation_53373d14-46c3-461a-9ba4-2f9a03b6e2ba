﻿namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Common
{
	public class XirrCalculationResult
	{
		public XirrCalculationResult(double xirrValue, double netPresentValue, int iterations)
		{
			XirrValue = xirrValue;
			NetPresentValue = netPresentValue;
			Iterations = iterations;
		}

		public double XirrValue { get; }
		public double NetPresentValue { get; }
		public int Iterations { get; }
	}
}
