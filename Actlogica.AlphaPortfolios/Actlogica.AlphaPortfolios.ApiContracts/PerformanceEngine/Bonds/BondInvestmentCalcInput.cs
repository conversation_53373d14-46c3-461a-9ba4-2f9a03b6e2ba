﻿using Actlogica.AlphaPortfolios.ApiContracts.Bonds;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Bonds
{
	public class BondInvestmentCalcInput
	{
		public Investment Investment { get; set; }
		public IEnumerable<BondPrice> PriceHistory { get; set; }
		public BondMasterDetails SecurityMaster { get; set; }
		public BondInvestmentMarketValue MarketValue { get; set; }
		public DateTime AsOnDate { get; set; }
	}
}
