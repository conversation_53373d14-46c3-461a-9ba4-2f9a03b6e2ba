﻿using Actlogica.AlphaPortfolios.ApiContracts.Benchmarks;
using Actlogica.AlphaPortfolios.ApiContracts.Bonds;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.DirectEquity;
using Actlogica.AlphaPortfolios.ApiContracts.MutualFunds;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Portfolio
{
  public class PortfolioPerfEngineMasterData
  {
    public List<EquityCompanyMaster> StockMaster { get; set; }
    public List<SchemeMasterDetails> MutualFundMaster { get; set; }
    public List<BondMasterDetails> BondMaster { get; set; }
    public List<DirectEquityPrice> StockPrices { get; set; }
    public List<MutualFundNav> MutualFundPrices { get; set; }
    public List<BondPrice> BondPrices { get; set; }
		public List<BenchmarkDetails> BenchmarkDetails { get; set; }
	}
}
