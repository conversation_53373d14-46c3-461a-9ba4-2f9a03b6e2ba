﻿namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Portfolio
{
public class RiskCalculationValues
{
    public double Alpha { get; set; }
    public double BenchmarkAlpha { get; set; }
    public double StandardDeviation { get; set; }
    public double BenchmarkStandardDeviation { get; set; }
    public double Mean { get; set; }
    public double BenchmarkMean { get; set; }
    public double Variance { get; set; }
    public double BenchmarkVariance { get; set; }
    public double Covariance { get; set; }
    public double SharpeRatio { get; set; }
    public double Beta { get; set; }
    public double TreynorRatio { get; set; }
    public double RiskAdjustedRate { get; set; }
}
}
