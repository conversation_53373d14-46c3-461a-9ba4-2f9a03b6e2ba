﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Portfolio
{
	public class PortfolioMarketValue
	{
		public string PartitionKey { get; set; }
		public string RowKey { get; set; }
		public DateTime AsAtDate { get; set; }
		public double TotalCapital { get; set; }
		public double TotalWithdrawals { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double CashBalance { get; set; }
		public double TotalCashFlow { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double IrrSinceInception { get; set; }
		//public double IrrCurrent { get; set; }
		public double TwrrSinceInception { get; set; }
		public string BenchmarkName { get; set; }
		public double BenchmarkIrrSinceInception { get; set; }
		public double BenchmarkTwrrSinceInception { get; set; }
		public double NetReceivables { get; set; }
		public double NetPayables { get; set; }
		public double CurrentAssetsAndLiabilities { get; set; }
	}
}
