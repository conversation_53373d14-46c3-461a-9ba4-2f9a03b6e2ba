﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Portfolio
{
	public class PortfolioPerformanceDaily
	{
		public string PortfolioId { get; set; }
		public DateTime AsAtDate { get; set; }
		public string PortfolioName { get; set; }
		public string BenchmarkName { get; set; }
		public double PortfolioStandardDeviation { get; set; }
		public double BenchmarkStandardDeviation { get; set; }
		public double PortfolioMean { get; set; }
		public double BenchmarkMean { get; set; }
		public double PortfolioAlpha { get; set; }
		public double BenchmarkAlpha { get; set; }
		public double PortfolioVariance { get; set; }
		public double BenchmarkVariance { get; set; }
		public double PortfolioCovariance { get; set; }
		public double PortfolioSharpeRatio { get; set; }
		public double Beta { get; set; }
		public double TreynorRatio { get; set; }
		public double RiskAdjustedRate { get; set; }
		public double XirrSi { get; set; }
		public double BenchmarkXirrSi { get; set; }
		public double Xirr1M { get; set; }
		public double BenchmarkXirr1M { get; set; }
		public double Xirr3M { get; set; }
		public double BenchmarkXirr3M { get; set; }
		public double Xirr6M { get; set; }
		public double BenchmarkXirr6M { get; set; }
		public double XirrYtd { get; set; }
		public double BenchmarkXirrYtd { get; set; }
		public double Xirr1Y { get; set; }
		public double BenchmarkXirr1Y { get; set; }
		public double Xirr3Y { get; set; }
		public double BenchmarkXirr3Y { get; set; }
		public double Xirr5Y { get; set; }
		public double BenchmarkXirr5Y { get; set; }
		public double TwrrSi { get; set; }
		public double BenchmarkTwrrSi { get; set; }
		public double Twrr1M { get; set; }
		public double BenchmarkTwrr1M { get; set; }
		public double Twrr3M { get; set; }
		public double BenchmarkTwrr3M { get; set; }
		public double Twrr6M { get; set; }
		public double BenchmarkTwrr6M { get; set; }
		public double TwrrYtd { get; set; }
		public double BenchmarkTwrrYtd { get; set; }
		public double Twrr1Y { get; set; }
		public double BenchmarkTwrr1Y { get; set; }
		public double Twrr3Y { get; set; }
		public double BenchmarkTwrr3Y { get; set; }
		public double Twrr5Y { get; set; }
		public double BenchmarkTwrr5Y { get; set; }
	}
}
