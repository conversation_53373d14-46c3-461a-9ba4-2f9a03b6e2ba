﻿using Actlogica.AlphaPortfolios.ApiContracts.Benchmarks;
using Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Bonds;
using Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.DirectEquity;
using Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.MutualFund;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Portfolio
{
	public class PortfolioCalcInput
	{
		public List<DirectEquityInvestmentCalcInput> DirectEquityInvestmentsInput { get; set; }
		public List<MutualFundInvestmentCalcInput> MutualFundInvestmentsInput { get; set; }
		public List<BondInvestmentCalcInput> BondInvestmentsInput { get; set; }
		public IEnumerable<PortfolioCapitalRegister> CapitalRegister { get; set; }
		public IEnumerable<PortfolioCashLedger> Ledger { get; set; }
		public BenchmarkDetails PortfolioBenchmarkDetails { get; set; }
		public Portfolios.Portfolio Portfolio { get; set; }
		public PortfolioCashPosition PortfolioCashPosition { get; set; }
		public DateTime AsOnDate { get; set; }
		public DateTime CalculationDate { get; set; }
	}
}
