﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.DirectEquity;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.DirectEquity
{
	public class DirectEquityInvestmentCalcInput
	{
		public Investment Investment { get; set; }
		public IEnumerable<DirectEquityPrice> PriceHistory { get; set; }
		public EquityCompanyMaster SecurityMaster { get; set; }
		public DirectEquityInvestmentMarketValue MarketValue { get; set; }
		public DateTime AsOnDate { get; set; }
	}
}
