﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.MutualFunds;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.Collections.Generic;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.MutualFund
{
	public class MutualFundInvestmentCalcInput
	{
		public Investment Investment { get; set; }
		public IEnumerable<MutualFundNav> PriceHistory { get; set; }
		public SchemeMasterDetails SecurityMaster { get; set; }
		public MutualFundInvestmentMarketValue MarketValue { get; set; }
		public DateTime AsOnDate { get; set; }
	}
}
