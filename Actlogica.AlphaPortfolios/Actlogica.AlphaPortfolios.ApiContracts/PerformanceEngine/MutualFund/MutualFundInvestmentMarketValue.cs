﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.MutualFund
{
	public class MutualFundInvestmentMarketValue
	{
		public string PartitionKey { get; set; }
		public string RowKey { get; set; }
		public string InvestmentId { get; set; }
		public string PortfolioId { get; set; }
		public string ModelPortfolioId { get; set; }
		public string ClientId { get; set; }
		public bool IsModelPortfolio { get; set; }
		public double TotalCapital { get; set; }
		public double TotalWithdrawals { get; set; }
		public double InvestedCapital { get; set; }
		public double MarketValue { get; set; }
		public double TotalCashFlow { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double DividendsPaid { get; set; }
		public double IrrSinceInception { get; set; }
		public double IrrCurrent { get; set; }
		public double AveragePrice { get; set; }
		public string BenchmarkName { get; set; }
		public double BenchmarkIrrSinceInception { get; set; }
		public double BenchmarkIrrCurrent { get; set; }
		public double ClosePrice { get; set; }
		public DateTime ClosePriceDate { get; set; }
	}
}
