﻿using Actlogica.AlphaPortfolios.Utils.Dates;
using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.HistoricalData
{
  public class HistoricalModelPortfolioRecord
  {
    public string ModelPortfolioId { get; set; }
    public string Name { get; set; }
    public DateTime AsAtDate { get; set; }
    public double TotalCapital { get; set; }
    public double InvestedCapital { get; set; }
    public double RealisedGainLoss { get; set; }
    public double UnRealisedGainLoss { get; set; }
    public double Dividends { get; set; }
    public double MarketValue { get; set; }
    public double AnnualReturnIrr { get; set; }
    public double AnnualReturnIrrUnrealised { get; set; }
    public double TwrrSinceInception { get; set; }
    public double AnnualPerformanceTwrr { get; set; }
    public double CurrentCashBalance { get; set; }
    public string AsAtDateIndex { get; set; }
  }
}
