﻿using System;
using Actlogica.AlphaPortfolios.Utils.Dates;

namespace Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.HistoricalData
{
  public class HistoricalModelPortfolioHolding
  {
    public DateTime AsAtDate { get; set; }
    public string InvestmentId { get; set; }
    public string ModelPortfolioId { get; set; }
    public string Name { get; set; }
    public string Symbol { get; set; }
    public string Isin { get; set; }
    public string AmfiCode { get; set; }
    public string Exchange { get; set; }
    public string FundClass { get; set; }
    public string Category { get; set; }
    public string AssetClass { get; set; }
    public string Rating { get; set; }
    public string SecurityType { get; set; }
    public string SecuritySubType { get; set; }
    public double ClosePrice { get; set; }
    public double AveragePrice { get; set; }
    public double TotalCapital { get; set; }
    public double InvestedCapital { get; set; }
    public double MarketValue { get; set; }
    public double Dividends { get; set; }
    public double TotalRealisations { get; set; }
    public double RealisedGainLoss { get; set; }
    public double UnRealisedGainLoss { get; set; }
    public double AnnualReturnIrr { get; set; }
    public double AnnualReturnIrrUnrealised { get; set; }
    public double TwrrSinceInception { get; set; }
    public double AnnualPerformanceTwrr { get; set; }
    public string AsAtDateIndex { get; set; }
  }
}
