﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloPortfolioProfitLossDaily
  {
    public string PortfolioId { get; set; }
    public string ClientId { get; set; }
    public double Contributions { get; set; }
    public double Withdrawals { get; set; }
    public double RealisedPurchaseCost { get; set; }
    public double RealisedGainLoss { get; set; }
    public double UnrealisedPurchaseCost { get; set; }
    public double UnrealisedGainLoss { get; set; }
    public double CurrentMarketValue { get; set; }
    public DateTime AsAtDate { get; set; }
    public double BeginningValue { get; set; }
    public double InvestedCapital { get; set; }
    public double AbsoluteGainsValue { get; set; }
    public double AbsoluteGainsPercentage { get; set; }
    public double AbsoluteReturnPercentage { get; set; }
    public double DividendsEarned { get; set; }
    public string CAGR { get; set; }
    public double DividendReInvested { get; set; }
    public double TotalCapitalExcludingSwitchIns { get; set; }
  }
}
