﻿using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloCustomer
  {
    public string Id { get; set; }
    public string FirstName { get; set; }
    public string MiddleName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string Phone { get; set; }
    public string Gender { get; set; }
    public DateTime DateOfBirth { get; set; }
    public int UserId { get; set; }
    public int ManagerId { get; set; }
    public List<FinFloPortfolio> Portfolio { get; set; }
    public DateTime? DateCreated { get; set; }
    public DateTime? LastUpdatedDate { get; set; }
    public int ProductsCount { get; set; }
    public string PaymentPlatform { get; set; }
    public string Family_Id { get; set; }
    public string Occupation { get; set; }
    public string LeadType { get; set; }
    public string LeadStatus { get; set; }
    public string AccountStatus { get; set; }
    public string Ucc { get; set; }
    public bool IsInvitationSent { get; set; }
    public bool IsOMSEnabled { get; set; }

    public decimal CashBalance { get; set; }
    public string Address { get; set; }

    public string GST { get; set; }
    public string DPId { get; set; }
    public string DPAccount { get; set; }
    public string address1 { get; set; }
    public string address2 { get; set; }
    public string address3 { get; set; }
    public string address4 { get; set; }
    public string pincode { get; set; }
    public string state { get; set; }
    public string mobile { get; set; }

    public double ConsolidatedPortfolioValue { get; set; }

    public bool AOFToBSE { get; set; }
    public bool DPPOA { get; set; }
    public bool TriPartyAgreement { get; set; }
    public bool Nominations { get; set; }
    public bool ECN { get; set; }
    public bool LPOA { get; set; }

    public DateTime? DPAccountOpenDate { get; set; }
    public bool NonIndividual { get; set; }
    public bool IsLegacyCashAccountUpdated { get; set; }
    public string Pan { get; set; }
    public string DematUcc { get; set; }
    public string Aadhar { get; set; }
    public bool IsMinor { get; set; }
    public bool IsFeeEnabled { get; set; }
    public string AccountArn { get; set; }
    public bool IsCustodianEnabled { get; set; }

    public string UserName { get; set; }
    public string PrimaryAdvisor { get; set; }
    public int PrirmaryEmployeeId { get; set; }

    public bool IsPrimaryEmployee { get; set; }
  }
}
