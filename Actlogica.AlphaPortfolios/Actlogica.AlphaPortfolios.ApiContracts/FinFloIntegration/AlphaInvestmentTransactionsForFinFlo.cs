﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class AlphaInvestmentTransactionsForFinFlo
  {
    public string Id { get; set; }
    public string Exchange { get; set; }
    public string Symbol { get; set; }
    public DateTime TransactionDate { get; set; }
    public string Type { get; set; }
    public string SubType { get; set; }
    public double Quantity { get; set; }
    public double MarketRate { get; set; }
    public double Amount { get; set; }
    public double Brokerage { get; set; }
    public double TaxAndCharges { get; set; }
    public string Name { get; set; }
    public string MFFolio { get; set; }
    public string Isin { get; set; }
    public string AmfiCode { get; set; }
    public string SecurityType { get; set; }
  }
}
