﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloEquityTransactionDetails
  {
    public string Id { get; set; }
    public string RowKey { get; set; }
    public string AccountNumber { get; set; }
    public string Symbol { get; set; }
    public string Exchange { get; set; }
    public string StockName { get; set; }
    public string TransactionId { get; set; }
    public DateTime TransactionDate { set; get; }
    public string TransactionType { set; get; }
    public string TransactionSubType { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal NetRate { get; set; }
    public double Quantity { set; get; }
    public string BrokeragePerUnit { get; set; }
    public double OtherCharges { get; set; }
    public decimal GrossRatePerUnit { get; set; }
    public string BrokerName { get; set; }
    public double TransactionAmount { get; set; }
    public double UnSettledSellQuantity { get; set; }
    public string Status { get; set; }
    public string ClientId { get; set; }
    public string UserName { get; set; }
    public string ProductId { get; set; }
    public decimal StockPrice { get; set; }
    public string PortfolioId { get; set; }
    public string PortfolioName { get; set; }


    public double Trxtax { get; set; }
    public double Sebitax { get; set; }
    public double Stt { get; set; }
    public double Gst { get; set; }
    public double Kkctax { get; set; }
    public double Sbctax { get; set; }
    public double Clgtax { get; set; }
    public double Stamp { get; set; }
    public string AccountType { get; set; }
    public bool IsForFee { get; set; }
    public DateTime UploadDate { set; get; }
    public int Result { set; get; }
  }
}
