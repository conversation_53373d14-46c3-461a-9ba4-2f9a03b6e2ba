﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
	public class FinFloPortfolioAnalyticsNewDaily
	{
		public string PortfolioId { get; set; }
		public string ClientId { get; set; }
    public string AssetType { get; set; }
		public int ProductType { get; set; }
		public DateTime AsAtDate { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double RealisedPurchaseCost { get; set; }
		public double UnRealisedPurchaseCost { get; set; }
		public double InvestedCapital { get; set; }
		public double TotalCapital { get; set; }
		public double Withdrawals { get; set; }
		public double NetWorth { get; set; }
		public double Change { get; set; }
		public double GrowthAt10000 { get; set; }
		public double GrowthAt10000InPercentage { get; set; }
		public double Twrr { get; set; }
		public string AggregateType { get; set; }
		public string CategoryName { get; set; }
		public int NumberOfInvestments { get; set; }
		public double Xirr { get; set; }
		public double AbsoluteGainsPercentage { get; set; }
		public double AbsoluteGainsValue { get; set; }
		public double OneDayGainLossValue { get; set; }
		public double OneDayGainLossPercentage { get; set; }
		public DateTime FirstTransactionDate { get; set; }
		public double AbsoluteReturnPercentage { get; set; }
		public double DividendsEarned { get; set; }
		public string CAGR { get; set; }
		public double TotalCashflow { get; set; }
		public double DividendReInvested { get; set; }
		public double XirrUnrealised { get; set; }
		public double AbsoluteReturnPercentageUnrealised { get; set; }
		public double Weight { get; set; }
		//Product
		public string AssetClass { get; set; }
		public string Exchange { get; set; }
		public string IndexName { get; set; }
		public string Industry { get; set; }
		public string Symbol { get; set; }
		public string MarketCap { get; set; }
		public string CreditRating { get; set; }
		public double AllUnits { get; set; }
		public double ShortTermUnits { get; set; }
		public double LongTermUnits { get; set; }
		public double AvgPurchasePrice { get; set; }
		public double LatestPrice { get; set; }
		public int SchemeCode { get; set; }
		public string ProductName { get; set; }
		public string ImageUrl { get; set; }
		public DateTime FirstDateOfInvestment { get; set; }
		public DateTime InvestmentDateNew { get; set; }
		public DateTime LatestNavDate { get; set; }
		public string NavDates { get; set; }
		public string AmcName { get; set; }
		public string ClassName { get; set; }
		public string ProductId { get; set; }
		public string ISIN { get; set; }
		public string BenchmarkName { get; set; }
		public double BenchmarkReturnValue { get; set; }
		public double BenchmarkReturnValueSinceInception { get; set; }
		public DateTime DateOfMaturity { get; set; }
	}
}
