﻿using System;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloMfTransactionDetails
  {
    public string Id { get; set; }
    public string ClientId { get; set; }
    public string FolioNumber { get; set; }
    public int TransactionType { get; set; }
    public string TransactionId { get; set; }
    public DateTime TransactionDate { get; set; }
    public decimal TransactionAmount { get; set; }
    public string SchemeId { get; set; }
    public decimal Units { get; set; }
    public decimal UnitPrice { get; set; }
    public string BrokeragePerUnit { get; set; }
    public decimal OtherCharges { get; set; }
    public decimal NetRatePerUnit { get; set; }
    public string BrokerName { get; set; }
    public string BrokerCode { get; set; }
    public string TradeAccountNumber { get; set; }
    public string ProductCode { get; set; }
    public string AmcCode { get; set; }
    public string AmcName { get; set; }
    public string MutualFundName { get; set; }
    public string Status { get; set; }
    public string PortfolioName { get; set; }
    public decimal STT { get; set; }
    public decimal Tax { get; set; }
    public decimal Total_Tax { get; set; }
    public decimal IGST_Amount { get; set; }
    public decimal CGST_Amount { get; set; }
    public decimal SGST_Amount { get; set; }
    public decimal TrnxsCharges { get; set; }
    public bool IsDemat { get; set; }
    public double UnSettledRedeemUnits { get; set; }
    public string RedeemStatus { get; set; }
    public string ISINNo { get; set; }
    public string AccountType { get; set; }
    public string HoldingType { get; set; }
    public bool IsForFee { get; set; }
    public string TransactionSubType { get; set; }
    public string PortfolioId { get; set; }
    public int Result { get; set; }
    public string RTASchemeCode { get; set; }
    public string Pan { get; set; }
    public DateTime UploadDate { get; set; }
    public string Source { get; set; }
  }
}
