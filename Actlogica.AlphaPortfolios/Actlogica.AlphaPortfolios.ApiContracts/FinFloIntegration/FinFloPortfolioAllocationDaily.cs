﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloPortfolioAllocationDaily
  {
		public DateTime AsAtDate { get; set; }
		public string AllocationAnalysisType { get; set; }
		public string Type { get; set; }
		public double Value { get; set; }
		public double Weight { get; set; }
		public double InvestedCapital { get; set; }
		public string PortfolioId { get; set; }
		public string ClientId { get; set; }
  }
}
