﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloPortfolioAoiwDaily
  {
		public string PortfolioId { get; set; }
		public DateTime AsAtDate { get; set; }
		public double OpeningValue { get; set; }
		public double SubsequentInvestment { get; set; }
		public double CurrentValueOfInvestedWealth { get; set; }
		public double Realizations { get; set; }
		public double RealisedGainLoss { get; set; }
		public double UnRealisedGainLoss { get; set; }
		public double DividendsPaid { get; set; }
		public DateTime PortfolioStartDate { get; set; }
		public double DividendsReInvested { get; set; }
		public double CapitalInvested { get; set; }
		public double TotalCapitalInvested { get; set; }
		public double NetCapitalInvested { get; set; }
		public double CurrentCapitalInvested { get; set; }
		public double Xirr { get; set; }
		public double XirrUnrealised { get; set; }
		public double BlendedBenchmarkReturn { get; set; }
		public double AbsoluteReturnPercentage { get; set; }
	}
}
