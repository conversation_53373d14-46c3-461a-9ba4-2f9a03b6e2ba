﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloPortfolio
  {
    public string Id { get; set; }
    public Guid Code { get; set; }
    public int UserId { get; set; }
    public DateTime? DateCreated { get; set; }
    public int PortfolioState { get; set; }
    public decimal NetWorth { get; set; }

    public DateTime PortfolioStartDate { get; set; }
    public decimal CapitalCommitment { get; set; }
    public Boolean IsDefault { get; set; }
    public string SeriesNumber { get; set; }
    public DateTime? CommitmentDate { get; set; }
    public Boolean HasCashAccount { get; set; }
    public string Name { get; set; }

    public string Customer_Id { get; set; }

    public int FeeStrategy { get; set; }

    public string FamilyOfficeAdvisor_Id { get; set; }
    public bool IsDefaultInvestmentPortfolio { get; set; }
    public string InvestmentBenchmark { get; set; }
    public bool IsProduct { get; set; }
    public bool IsAlphaPortfolio { get; set; }
    public string ProductMasterId { get; set; }
  }
}
