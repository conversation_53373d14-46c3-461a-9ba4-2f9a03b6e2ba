﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ApiContracts.FinFloIntegration
{
  public class FinFloPortfolioNetWorthDaily
  {
    public string PortfolioId { get; set; }
    public string ClientId { get; set; }
    public double NetWorth { get; set; }
    public DateTime AsAtDate { get; set; }
    public double GrowthOf10000 { get; set; }
    public double GrowthOf10000InPercentage { get; set; }
    public double Twrr { get; set; }
    public double Xirr { get; set; }
    public double OneDayGainLossValue { get; set; }
    public double OneDayGainLossPercentage { get; set; }
    public double CashBalance { get; set; }
    public double TotalCashflow { get; set; }
    public double DividendsEarned { get; set; }
    public double DividendReInvested { get; set; }
    public double BlendedBenchmarkReturn { get; set; }
    public double XirrUnrealised { get; set; }
    public double TwrrUnrealised { get; set; }
    public double NonMarketAssetsNetworth { get; set; }
		public DateTime PortfolioStartDate { get; set; }
	}
}
