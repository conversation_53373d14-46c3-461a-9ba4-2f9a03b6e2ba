﻿using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using Actlogica.AlphaPortfolios.ApiContracts.Imports.LegacyData;
using Actlogica.AlphaPortfolios.Data.Configs;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.OrderMgmt;
using Actlogica.AlphaPortfolios.Data.Repositories.MasterDataDb;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage;
using Actlogica.AlphaPortfolios.ServiceIntegration.Configs;
using Actlogica.AlphaPortfolios.ServiceIntegration.Imports.LegacyData;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using AutoMapper;
using System.Drawing.Text;

var mapper = new MapperConfiguration((Action<IMapperConfigurationExpression>)(mc =>
{
	mc.AddProfile((Profile)new DataMappingProfiles());
	mc.AddProfile((Profile)new ServiceMappingProfiles());
})).CreateMapper();
var masterDataDbConnStr = "Data Source=masterdatadbsvr-si.database.windows.net;Initial Catalog=marketdata-db-uat;Persist Security Info=False;User ID=productdbadmin;Password=***************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
var templateConnectionString = "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=<tenant>;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
var commonDataConnStr = "DefaultEndpointsProtocol=https;AccountName=finflocommondata;AccountKey=****************************************************************************************;";

Console.WriteLine("Hello! This console allows you to import BankBook data in bulk across multiple clients.");

Console.WriteLine("Start by selecting an environment. Please enter the name of the tenant. For Testing=testing, for UAT=sandbox for Production=rhpms:");
var tenantName = Console.ReadLine();


var dbConnectionString = templateConnectionString.Replace("<tenant>", tenantName);
var dbContext = new AlphaPortfolioDbContext(dbConnectionString);

var clientRepo = new ClientRepository(dbContext);
var strategyRepo = new StrategyRepository(dbContext);
var strategyModelRepo = new StrategyModelRepository(dbContext);
var portfolioCashLedgerRepo = new PortfolioCashLedgerRepository(dbContext);
var portfolioCapRegRepo = new PortfolioCapitalRegisterRepository(dbContext);
var portfolioRepo = new PortfolioRepository(dbContext);

var investmentRepo = new InvestmentRepository(dbContext);
var investmentTxnRepo = new InvestmentTransactionRepository(dbContext);
var mfInvestmentRepo = new MutualFundInvestmentRepository(dbContext);
var mfInvestmentTxn = new MutualFundTransactionRepository(dbContext);
var poolCapRegRepo = new PoolCapitalRegisterRepository(dbContext);
var poolCashRegRepo = new PoolCashLedgerRepository(dbContext);
var tradeOrderUnsettledAmountRepo = new TradeOrderUnsettledAmountsRepository(dbContext);

var modelPortfolioRepo = new ModelPortfolioRepository(dbContext);
var invstRepo = new InvestmentRepository(dbContext);
var invTxnRepo = new InvestmentTransactionRepository(dbContext);
var directEquityMasterDataRepo = new DirectEquityMasterDataRepository(masterDataDbConnStr);
var mfMasterRepo = new MutualFundMasterDataRepository(masterDataDbConnStr);
var bondMasterRepo = new BondMasterRepository(masterDataDbConnStr);
var portfolioSharingConfig = new PortfolioDistributorSharingRepository(dbContext);

var indexDataStorageSvc = new MarketIndexDataStorageRepository();
var securityMasterService = new SecurityMasterService("https://investa.actlogica.com/", "43fc59fb04234a26b7b166bc852b7672",
	bondMasterRepo, directEquityMasterDataRepo, mfMasterRepo, indexDataStorageSvc, dbContext);
var equityHistoryRepo = new EquityHistoryRepository();
var portfolioService = new PortfolioService(mapper, invstRepo, investmentTxnRepo,
	mfInvestmentRepo, mfInvestmentTxn, poolCapRegRepo, poolCashRegRepo, portfolioCapRegRepo,
	portfolioCashLedgerRepo, portfolioRepo, modelPortfolioRepo, securityMasterService, tradeOrderUnsettledAmountRepo, clientRepo, portfolioSharingConfig);
var generalSettingRepo = new GeneralSettingRepository(dbContext);

Console.WriteLine("\nPlease paste the path of the folder to import data from");
Console.WriteLine("\nNote: Only XLSX files are accepted!");
var rootDirectory = Console.ReadLine();
if (string.IsNullOrEmpty(rootDirectory))
	return;

Console.WriteLine("Reading files from directory.");
var filesInDirectory = Directory.GetFiles(rootDirectory);
Console.WriteLine($"{filesInDirectory.Count()} files found.");
filesInDirectory.ToList().ForEach(file => Console.WriteLine($"{file}"));

Console.WriteLine("Please type \"Yes\" and hit enter key to start the migration process.");
var proceedYesOrNo = Console.ReadLine();

if (string.IsNullOrWhiteSpace(proceedYesOrNo) || proceedYesOrNo.ToLower() != "yes")
{
	Console.WriteLine("Invalid input. Process aboring...");
	Console.WriteLine("Enter any key to shut down...");
	Console.ReadLine();
	return;
}
Console.WriteLine("===========================================================================================================================");

Console.WriteLine("Reading bulk income and expenses file.");
var incomeExpenseFilePath = filesInDirectory.Where(file => file.Contains("BankBook") && file.Contains("Import-Input")).FirstOrDefault();
if (incomeExpenseFilePath == null)
{
	Console.WriteLine("BabkBook input template not found in directory. Process aboring...");
	Console.WriteLine("Enter any key to shut down...");
	Console.ReadLine();
	return;
}
var incomeExpenseImporter = new IncomeAndExpensesImporter(clientRepo, strategyRepo, portfolioCashLedgerRepo, strategyModelRepo, portfolioRepo, modelPortfolioRepo);
var mappedLedgerTxns = await incomeExpenseImporter.LoadBankBookDataFromFile(incomeExpenseFilePath);

var clientsToSkip = GetClientCodesToSkip();

var apClientCodesInFile = mappedLedgerTxns.GroupBy(crTxn => crTxn.APClientCode).Select(client => client.Key).ToList();

var apClientCodes = apClientCodesInFile.Distinct();

Console.WriteLine($"Beginning import data for {apClientCodes.Count()} clients at {DateTime.Now}.");
var watch = new System.Diagnostics.Stopwatch();
watch.Start();
int importClientNumber = 0;

try
{
	foreach (var apClientCode in apClientCodes)
	{
		importClientNumber++;
		if (clientsToSkip.Contains(apClientCode))
		{
			Console.WriteLine($"*************************************************************************");
			Console.WriteLine($"Skipping client {apClientCode}");
			Console.WriteLine($"*************************************************************************");
			continue;
		}

		var allClientStrategyCodes = mappedLedgerTxns.Where(cg => cg.APClientCode == apClientCode).GroupBy(c => c.ClientCode).Select(g => g.Key).ToList();

		var uniqueClientStrategyCodes = allClientStrategyCodes.Distinct();

		foreach (var clientStrategyCode in uniqueClientStrategyCodes)
		{
			Console.WriteLine($"Starting ClientStrategyCode: {clientStrategyCode} for {importClientNumber} out of {apClientCode.Count()} clients at {DateTime.Now}.");
			using (var transaction = await dbContext.Database.BeginTransactionAsync())
			{
				try
				{
					var ledgerEntriesForMigration = new List<Actlogica.AlphaPortfolios.Data.Entity.Db.PortfolioCashLedger>();

					Console.WriteLine($"Beginning Income/Expenses transactions import for Client Strategy Code {clientStrategyCode}.");
					if (mappedLedgerTxns.Count() > 0)
					{
						ledgerEntriesForMigration.AddRange(
							await incomeExpenseImporter.ImportBankBookTransactions(mappedLedgerTxns.Where(txn => txn.ClientCode == clientStrategyCode)));
					}
					Console.WriteLine($"Income & expenses import complete. Imported {ledgerEntriesForMigration.Count} records.");
					Console.WriteLine("-----------------------------------------------");

					Console.WriteLine($"Beginning CashLedger transactions import for Client Strategy Code {clientStrategyCode}.");

					if (!ledgerEntriesForMigration.Any())
					{
						Console.WriteLine($"No ledger entries for Client Strategy Code {clientStrategyCode}. Skipping....");
						continue;
					}

					if (ledgerEntriesForMigration.Any())
					{
						var ledgerBuilder = new PortfolioLedgerBuilder(ledgerEntriesForMigration.FirstOrDefault().PortfolioId,
							portfolioCashLedgerRepo, portfolioService);
						await ledgerBuilder.ClearAndDirectImportWithOutRUnningBalance(ledgerEntriesForMigration);
						Console.WriteLine($"Cash ledger import complete. Imported {ledgerEntriesForMigration.Count()} records.");
					}

					await transaction.CommitAsync();
					Console.WriteLine($"Import completed successfully for AP Client Code {clientStrategyCode}.");
					Console.WriteLine("=============================================");

				}
				catch (Exception ex)
				{
					await transaction.RollbackAsync();
					Console.WriteLine($"Import for {clientStrategyCode} as errored. Rolled back this client data import. Exception: {ex.Message}");
					Console.WriteLine("If you wish to continue, hit the enter button on your keyboard or close the console.");
					Console.ReadLine();
				}
			}

		}

	}
	watch.Stop();
	Console.WriteLine($"Import completed successfully for {apClientCodes.Count()} clients in {watch.ElapsedMilliseconds / 1000}s. You may now close the console.");
	Console.WriteLine("===========================================================================================================================");

}
catch (Exception ex)
{

}





static List<string> GetClientCodesToSkip()
{
	var apClientCodesToSkip = new List<string>();
	apClientCodesToSkip.Add("");

	return apClientCodesToSkip;
}