﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Documents.SystemFunctions;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Middleware
{
	// You may need to install the Microsoft.AspNetCore.Http.Abstractions package into your project
	public class TenantSecurityMiddleware
	{
		private readonly RequestDelegate _next;

		public TenantSecurityMiddleware(RequestDelegate next)
		{
			_next = next;
		}

		public Task Invoke(HttpContext httpContext)
		{
			var authHeader = httpContext.Request.Headers["Authorization"];

			if(!authHeader.IsNull())
			{
				//var userClaims = httpContext.User.FindFirst(ClaimTypes)
			}
			return _next(httpContext);
		}
	}

	// Extension method used to add the middleware to the HTTP request pipeline.
	public static class TenantSecurityMiddleExtensions
	{
		public static IApplicationBuilder UseTenantSecurityMiddle(this IApplicationBuilder builder)
		{
			return builder.UseMiddleware<TenantSecurityMiddleware>();
		}
	}
}
