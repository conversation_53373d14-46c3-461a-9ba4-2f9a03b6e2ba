
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Actlogica.AlphaPortfolios.Api.Controllers;

[Controller]
public class AlphaBaseController : ControllerBase
{
    protected string orgId;
    protected string accessToken;
    protected string[] role;
    protected string email;
    protected string userName;
    private readonly IHttpContextAccessor _httpContextAccessor;
    protected string tenantName;

    protected string userId;
    public AlphaBaseController(IHttpContextAccessor contextAccessor)
    {
        _httpContextAccessor = contextAccessor;
        var idServerJwt = _httpContextAccessor.HttpContext.Request.Headers.TryGetValue("Authorization", out var token);
        if (idServerJwt)
        {
            var tokenString = token.ToString().Split(" ")[1];
            var stream = tokenString.Replace("Bearer ", "");
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadToken(stream);
            JwtSecurityToken tokenS = jsonToken as JwtSecurityToken;
            var jti = tokenS.Claims;
            orgId = jti.ToArray().FirstOrDefault(x => x.Type == "organisationId")?.Value;
            accessToken = stream;
            role = jti.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
            email = jti.ToArray().FirstOrDefault(x => x.Type == "email")?.Value;
            tenantName = jti.ToArray().FirstOrDefault(x => x.Type == "tenant")?.Value;
            userName = jti.ToArray().FirstOrDefault(x => x.Type == "name")?.Value;
            userId = jti.ToArray().FirstOrDefault(x => x.Type == "sub")?.Value;
        }
        else
        {
            throw new System.Exception("Not Authorized");
        }
    }
}