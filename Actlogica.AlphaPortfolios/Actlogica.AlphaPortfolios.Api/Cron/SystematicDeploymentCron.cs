using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Quartz;

namespace Actlogica.AlphaPortfolios.Api.Cron
{
    [Quartz.DisallowConcurrentExecutionAttribute()]
    public class SystematicDeploymentCron : IJob
    {

        private readonly ISystematicDeploymentService _systematicDeployment;
        public SystematicDeploymentCron(ISystematicDeploymentService systematicDeploymentService)
        {
            _systematicDeployment = systematicDeploymentService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            await _systematicDeployment.TriggerRedemptionsForToday();
            await _systematicDeployment.TriggerInstallmentsForToday();
        }


    }

}