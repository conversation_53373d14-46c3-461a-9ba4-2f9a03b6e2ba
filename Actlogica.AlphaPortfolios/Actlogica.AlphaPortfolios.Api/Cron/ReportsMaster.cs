using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting;
using Actlogica.AlphaPortfolios.Utils.Dates;
using Newtonsoft.Json;
using Quartz;

namespace Actlogica.AlphaPortfolios.Api.Cron
{
    public class ReportsMasterCron : IJob
    {


        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public ReportsMasterCron(IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var currentDate = DateTime.UtcNow;

            Console.WriteLine("Running Master Report Cron");
            //Fetch All the report config from database
            var reportCronConfig = await _reportsCronConfigRepository.GetAll();

            foreach (var config in reportCronConfig)
            {
                //If Any of the cron time is in back update it
                if (config.Time.Date < currentDate.Date)
                {
                    if (config.Frequency == ReportFrequency.Monthly)
                    {
                        config.Time = config.Time.AddDays(DateTime.DaysInMonth(currentDate.Year, currentDate.Month));
                    }
                    else if (config.Frequency == ReportFrequency.Weekly)
                    {
                        config.Time = config.Time.AddDays(7);
                    }
                    else
                    {
                        config.Time = config.Time.AddDays(1);

                    }
                    await _reportsCronConfigRepository.Update(config);
                }

                //If Report Status is Active Then Schedule It
                if (config.Status == ScheduledReportStatus.Active)
                {
                    switch (config.ReportType)
                    {

                        case ReportType.Mis_CapitalRegister:
                        case ReportType.Mis_ClientMaster:
                        case ReportType.Mis_Dividends:
                        case ReportType.Mis_Holdings:
                        case ReportType.Mis_IncomeExpense:
                        case ReportType.Mis_ModelPortfolioTransactions:
                        case ReportType.Mis_ModelPortfolioHoldingsLatest:
                        case ReportType.Mis_Trades:
                            Console.WriteLine("Triggering MIS Job");

                            await ConfigureCron("MisReportJob", "Group", config.Frequency, config.Time, config.PortfolioId, config.ClientId, context);
                            break;

                        case ReportType.HoldingDetails:
                            await ConfigureCron("HoldingDetailsJob", "Group", config.Frequency, config.Time, config.PortfolioId, config.ClientId, context);
                            break;

                        case ReportType.CapitalGains:

                            await ConfigureCron("CapitalGainsJob", "Group", config.Frequency, config.Time, config.PortfolioId, config.ClientId, context);
                            break;

                        case ReportType.CapitalRegister:
                            await ConfigureCron("CapitalRegisterJob", "Group", config.Frequency, config.Time, config.PortfolioId, config.ClientId, context);
                            break;

                        case ReportType.BankBook:
                            await ConfigureCron("BankBookJob", "Group", config.Frequency, config.Time, config.PortfolioId, config.ClientId, context);
                            break;

                        case ReportType.DividendStatement:
                            await ConfigureCron("DividendStatementJob", "Group", config.Frequency, config.Time, config.PortfolioId, config.ClientId, context);
                            break;

                        case ReportType.SebiQuarterly:
                            await ConfigureCron("SebiQuarterlyJob", "Group", config.Frequency, config.Time, config.PortfolioId, config.ClientId, context);
                            break;



                    }

                }

                //Update the Date to Next Date
                if (config.Frequency == ReportFrequency.Monthly)
                {
                    var isExecutionDayOfMonth = DateTime.Now.Day == config.Time.Day;
                    if (isExecutionDayOfMonth)
                    {
                        config.Time = config.Time.AddDays(DateTime.DaysInMonth(currentDate.Year, currentDate.Month));
                        await _reportsCronConfigRepository.Update(config);
                    }

                }
                else if (config.Frequency == ReportFrequency.Weekly)
                {

                    var isDayOfWeek = DateTime.Now.DayOfWeek == config.Time.DayOfWeek;
                    if (isDayOfWeek)
                    {
                        config.Time = config.Time.AddDays(7);
                        await _reportsCronConfigRepository.Update(config);
                    }
                }
                else
                {
                    if (config.Time.Date == currentDate.Date)
                    {
                        config.Time = config.Time.AddDays(1);
                        await _reportsCronConfigRepository.Update(config);
                    }
                }

            }

            await _reportsCronConfigRepository.Commit();
        }



        public static async Task ConfigureCron(string identity, string group, ReportFrequency reportFrequency, DateTime time, string portfolioId, string clientId, IJobExecutionContext jobExecutionContext)
        {

            DateTime currentDate = DateTime.UtcNow;
            DateTime executionTime = new DateTime(currentDate.Year, currentDate.Month, currentDate.Day,
                                                     time.Hour, time.Minute, time.Second);

            var difference = executionTime.Subtract(currentDate).TotalSeconds;

            var jobData = new JobDataMap
                {
                {"portfolioId", portfolioId},
                {"clientId", clientId}
                };


            switch (reportFrequency)
            {
                case ReportFrequency.Daily:
                    if (currentDate.Date == time.Date)
                    {
                        ITrigger misTrigger = TriggerBuilder.Create()
                                     .ForJob(new JobKey(identity))
                                     .WithIdentity(identity + "Trigger")
                                     .StartAt(DateTime.UtcNow.AddSeconds(difference))
                                     .Build();
                        await jobExecutionContext.Scheduler.ScheduleJob(misTrigger);

                    }
                    break;

                case ReportFrequency.Monthly:
                    //Check if the day is start of the month
                    var isExecutionDayOfMonth = DateTime.Now.Day == time.Day;
                    if (isExecutionDayOfMonth)
                    {
                        ITrigger trigger = TriggerBuilder.Create()
                        .ForJob(new JobKey(identity))
                        .WithIdentity(identity + "Trigger" + portfolioId + clientId)
                        .UsingJobData(jobData)
                        .StartAt(DateTime.UtcNow.AddSeconds(difference))
                        .Build();

                        await jobExecutionContext.Scheduler.ScheduleJob(trigger);
                    }
                    break;

                case ReportFrequency.Weekly:

                    var isDayOfWeek = DateTime.Now.DayOfWeek == time.DayOfWeek;
                    if (isDayOfWeek)
                    {
                        ITrigger trigger = TriggerBuilder.Create()
                        .ForJob(new JobKey(identity))
                        .WithIdentity(identity + "Trigger" + portfolioId + clientId)
                        .UsingJobData(jobData)
                        .StartAt(DateTime.UtcNow.AddSeconds(difference))
                        .Build();

                        await jobExecutionContext.Scheduler.ScheduleJob(trigger);
                    }

                    break;
                default:
                    break;
            }
        }

    }
}