using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting;
using Newtonsoft.Json;
using Quartz;

namespace Actlogica.AlphaPortfolios.Api.Cron
{

    public class HoldingDetailsReportsJob : IJob
    {

        private readonly IReportsService _reportService;
        private readonly IGeneralSettingService _generalSettings;

        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public HoldingDetailsReportsJob(IReportsService reportService, IGeneralSettingService generalSettings, IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportService = reportService;
            _generalSettings = generalSettings;
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var portoflioId = context.Trigger.JobDataMap.Get("portfolioId").ToString();
            var clientId = context.Trigger.JobDataMap.Get("clientId").ToString();

            var generalSettings = await _generalSettings.GetAll();

            var tenantName = generalSettings.FirstOrDefault(v => v.Key == "TenantName");
            var asAtDate = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture); ;

            var requestPayload = new { AsAtDate = asAtDate, Format = $"{ReportFormat.Xlsx}", fromDate = asAtDate, toDate = asAtDate, PortfolioId = portoflioId };
            var reportRequest = new ReportRequest
            {
                PortfolioId = portoflioId,
                ReportType = ReportType.CapitalGains.ToString(),
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                TenantName = tenantName.Value,
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                Format = ReportFormat.Xlsx.ToString()

            };
            await _reportService.InitiateReports(reportRequest);


        }

    }

    public class CapitalGainsReportsJob : IJob
    {
        private readonly IReportsService _reportService;
        private readonly IGeneralSettingService _generalSettings;

        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public CapitalGainsReportsJob(IReportsService reportService, IGeneralSettingService generalSettings, IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportService = reportService;
            _generalSettings = generalSettings;
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            //CG Require ClientId
            var portoflioId = context.Trigger.JobDataMap.Get("portfolioId").ToString();
            var clientId = context.Trigger.JobDataMap.Get("clientId").ToString();

            var generalSettings = await _generalSettings.GetAll();
            var tenantName = generalSettings.FirstOrDefault(v => v.Key == "TenantName");
            var asAtDate = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture); ;

            var requestPayload = new { AsAtDate = asAtDate, Format = $"{ReportFormat.Xlsx}", ClientId = clientId, fromDate = asAtDate, toDate = asAtDate, PortfolioId = portoflioId };
            var reportRequest = new ReportRequest
            {
                PortfolioId = portoflioId,
                ReportType = ReportType.CapitalGains.ToString(),
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                TenantName = tenantName.Value,
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                Format = ReportFormat.Xlsx.ToString()

            };
            await _reportService.InitiateReports(reportRequest);
        }

    }

    public class CapitalRegisterReportsJob : IJob
    {

        private readonly IReportsService _reportService;
        private readonly IGeneralSettingService _generalSettings;

        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public CapitalRegisterReportsJob(IReportsService reportService, IGeneralSettingService generalSettings, IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportService = reportService;
            _generalSettings = generalSettings;
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var portoflioId = context.Trigger.JobDataMap.Get("portfolioId").ToString();
            var clientId = context.Trigger.JobDataMap.Get("clientId").ToString();
            var generalSettings = await _generalSettings.GetAll();

            var tenantName = generalSettings.FirstOrDefault(v => v.Key == "TenantName");
            var asAtDate = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture); ;

            var requestPayload = new { AsAtDate = asAtDate, Format = $"{ReportFormat.Xlsx}", ClientId = clientId, fromDate = asAtDate, toDate = asAtDate, PortfolioId = portoflioId };
            var reportRequest = new ReportRequest
            {
                PortfolioId = portoflioId,
                ReportType = ReportType.CapitalRegister.ToString(),
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                TenantName = tenantName.Value,
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                Format = ReportFormat.Xlsx.ToString()

            };
            await _reportService.InitiateReports(reportRequest);


        }

    }

    public class BankBookReportsJob : IJob
    {

        private readonly IReportsService _reportService;
        private readonly IGeneralSettingService _generalSettings;

        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public BankBookReportsJob(IReportsService reportService, IGeneralSettingService generalSettings, IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportService = reportService;
            _generalSettings = generalSettings;
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var portoflioId = context.Trigger.JobDataMap.Get("portfolioId").ToString();
            var clientId = context.Trigger.JobDataMap.Get("clientId").ToString();
            var generalSettings = await _generalSettings.GetAll();

            var tenantName = generalSettings.FirstOrDefault(v => v.Key == "TenantName");
            var asAtDate = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture); ;

            var requestPayload = new { AsAtDate = asAtDate, Format = $"{ReportFormat.Xlsx}", ClientId = clientId, fromDate = asAtDate, toDate = asAtDate, PortfolioId = portoflioId };
            var reportRequest = new ReportRequest
            {
                PortfolioId = portoflioId,
                ReportType = ReportType.BankBook.ToString(),
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                TenantName = tenantName.Value,
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                Format = ReportFormat.Xlsx.ToString()

            };
            await _reportService.InitiateReports(reportRequest);


        }

    }

    public class DividendStatementReportsJob : IJob
    {

        private readonly IReportsService _reportService;
        private readonly IGeneralSettingService _generalSettings;

        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public DividendStatementReportsJob(IReportsService reportService, IGeneralSettingService generalSettings, IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportService = reportService;
            _generalSettings = generalSettings;
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            var portoflioId = context.Trigger.JobDataMap.Get("portfolioId").ToString();
            var clientId = context.Trigger.JobDataMap.Get("clientId").ToString();
            var generalSettings = await _generalSettings.GetAll();

            var tenantName = generalSettings.FirstOrDefault(v => v.Key == "TenantName");
            var asAtDate = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture); ;

            var requestPayload = new { AsAtDate = asAtDate, Format = $"{ReportFormat.Xlsx}", ClientId = clientId, fromDate = asAtDate, toDate = asAtDate, PortfolioId = portoflioId };
            var reportRequest = new ReportRequest
            {
                PortfolioId = portoflioId,
                ReportType = ReportType.DividendStatement.ToString(),
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                TenantName = tenantName.Value,
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                Format = ReportFormat.Xlsx.ToString()

            };
            await _reportService.InitiateReports(reportRequest);


        }

    }

    public class SebiQuarterlyReportsJob : IJob
    {

        private readonly IReportsService _reportService;
        private readonly IGeneralSettingService _generalSettings;

        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public SebiQuarterlyReportsJob(IReportsService reportService, IGeneralSettingService generalSettings, IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportService = reportService;
            _generalSettings = generalSettings;
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {

            var portoflioId = context.Trigger.JobDataMap.Get("portfolioId").ToString();
            var clientId = context.Trigger.JobDataMap.Get("clientId").ToString();
            var generalSettings = await _generalSettings.GetAll();

            var tenantName = generalSettings.FirstOrDefault(v => v.Key == "TenantName");
            var asAtDate = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture); ;

            var requestPayload = new { AsAtDate = asAtDate, Format = $"{ReportFormat.Xlsx}", ClientId = clientId, fromDate = asAtDate, toDate = asAtDate, PortfolioId = portoflioId };
            var reportRequest = new ReportRequest
            {
                PortfolioId = portoflioId,
                ReportType = ReportType.SebiQuarterly.ToString(),
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                TenantName = tenantName.Value,
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                Format = ReportFormat.Xlsx.ToString()

            };
            await _reportService.InitiateReports(reportRequest);

        }

    }
}