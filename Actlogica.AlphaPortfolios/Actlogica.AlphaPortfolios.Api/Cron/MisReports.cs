using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting;
using Newtonsoft.Json;
using Quartz;

namespace Actlogica.AlphaPortfolios.Api.Cron
{
    public class MisReportJob : IJob
    {

        private readonly IReportsService _reportService;
        private readonly IGeneralSettingService _generalSettings;

        private readonly IReportsCronConfigRepository _reportsCronConfigRepository;
        public MisReportJob(IReportsService reportService, IGeneralSettingService generalSettings, IReportsCronConfigRepository reportsCronConfigRepository)
        {
            _reportService = reportService;
            _generalSettings = generalSettings;
            _reportsCronConfigRepository = reportsCronConfigRepository;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Console.WriteLine("Executing MIS Job");
            var generalSettings = await _generalSettings.GetAll();
            var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");

            var tenantName = generalSettings.FirstOrDefault(v => v.Key == "TenantName");

            var capitalRegisterPayload = GetCapitalRegisterRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var holdingDetailsPayload = GetHoldingsRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var dividendsPayload = GetDividendsRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var incomeExpensePayload = GetIncomeExpenseRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var clientMasterPayload = GetClientMasterRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var tradesPayload = GetTradesRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var transactionPayload = GetTransactionsRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var modelTransactionsPayLoad = GetModelPortfolioTransactionsRequestPayload(tenantName.Value, misEmailRecipients.Value);
            var modelHoldingsPayload = GetModelPortfolioHoldingsLatestRequestPayload(tenantName.Value, misEmailRecipients.Value);

            await _reportService.InitiateReports(holdingDetailsPayload);
            await _reportService.InitiateReports(capitalRegisterPayload);
            await _reportService.InitiateReports(dividendsPayload);
            await _reportService.InitiateReports(incomeExpensePayload);
            await _reportService.InitiateReports(clientMasterPayload);
            await _reportService.InitiateReports(tradesPayload);
            await _reportService.InitiateReports(transactionPayload);
            await _reportService.InitiateReports(modelTransactionsPayLoad);
            await _reportService.InitiateReports(modelHoldingsPayload);

        }


        public ReportRequest GetCapitalRegisterRequestPayload(string tenantName, string misEmailRecipients)
        {

            var asAt = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
            var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

            var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}" };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_CapitalRegister}",
                RequestDate = System.DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };

            return request;
        }

        public ReportRequest GetHoldingsRequestPayload(string tenantName, string misEmailRecipients)
        {

            var asAt = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
            var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

            var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}" };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_Holdings}",
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };

            return request;
        }

        public ReportRequest GetDividendsRequestPayload(string tenantName, string misEmailRecipients)
        {

            var asAt = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
            var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);
            var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}" };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_Dividends}",
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };


            return request;
        }

        public ReportRequest GetIncomeExpenseRequestPayload(string tenantName, string misEmailRecipients)
        {

            var asAt = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
            var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);
            var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}" };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_IncomeExpense}",
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };

            return request;
        }

        public ReportRequest GetClientMasterRequestPayload(string tenantName, string misEmailRecipients)
        {
            var asAt = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
            var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

            var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}" };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_ClientMaster}",
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };

            return request;
        }

        public ReportRequest GetTradesRequestPayload(string tenantName, string misEmailRecipients)
        {
            var fromDateTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);
            var toDateTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);

            var requestPayload = new { AsAtDate = toDateTime, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}", FromDate = fromDateTime, ToDate = toDateTime };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_Trades}",
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };
            return request;
        }


        public ReportRequest GetTransactionsRequestPayload(string tenantName, string misEmailRecipients)
        {
            var fromDateTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);
            var toDateTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);

            var requestPayload = new { AsAtDate = toDateTime, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}", FromDate = fromDateTime, ToDate = toDateTime };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_Transactions}",
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };
            return request;
        }
        public ReportRequest GetModelPortfolioTransactionsRequestPayload(string tenantName, string misEmailRecipients)
        {
            var fromDateTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);
            var toDateTime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);

            var requestPayload = new { AsAtDate = toDateTime, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}", FromDate = fromDateTime, ToDate = toDateTime };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_ModelPortfolioTransactions}",
                RequestDate = DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };
            return request;

        }
        public ReportRequest GetModelPortfolioHoldingsLatestRequestPayload(string tenantName, string misEmailRecipients)
        {

            var asAt = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd");
            var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

            var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients, Format = $"{ReportFormat.Xlsx}" };

            var request = new ReportRequest
            {
                ClientId = "Not Applicable",
                Format = $"{ReportFormat.Xlsx}",
                PortfolioId = "Not Applicable",
                ReportType = $"{ReportType.Mis_ModelPortfolioHoldingsLatest}",
                RequestDate = System.DateTime.UtcNow,
                RequestedBy = "Cron",
                Status = $"{ReportStatus.Submitted}",
                RequestPayload = JsonConvert.SerializeObject(requestPayload),
                TenantName = tenantName
            };

            return request;
        }

    }


}