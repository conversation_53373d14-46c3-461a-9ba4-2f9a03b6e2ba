{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
    "Serilog": {
      "Using": [ "Serilog.Sinks.MongoDB" ],
      "MinimumLevel": "Debug",
      "WriteTo": [
        {
          "Name": "MongoDB",
          "Args": {
            "connectionString": "mongodb://localhost:27017/logs",
            "collectionName": "Log"
          }
        }
      ]
    },
  "PerformanceEngineConfigs": {
    "PortfolioPerfFnSecretKey": ">3|~<i('<Y&2#Fo[z>!D",
    "PortfolioPerfRequestQueue": "<tenantname>.alphap.perfengine.request",
    "ClientAgreementContainerName": "alpha-portfolio-client-agreement-file"
  },
  "ReportsConfigs": {
    "ReportBlobContainerName": "alphap-pdf-reports",
    "ReportRequestQueue": "<tenantname>.alphap.report.request"
  },
  "TransformerSettings": {
    //"Url": "https://localhost:7294/api"
    "Url": "https://act-transformer-api.azurewebsites.net/api"
  },
  "DataUpdateConfigs": {
    "DataUpdateFileContainerName" : "alpha-portfolio-dataupdates-file",
    "DataUpdateQueue" : "<tenantname>.alphap.dataupdate.request"
  },
  "FeeTriggerConfigs": {
    "FeeTriggerQueue" : "<tenantname>.alphap.feestrigger.request"
  },
  "AllowedHosts": "*",
  "AllowedCors": "http://localhost:4200;https://alphap-uat.actlogica.com",
 "Storage": {
    "TradeFileContainerName": "alpha-user-uploads",
    "AlphaTradeFileContainerName": "alpha-portfolio-trade-files",
    "PortfolioHoldingContainerName": "alpha-portfolio-holdingupdate-files",
    "ClientAgreementContainerName": "alpha-portfolio-client-agreement-file",
    "AllocationFileContainerName": "alpha-portfolio-allocation-files",
    "OrderSettlementFiles": "alpha-portfolio-settlement-files",
    "HoldingReconFileContainerName": "alpha-holding-recon",
    "CapitalRegisterFileContainerName": "alpha-portfolio-capital-register-file",
    "IncomeExpenseFileContainerName": "alpha-portfolio-income-expense-file",
    "DataUpdateFileContainerName": "alpha-portfolio-dataupdates-file",
    "DistributionCenterFileContainerName" : "alpha-distributor-files"
  },
  "Ndpms":{
    "NdpmsUrl" : "https://<tenantname>.alphap.actlogica.com/ndpms/order-approval/<encryptedToken>",
    "NdpmsAesKey" : "1014938661793608",
    "NdpmsAesIV" : "7583246749728994"
  },
  "SendGrid":{
    "SendGridApiKey" : "*********************************************************************",
    "SendGridApiUrl" : "https://api.sendgrid.com/v3/mail/send"
  },
  "PerformaceEngineV2":{
    "V2PerformanceEngineUrl" : "https://storage.<tenantname>.alphap.actlogica.com"
  },
  "IntegrationProviders": {
    "MfMasterDataApi": "https://finflo-masterdata-app.azurewebsites.net",
    "MfMasterDataApiKey": "vwg@ayf4dqv!uxr1NUQ"
  },
  "ConnectionStrings": {
    //"DefaultConnection": "Server=localhost\\SQLEXPRESS;Database=alphaportfolios-local-db;Trusted_Connection=True;MultipleActiveResultSets=true",

    "DefaultConnection": "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=testing;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"DefaultConnection": "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=uat;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"DefaultConnection": "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=righthorizons;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"DefaultConnection": "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=zenwealth;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"DefaultConnection": "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=indiaavenue;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"DefaultConnection": "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=carneliancapital;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"DefaultConnection": "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=goldstandard;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "TenantStorageConnectionString":"DefaultEndpointsProtocol=https;AccountName=alphapdev;AccountKey=****************************************************************************************;",
    // "TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphaptesting;AccountKey=****************************************************************************************;",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapuat;AccountKey=****************************************************************************************;",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphaprighthorizons;AccountKey=****************************************************************************************;",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapzenwealth;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapindiaavenue;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapcarneliancapital;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapgoldstandard;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapmoneygrow;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphappilot;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapprofitgate;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    //"TenantStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=alphapcarneliancapital2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",

    "MasterDataDbConnectionString": "Data Source=masterdatadbsvr-si.database.windows.net;Initial Catalog=marketdata-db-uat;Persist Security Info=False;User ID=productdbadmin;Password=***************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "MarketDataStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=finflocommondata;AccountKey=****************************************************************************************;",
    "IdentityDbConnectionString": "Server=tcp:finflo-tenantsdbserver-prod.database.windows.net,1433;Initial Catalog=AccountsDb-Uat;Persist Security Info=False; User ID=tenantsdbuser; Password=***********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    //"IdentityDbConnectionString": "Server=tcp:finflo-tenantsdbserver-prod.database.windows.net,1433;Initial Catalog=AccountsDb;Persist Security Info=False; User ID=tenantsdbuser; Password=***********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;",
    "AlphaPServiceBusConnectionString": "Endpoint=sb://alphap-actlogica.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=RFKSLAqDrLeFI1GM/qYPvzE4yDL/uCJgU+ASbK9iVRw=",
    "AzureEmailConnectionString": "endpoint=https://alphap-comms.india.communication.azure.com/;accesskey=LcKSjhxT+V1IRpjiJj1/QZdOUuHupkKt+ECrhbd5e71KMJW9ukE5UzNqXvBAJptEaWqEYXIt5flQ+QNYInvOYw=="
  },
  "IdentityServerSettings": {
    "DiscoveryUrl": "https://accounts-uat.actlogica.com",
    //"DiscoveryUrl": "https://accounts.actlogica.com",
    "ClientName": "m2m.client",
    "ClientPassword": "ClientSecret1",
    "UseHttps": true
  }
}
