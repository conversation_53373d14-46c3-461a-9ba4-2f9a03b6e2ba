using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Api.Utilities
{
    public class ValidValuesAttribute : ValidationAttribute
    {
        private readonly HashSet<string> _validValues;

        public ValidValuesAttribute(params string[] validValues)
        {
            _validValues = new HashSet<string>(validValues);
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value is IEnumerable<string> values)
            {
                var distinctValues = new HashSet<string>();
                foreach (var val in values)
                {
                    if (!_validValues.Contains(val))
                    {
                        return new ValidationResult($"Invalid value: {val}. Allowed values are: {string.Join(", ", _validValues)}");
                    }
                    if (!distinctValues.Add(val))
                    {
                        return new ValidationResult($"Duplicate value: {val}. Each value must be unique.");
                    }
                }
                return ValidationResult.Success;
            }
            return new ValidationResult("Invalid input. Expected a Array of strings.");
        }
    }


}