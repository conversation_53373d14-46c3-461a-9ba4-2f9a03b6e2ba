
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Actlogica.AlphaPortfolios.Api.Utilities
{
    public class QueryParameterRequirement(string queryParameterName, string controllerName) : IAuthorizationRequirement
    {
        public string QueryParameterName { get; } = queryParameterName;

        public string ControllerName { get; } = controllerName;
    }

    public class QueryParameterHandler : AuthorizationHandler<QueryParameterRequirement>
    {
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, QueryParameterRequirement requirement)
        {
            if (context.Resource is HttpContext httpContext)
            {
                if (requirement.ControllerName == "TradeIdea")
                {
                    List<string> rolesCanAccessTradeIdea = ["AlphaAccountsAdmin", "AlphaAdmin", "CIO", "PrincipleOfficer", "FundManager", "ResearchAnalyst",];
                    List<string> rolesCanAccessSubmittedTradeIdea = ["AlphaAccountsAdmin", "AlphaAdmin", "CIO", "PrincipleOfficer", "FundManager", "ResearchAnalyst", "OperationManager"];
                    List<string> rolesCanAccessExecuteTradeIdea = ["AlphaAccountsAdmin","AlphaAdmin", "CIO", "PrincipleOfficer", "FundManager", "ResearchAnalyst", "OperationManager", "DealerDeskTrader"];

                    var queryParamValue = httpContext.Request.Query[requirement.QueryParameterName].FirstOrDefault();
                    if (queryParamValue == null)
                    {
                        context.Fail();
                        return Task.CompletedTask;
                    }

                    var splitValue = queryParamValue.Split(":");
                    if (splitValue.Length != 2)
                    {
                        context.Fail();
                        return Task.CompletedTask;
                    }

                    var status = splitValue[1];

                    if (status.Equals("draft", StringComparison.CurrentCultureIgnoreCase)|| status.Equals("abandoned", StringComparison.CurrentCultureIgnoreCase))
                    {
                        //Only allow if user has the required roles
                        foreach (var role in rolesCanAccessTradeIdea)
                        {
                            var isInRole = context.User.IsInRole(role);
                            if (isInRole)
                            {
                                context.Succeed(requirement);
                                return Task.CompletedTask;
                            }
                        }
                    }
                    else if (status.Equals("senttofm", StringComparison.CurrentCultureIgnoreCase) || status.Equals("rejected", StringComparison.CurrentCultureIgnoreCase) )
                    {
                        //Only allow if user has the required roles
                        foreach (var role in rolesCanAccessSubmittedTradeIdea)
                        {
                            var isInRole = context.User.IsInRole(role);
                            if (isInRole)
                            {
                                context.Succeed(requirement);
                                return Task.CompletedTask;
                            }
                        }

                    }
                    else if (status.Equals("approved", StringComparison.CurrentCultureIgnoreCase))
                    {
                        //Only allow if user has the required roles
                        foreach (var role in rolesCanAccessExecuteTradeIdea)
                        {
                            var isInRole = context.User.IsInRole(role);
                            if (isInRole)
                            {
                                context.Succeed(requirement);
                                return Task.CompletedTask;
                            }
                        }
                    }
                    else
                    {
                        context.Fail();
                    }


                }
            }

            return Task.CompletedTask;
        }
    }


    public class QueryParameterAuthorizeAttribute : AuthorizeAttribute, IAuthorizationFilter
    {
        private readonly string _queryParameterName;

        public QueryParameterAuthorizeAttribute(string queryParameterName)
        {
            _queryParameterName = queryParameterName;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var queryParamValue = context.HttpContext.Request.Query[_queryParameterName].FirstOrDefault();
            context.HttpContext.Items[_queryParameterName] = queryParamValue;
        }
    }


}