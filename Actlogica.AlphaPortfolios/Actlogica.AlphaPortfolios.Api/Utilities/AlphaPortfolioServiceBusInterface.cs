﻿using Actlogica.AlphaPortfolios.ApiContracts.PerformanceEngine.Portfolio;
using Azure.Messaging.ServiceBus;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Utilities
{
	public static class AlphaPortfolioServiceBusInterface
	{
		public static async Task TriggerPortfolioPerformanceEngineRun(string tenantName, string portfolioId, 
			DateTime asAtDate, string serviceBusConnStr, string queueName)
		{
			var client = new ServiceBusClient(serviceBusConnStr);
			var sender = client.CreateSender(queueName);

			var payload = new PortfolioPerfRequestPayload
			{
				PortfolioId = portfolioId,
				AsAtDate = asAtDate.Date,
				TenantName = tenantName,
				PortfolioPerfSecretKey = ">3|~<i('<Y&2#Fo[z>!D"
			};

			var message = new ServiceBusMessage(JsonConvert.SerializeObject(payload));
			await sender.SendMessageAsync(message);
		}
	}
}
