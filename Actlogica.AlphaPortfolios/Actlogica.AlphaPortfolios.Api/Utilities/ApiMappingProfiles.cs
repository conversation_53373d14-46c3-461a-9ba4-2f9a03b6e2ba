using System.Collections.Generic;
using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using Actlogica.AlphaPortfolios.ApiContracts.Fees;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.ApiContracts.UserManagement;
using AutoMapper;

namespace Actlogica.AlphaPortfolios.Api.Utilities
{
  public class ApiMappingProfiles : Profile
  {
    public ApiMappingProfiles()
    {
      CreateMap<Data.Entity.Db.BankReconProcessResultData, BRDownloadFile>()
        .ForMember(d => d.REMARKS, o => o.MapFrom(s => s.Description))
                .ForMember(d => d.CLIENDCODE, o => o.MapFrom(s => s.ClientCode))
                .ForMember(d => d.TRANSACTIONTYPE, o => o.MapFrom(s => s.TransactionTypeBank ?? s.TransactionTypeLedger))
                .ForMember(d => d.ACQUISITIONDATE, o => o.MapFrom(s => s.TransactionDate))
                .ForMember(d => d.SETTLEMENTDATE, o => o.MapFrom(s => s.PostingDate))
                .ForMember(d => d.STTAMOUNT, o => o.MapFrom(s => s.BankInputAmount <= 0 ? s.PortfolioCashLedgerAmount : s.BankInputAmount))
                .ForMember(d => d.TRANSREF, o => o.MapFrom(s => s.Description))
                .ReverseMap();


      CreateMap<StrategicDeploymentBody, ApiContracts.Orders.StrategicDeployment>().ReverseMap();

      CreateMap<CustodianCreation, Custodian>().ReverseMap();
      CreateMap<BrokerCreation, Broker>().ReverseMap();
      CreateMap<CapitalRegisterCreation,PortfolioCapitalRegister>().ReverseMap();
      CreateMap<IncomeExpenseEntryCreation,PortfolioCashLedger>().ReverseMap();
      CreateMap<DistributorMasterCreation,DistributorMaster>().ReverseMap();
      CreateMap<DistributionUserCreation,ActlogicaUser>().ReverseMap();
      CreateMap<DistributionUserCreation,DistributionUser>().ReverseMap();
      CreateMap<DistributorMasterSharingConfigurationCreation,DistributorMasterSharingConfiguration>().ReverseMap();
      CreateMap<DistributorMasterEmpanelmentDetailsCreation,DistributorMasterEmpanelmentDetails>().ReverseMap();
      CreateMap<DistributorAttachmentsCreation,DistributorMasterDocuments>().ReverseMap();
      CreateMap<DistributorMasterBranchCreation,DistributorMasterBranch>().ReverseMap();
      CreateMap<DistributorMasterStatutoryCreation,DistributorMasterStatutory>().ReverseMap();
      CreateMap<DistributorMasterCerficationCreation,DistributorMasterCertfications>().ReverseMap();
      CreateMap<DistributorMasterPayoutDetailsCreation,DistributorMasterPayoutDetails>().ReverseMap();
      CreateMap<DistributorMasterRegionCreation,DistributorMasterRegion>().ReverseMap();
      CreateMap<DistributorMasterBranchesAMCCreation,DistributorAMCBranch>().ReverseMap();
      CreateMap<DistributorMasterRegionsAMCCreation,DistributorAMCRegion>().ReverseMap();
      CreateMap<DistributorAttachmentsCreation,DistributorMasterDocuments>().ReverseMap();
      CreateMap<PortfolioDistributorSharingCreation,PortfolioDistributorSharingConfigurations>().ReverseMap();
      CreateMap<PortfolioForeignBankDetails,PortfolioForeignBankCreation>().ReverseMap();

      CreateMap<ClientBrokerBody, ClientBroker>().ReverseMap();
      CreateMap<PortfolioBankBody, ClientBank>().ReverseMap();
      CreateMap<ClientCustodianBody, ClientCustodian>().ReverseMap();
      CreateMap<PortfolioBody, Portfolio>().ReverseMap();
      CreateMap<PortfolioRMDetailsBody, PortfolioRMDetail>().ReverseMap();
      CreateMap<PortfolioNomineeDetailsBody, PortfolioNomineeDetails>().ReverseMap();
      CreateMap<PortfolioPreferenceBody, PortfolioPreference>().ReverseMap();
      CreateMap<PortfolioTransactionsPreferenceBody, PortfolioTransactionsPrefernce>().ReverseMap();
      CreateMap<PortfolioFeeTemplateBody, PortfolioFeeTemplate>().ReverseMap();
    }


  }
}
