using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;

namespace Actlogica.AlphaPortfolios.Api.Types
{
  public class IncomeExpenseEntryCreation
  {
    public string PortfolioId {get;set; }
    public TransactionType TransactionType { get; set; }
    public TransactionSubType TransactionSubType { get; set; }
    public double Amount { get; set; }
    public double RunningBalance { get; set; }
    public string Description { get; set; }
    public bool IsModelPortfolio { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime SettlementDate { get; set; }

  }
}
