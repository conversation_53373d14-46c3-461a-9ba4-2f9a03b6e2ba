
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
using System.Collections.Generic;
namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class DistributorMasterPayoutDetailsCreation
    {
        public string DistributorMasterId { get; set; }

        public string Frequency { get; set; }

        public string PayoutBank { get; set; }
        public string PayoutBankAccNo { get; set; }
        public string PayoutBankBranch { get; set; }

        public string PayoutBankIFSC { get; set; }

        public string PayoutBankType { get; set; }


        public string PayoutBankMIRC { get; set; }

        public bool PennyDropDone { get; set; }

        public DateTime ValidFrom { get; set; }

        public DateTime? ValidTo { get; set; }

        public string Status { get; set; }

        public bool IsDefault { get; set; }
    }
}