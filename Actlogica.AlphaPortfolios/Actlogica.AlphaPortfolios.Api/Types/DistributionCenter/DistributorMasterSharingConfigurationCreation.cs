
using System;


namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class DistributorMasterSharingConfigurationCreation
    {
        public string DistributorMasterId { get; set; }
        public string StratergyId { get; set; }
        public double FixedFeeSharingPercentage { get; set; }
        public double PerformanceFeeSharingPercentage { get; set; }
        public double UpFrontFeeSharingPercentage { get; set; }
        public double ExitLoadSharingPercentage { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Remarks { get; set; }
        public double AMCMinimumRetention { get; set; }
        
    }

}