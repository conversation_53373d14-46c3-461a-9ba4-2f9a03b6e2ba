
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
namespace Actlogica.AlphaPortfolios.Api.Types
{
  public class DistributorMasterCerficationCreation
  {
    public string DistributorMasterId { get; set; }
    public string TeamMemberName { get; set; }
    public string NISMCertification { get; set; }
    public string NISMCertificationType { get; set; }
    public DateTime NISMCertValidfrom { get; set; }
    public DateTime? NISMCertValidto { get; set; }
    public string Remarks { get; set; }
    public string Attachment { get; set; }

  }
}
