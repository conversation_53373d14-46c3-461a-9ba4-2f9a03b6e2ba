using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class DistributionUserCreation
    {
        public string DMUserId { get; set; }
        public string DistributorMasterId { get; set; }
        public int UserId { get; set; }
        public string EmployeeId { get; set; }
        [Required]
        public string FirstName { get; set; }
        [Required]
        public string LastName { get; set; }
        [Required]
		[EmailAddress(ErrorMessage = "Invalid Email Address")]        
        public string Email { get; set; }
        [Required]
		[RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Phone Number")]
        public string Mobile { get; set; }
        [Required]
        public string UserName { get; set; }
        public string Password { get; set; }
        public string AccessLevel { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Remarks { get; set; }
        public string Branch { get; set; }
        public double Aum { get; set; }
        public int TotalPortfolios { get; set; }
        public int TotalDistributorRm { get; set; }
        public string Region { get; set; }
        public string ReportTo { get; set; }
		 public string Email2 { get; set; }
        public string Mobile2 { get; set; }
        public DateTime? ResignedOn { get; set; }
		public bool LoginEnabled { get; set; }
        public string Status { get; set; }
		public string Designation { get; set; }
		public string Department { get; set; }
		public Gender Gender { get; set; }
		public string Salutation { get; set; }
		public string Role { get; set; }
        
    }
}