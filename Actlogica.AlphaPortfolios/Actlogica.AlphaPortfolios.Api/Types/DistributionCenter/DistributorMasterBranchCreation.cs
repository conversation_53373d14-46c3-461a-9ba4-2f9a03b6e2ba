
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using System;
using System.Collections.Generic;
namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class DistributorMasterBranchCreation
    {
       public string DistributorMasterId { get; set; }
		public string BranchName { get; set; }
		public string Email { get; set; }
		public string Mobile { get; set; }
		public string Address1 { get; set; }
		public string Address2 { get; set; }
		public string City { get; set; }
		public string State { get; set; }
		public string PinCode { get; set; }
		public string Country { get; set; }
		public string GSTNo { get; set; }
		public string Status { get; set; }

    }
}
