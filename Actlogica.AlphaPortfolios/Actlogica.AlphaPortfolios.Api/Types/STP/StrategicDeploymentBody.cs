using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class StrategicDeploymentBody
    {
        [Required]
        public string ClientId { get; set; }
        [Required]
        public string SourcePortfolioId { get; set; }
        [Required]
        public string DestinationPortfolioId { get; set; }
        [Required]
        public string Type { get; set; }
        [Required]
        public int NoOfInstallments { get; set; }
        public double InstallmentAmount { get; set; }
        public double InstallmentPercentage { get; set; }
    }
}
