

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.Api.Utilities;
using Microsoft.Azure.Amqp.Framing;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class PeakMarginAutomaticBody : IValidatableObject
    {
        [Required, MinLength(1, ErrorMessage = "You Should Send Atleast One Portfolio Id")]

        public List<string> PortfolioId { set; get; }

        [Required]
        [Range(1, 100)]
        public double PeakMarginPct { set; get; }


        [Required, MinLength(1, ErrorMessage = "Atleast One Status is Required")]
        [ValidValues("Draft", "SentToExchange", ErrorMessage = "Status can Only be Draft or SentToExchange")]
        public List<string> Status { set; get; }

        [Required]

        public DateTime FromDate { get; set; }

        [Required]
        public DateTime ToDate { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (FromDate > ToDate)
            {
                yield return new ValidationResult("FromDate should be less than ToDate.", new[] { "FromDate", "ToDate" });
            }
        }


    }


    public class PeakMarginManualBody
    {
        [Required, MinLength(1, ErrorMessage = "You Should Send Atleast One Portfolio Id")]
        public List<string> PortfolioId { set; get; }

        [Required, Range(1, double.MaxValue, ErrorMessage = "Field Amount Should be Greater than zero")]
        public double Amount { set; get; }

        [Required]
        [Range(1, 100)]
        public double PeakMarginPct { set; get; }

    }


}