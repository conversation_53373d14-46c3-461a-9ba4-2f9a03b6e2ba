

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class ScheduleReportsCron
    {
        [Required]
        public ReportType ReportType { get; set; }

        [Required]
        public ReportFrequency Frequency { get; set; }

        [Required]
        public ReportsFormat ReportFormat { get; set; }

        [Required]
        public DateTime Time { get; set; }

        public string PortfolioId { get; set; }

        [Required]
        public string ClientId { get; set; }

    }



}