using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class CreatePortfolio
    {

        [Required]
        public PortfolioBody Portfolio { set; get; }

        [Required]
        public IEnumerable<PortfolioNomineeDetailsBody> PortfolioNomineeDetailsBody { set; get; }

        public ClientBrokerBody Broker { set; get; }

        [Required]
        public PortfolioBankBody ClientBankBody { set; get; }

        [Required]
        public PortfolioTransactionsPreferenceBody PortfolioTransactionsPreference { set; get; }

        public PortfolioFeeTemplateBody PortfolioFeeTemplateBody { set; get; }

        public IEnumerable<PortfolioRMDetailsBody> PortfolioRMDetail { set; get; }

        [Required]
        public PortfolioPreferenceBody PortfolioPreference { set; get; }

        [Required]
        public ClientCustodianBody ClientCustodian { set; get; }

        public IEnumerable<ClientOrderEntry> Orders { get; set; }
        public double CapitalToDeploy { get; set; }
        public string DistributorMasterId { get; set; }
        public PortfolioDistributorSharingCreation PortfolioDistributorSharingCreation { get; set; }
        public DateTime FromDate { get; set; }
        public PortfolioForeignBankCreation PortfolioForeignBankCreation { get; set; }

    }

}