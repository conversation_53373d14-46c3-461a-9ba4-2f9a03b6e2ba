

using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class PortfolioBody
    {
        [Required]
        public string ClientId { get; set; }

        [Required]
        public string ModelId { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 3)]
        public string ClientStrategyCode { get; set; }

        [Required]
        [StringLength(50, MinimumLength = 3)]
        public string CustodianPortfolioCode { get; set; }

        [StringLength(50, MinimumLength = 3)]
        public string FAAccountNo { get; set; }

        [Required]
        public PortfolioType PortfolioType { get; set; }

        [Required]
        public AccountStatus AccountStatus { get; set; }

        [Required]
        public ModeOfOperation ModeOfOperation { get; set; }

        [Required]
        public TradingMode TradingMode { get; set; }

        [Required]
        public FundSettlementMode FundSettlementMode { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string StockSettlementMode { get; set; }

        [Required]
        public bool POAOnBank { get; set; }

        [Required]
        public bool POAOnDemat { get; set; }

        [Required]
        public bool POAOnMF { get; set; }
        
        [Required]
        public DateTime StartDate { get; set; }

    }

}
