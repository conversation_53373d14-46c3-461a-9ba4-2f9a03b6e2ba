

using System;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class PortfolioTransactionsPreferenceBody
    {
        public bool? FixedIncome { get; set; }
        public bool? Equity { get; set; }
        public bool? MutualFund { get; set; }
        public bool? IPO { get; set; }
        public bool? Derivative { get; set; }
        public bool? Others { get; set; }
        public bool? UnListedEquity { get; set; }
        public bool? DebtInstruments { get; set; }
        public DateTime ValidFromDate { get; set; }
        public DateTime ValidToDate { get; set; }

    }

}