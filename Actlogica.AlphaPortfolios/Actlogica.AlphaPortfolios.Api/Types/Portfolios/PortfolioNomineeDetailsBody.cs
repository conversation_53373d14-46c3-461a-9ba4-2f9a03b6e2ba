
using System;
using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class PortfolioNomineeDetailsBody
    {

        [StringLength(100, MinimumLength = 3)]
        public string NomineeName { get; set; }
        public bool IsMinor { get; set; }
        public DateTime? NomineeDOB { get; set; }
        public NomineeRelationship? Relationship { get; set; }
        public string NomineeSharePercentage { get; set; }
        public string NomineeNo { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string GuardianName { get; set; }

        [StringLength(10, MinimumLength = 10)]
        public string GuardianPAN { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string GuardianAddress { get; set; }

        [StringLength(100, MinimumLength = 3)]
        public string TPAName { get; set; }
        public string NomineePanNo { get; set; }
        public string NomineeIdentityIDType { get; set; }
        public string NomineeIdentityIDNo { get; set; }
        public string NomineeAddressType { get; set; }
        public string NomineeAddressLine1 { get; set; }
        public string NomineeAddressLine2 { get; set; }
        public string NomineeAddressLine3 { get; set; }
        public string NomineeCity { get; set; }
        public string NomineeDistrict { get; set; }
        public string NomineeState { get; set; }
        public string NomineeCountry { get; set; }
        public string NomineePincode { get; set; }
        public string NomineeCountrycode { get; set; }
        public string NomineeMobileNo { get; set; }
        public string NomineeTelephoneOffice { get; set; }
        public string NomineeEmailIDPrimary { get; set; }
        public string GuardianIdentityIDType { get; set; }
        public string GuardianIdentityIDNo { get; set; }
        public string GuardianAddressType { get; set; }
        public string GuardianAddressLine1 { get; set; }
        public string GuardianAddressLine2 { get; set; }
        public string GuardianAddressLine3 { get; set; }
        public string GuardianCity { get; set; }
        public string GuardianDistrict { get; set; }
        public string GuardianState { get; set; }
        public string GuardianCountry { get; set; }
        public string GuardianPincode { get; set; }
        public string GuardianCountrycode { get; set; }
        public string GuardianMobileNo { get; set; }
        public string GuardianTelephoneOffice { get; set; }
        public string GuardianEmailIDPrimary { get; set; }
        public DateTime ValidFromDate { get; set; }
        public DateTime ValidToDate { get; set; }

    }

}