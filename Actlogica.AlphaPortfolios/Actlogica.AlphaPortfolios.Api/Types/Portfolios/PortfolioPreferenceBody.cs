using System.ComponentModel.DataAnnotations;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;

namespace Actlogica.AlphaPortfolios.Api.Types
{
    public class PortfolioPreferenceBody
    {
        public SendPortfolioReportBy? SendClientReport { get; set; }
        public SendAlertBy? SendAlert { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string SendTo { get; set; }
        public AcceptOrderRequest? AcceptOrderRequest { get; set; }
        public FinancialMonthStartfrom? FinancialMonthStartfrom { get; set; }

    }




}