using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.Api.Types
{
  public class CapitalRegisterCreation
  {
    [Required]
    public string PortfolioId { get; set; }
    public TransactionType TransactionType { get; set; }
    public TransactionSubType TransactionSubType { get; set; }
    public double Amount { get; set; }
    public string Description { get; set; }
    public bool IsModelPortfolio { get; set; }
    public DateTime TransactionDate { get; set; }
    public DateTime SettlementDate { get; set; }

  }
}
