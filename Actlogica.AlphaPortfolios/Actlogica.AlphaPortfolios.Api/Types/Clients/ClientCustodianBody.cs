using System;
using System.ComponentModel.DataAnnotations;

namespace Actlogica.AlphaPortfolios.ApiContracts.Clients
{
    public class ClientCustodianBody
    {
        [Required]
        public string CustodianId { get; set; }
        
        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string CustodyAccountNumber { get; set; }

        [Required]
        public DpType DPType { get; set; }

        [Required]
        public ModeOfHolding ModeofHolding { get; set; }

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string SecondHolderName { get; set; }

        [StringLength(100, MinimumLength = 2)]
        public string ThirdHolderName { get; set; }

        [StringLength(10, MinimumLength = 10)]
        public string SecondHolderPAN { get; set; }

        [StringLength(10, MinimumLength = 10)]
        public string ThirdHolderPAN { get; set; }

        [RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
        public string SecondHolderPhone { get; set; }

        [RegularExpression("^([6-9]{1})([0-9]{9})$", ErrorMessage = "Invalid Mobile Number")]
        public string ThirdHolderPhone { get; set; }

        [StringLength(100, MinimumLength = 1)]
        [EmailAddress(ErrorMessage = "Invalid Email")]
        public string SecondHolderEmail { get; set; }

        [StringLength(100, MinimumLength = 1)]
        [EmailAddress(ErrorMessage = "Invalid Email")]
        public string ThirdHolderEmail { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string MFUCCDemat { get; set; }

        [StringLength(50, MinimumLength = 1)]
        public string MFUCCPhysical { get; set; }
        public string DPID { get; set; }
        public DateTime ValidFromDate { get; set; }
        public DateTime ValidToDate { get; set; }

    }
}
