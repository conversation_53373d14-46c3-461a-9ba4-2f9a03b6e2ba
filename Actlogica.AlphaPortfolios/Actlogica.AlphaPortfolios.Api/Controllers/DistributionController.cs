using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using Actlogica.AlphaPortfolios.ApiContracts.UserManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.DistributionCenter;
using Actlogica.AlphaPortfolios.ServiceIntegration.UserManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using AutoMapper;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [Route("[controller]")]
    [Authorize]
    [ApiController]

    public class DistributionController : AlphaBaseController

    {
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IFileStorageService _fileStorageService;
        private readonly IDistributionCenterService _distributionCenterService;
        private readonly IDistributorMasterUsersRepository _distributorMasterUsersRepository;
        public DistributionController(IMapper mapper, IDistributionCenterService distributionCenterService, IDistributorMasterUsersRepository distributorMasterUsersRepository, IUserService userService, IFileStorageService fileStorageService, IHttpContextAccessor contextAccessor) : base(contextAccessor)
        {
            _mapper = mapper;
            _userService = userService;
            _fileStorageService = fileStorageService;
            _distributionCenterService = distributionCenterService;
            _distributorMasterUsersRepository = distributorMasterUsersRepository;
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddDistributorMasterWithUser([FromBody] DistributorMasterWithUserCreation distributorMasterWithUserCreation)
        {
            try
            {
                var distributorMasterEntity = await _distributionCenterService.CreateDistributorMaster(_mapper.Map<ApiContracts.DistributionCenter.DistributorMaster>(distributorMasterWithUserCreation.DistributorMaster));
                var distributorMasterId = distributorMasterEntity.Id;
                var newUser = _mapper.Map<ActlogicaUser>(distributorMasterWithUserCreation.DistributorUser);
                var orgIdClaim = User.Claims.FirstOrDefault(cl => cl.Type == "organisationId");
                var adminEmail = User.Claims.FirstOrDefault(cl => cl.Type == "email");
                var adminTenantName = User.Claims.FirstOrDefault(cl => cl.Type == "tenant");
                newUser.OrganisationId = Convert.ToInt16(orgIdClaim.Value);
                newUser.RoleId = [208];
                var createdUser = await _userService.AddNewUser(newUser, adminEmail.Value, adminTenantName.Value);
                distributorMasterWithUserCreation.DistributorUser.UserId = createdUser.Id;
                distributorMasterWithUserCreation.DistributorUser.DistributorMasterId = distributorMasterId;
                distributorMasterWithUserCreation.DistributorUser.Branch = distributorMasterWithUserCreation.DistributorMaster.BranchId;
                distributorMasterWithUserCreation.DistributorUser.Region = distributorMasterWithUserCreation.DistributorMaster.RegionId;
                distributorMasterWithUserCreation.DistributorUser.Status = "Inactive";
                var distributorUserEntity = await _distributionCenterService.CreateNewDistributorUser(_mapper.Map<ApiContracts.DistributionCenter.DistributionUser>(distributorMasterWithUserCreation.DistributorUser));
                return Ok(new { message = "Distributor Master Created Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create the Distributor Master : {e.Message} " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddDistributionUser([FromBody] DistributionUserCreation distributorUser)
        {

            try
            {
                var newUser = _mapper.Map<ActlogicaUser>(distributorUser);
                var orgIdClaim = User.Claims.FirstOrDefault(cl => cl.Type == "organisationId");
                var adminEmail = User.Claims.FirstOrDefault(cl => cl.Type == "email");
                var adminTenantName = User.Claims.FirstOrDefault(cl => cl.Type == "tenant");
                newUser.OrganisationId = Convert.ToInt16(orgIdClaim.Value);
                newUser.RoleId = [209];
                var createdUser = await _userService.AddNewUser(newUser, adminEmail.Value, adminTenantName.Value);
                distributorUser.UserId = createdUser.Id;
                var distributorData = await _distributorMasterUsersRepository.GetDistributorByUserID(userId);
                distributorUser.DistributorMasterId = distributorData.Id;
                distributorUser.LoginEnabled = false;
                distributorUser.Status = "Inactive";
                var distributorUserEntity = await _distributionCenterService.CreateNewDistributorUser(_mapper.Map<ApiContracts.DistributionCenter.DistributionUser>(distributorUser));
                return Ok(distributorUserEntity);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create Distribution User : {e.Message} " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddDistributorRmToAdmin([FromBody] DistributionUserCreation newDistributorUser)
        {

            try
            {
                var newUser = _mapper.Map<ActlogicaUser>(newDistributorUser);
                var orgIdClaim = User.Claims.FirstOrDefault(cl => cl.Type == "organisationId");
                var adminEmail = User.Claims.FirstOrDefault(cl => cl.Type == "email");
                var adminTenantName = User.Claims.FirstOrDefault(cl => cl.Type == "tenant");
                newUser.OrganisationId = Convert.ToInt16(orgIdClaim.Value);
                newUser.RoleId = [209];
                var createdUser = await _userService.AddNewUser(newUser, adminEmail.Value, adminTenantName.Value);
                newDistributorUser.UserId = createdUser.Id;
                newDistributorUser.LoginEnabled = false;
                newDistributorUser.Status = "Inactive";
                var distributorUserEntity = await _distributionCenterService.CreateNewDistributorUser(_mapper.Map<ApiContracts.DistributionCenter.DistributionUser>(newDistributorUser));
                return Ok(distributorUserEntity);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Add RM to Admin : {e.Message} " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddEmpanelment([FromBody] DistributorMasterEmpanelmentDetailsCreation distributorMasterEmpanelmentDetailsCreation)
        {

            try
            {
                await _distributionCenterService.CreateDistributorEmpanelment(_mapper.Map<ApiContracts.DistributionCenter.DistributorMasterEmpanelmentDetails>(distributorMasterEmpanelmentDetailsCreation));
                return Ok(new { message = "Empanelment Created Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create the Emapanelment : {e.Message} " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddFeeSharingConfiguration([FromBody] List<DistributorMasterSharingConfigurationCreation> distributorMasterSharingConfigurationCreation)
        {

            try
            {
                await _distributionCenterService.CreateDistributorMasterSharingConfigurations(_mapper.Map<List<ApiContracts.DistributionCenter.DistributorMasterSharingConfiguration>>(distributorMasterSharingConfigurationCreation));
                return Ok(new { message = "Fee Sharing Created Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create the Fee Sharing Configuration : {e.Message} " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddPayoutDetails([FromBody] DistributorMasterPayoutDetailsCreation distributorMasterPayoutDetailsCreation)
        {

            try
            {
                var distributorMasterPayoutDetails = await _distributionCenterService.CreateDistributorMasterPayout(_mapper.Map<ApiContracts.DistributionCenter.DistributorMasterPayoutDetails>(distributorMasterPayoutDetailsCreation));
                return Ok(distributorMasterPayoutDetails);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create the Payout Details : {e.Message} " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddBranch(string dmId, [FromBody] DistributorMasterBranchCreation branchCreation)
        {
            try
            {
                await _distributionCenterService.CreateBranch(_mapper.Map<ApiContracts.DistributionCenter.DistributorMasterBranch>(branchCreation), dmId);
                return Ok(new { message = "Branch Created Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create Branch : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddAMCBranch([FromBody] DistributorMasterBranchesAMCCreation branchCreation)
        {
            try
            {
                await _distributionCenterService.CreateAMCBranch(_mapper.Map<ApiContracts.DistributionCenter.DistributorAMCBranch>(branchCreation));
                return Ok(new { message = "AMC Branch Created Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create AMC Branch : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddRegion(string dmId, [FromBody] List<DistributorMasterRegionCreation> regionCreation)
        {
            try
            {

                await _distributionCenterService.CreateRegion(_mapper.Map<List<ApiContracts.DistributionCenter.DistributorMasterRegion>>(regionCreation), dmId);
                return Ok(new { message = "Region Created Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create Region : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddAMCRegion([FromBody] List<DistributorMasterRegionsAMCCreation> regionCreation)
        {
            try
            {
                await _distributionCenterService.CreateAMCRegion(_mapper.Map<List<ApiContracts.DistributionCenter.DistributorAMCRegion>>(regionCreation));
                return Ok(new { message = "AMC Region Created Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create AMC Region : {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        [Authorize(Roles = "AlphaDistributorAdmin,AlphaDistributorRelationshipManager")]
        public async Task<IActionResult> GetAllDistributorPortfolios()
        {
            try
            {
                var isInternal = role.Contains("AlphaDistributorRelationshipManager");

                if (isInternal)
                {
                    var rmPortfolios = await _distributionCenterService.GetPortfoliosOfRMByUserId(userId);
                    return Ok(rmPortfolios);
                }
                var distributorUser = await _distributorMasterUsersRepository.GetDistributorMasterAdminIdByUserId(userId);
                var distributorPortfolios = await _distributionCenterService.GetPortfoliosByDistributorId(distributorUser.Id);
                return Ok(distributorPortfolios);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllPortfoliosByDistributorId(string id)
        {
            try
            {
                var distributorPortfolios = await _distributionCenterService.GetPortfoliosByDistributorId(id);
                return Ok(distributorPortfolios);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetPortfolioMappingById(string id)
        {
            try
            {
                var distributorMappingPortfolios = await _distributionCenterService.GetPortfolioMappingById(id);
                return Ok(distributorMappingPortfolios);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpPut("[action]/{Id}")]
        public async Task<IActionResult> EditDistributorPortfolioMapping(string id, EditPortfolioMapping editPortfolioMapping)
        {
            try
            {
                await _distributionCenterService.EditPortfolioMapping(id, editPortfolioMapping);
                return Ok(new { message = "Mapping Updated Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update  : {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        [Authorize(Roles = "AlphaDistributorAdmin,AlphaDistributorRelationshipManager")]
        public async Task<IActionResult> GetAllDistributorClients()
        {
            try
            {
                var isRelationshipManager = role.Contains("AlphaDistributorRelationshipManager");

                if (isRelationshipManager)
                {
                    var rmClients = await _distributionCenterService.GetRmClients(Int32.Parse(userId));
                    return Ok(rmClients);
                }
                var masterUser = await _distributorMasterUsersRepository.GetDistributorByUserID(userId);
                var distributorClients = await _distributionCenterService.GetRmsByDistributorId(masterUser.Id);
                return Ok(distributorClients);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }


        [HttpGet("[action]")]
        public async Task<IActionResult> GetTopDistributorClients()
        {
            try
            {
                var isRelationshipManager = role.Contains("AlphaDistributorRelationshipManager");

                if (isRelationshipManager)
                {
                    var rmClients = await _distributionCenterService.GetTopRmClients(Int32.Parse(userId));
                    return Ok(rmClients);
                }
                var masterUser = await _distributorMasterUsersRepository.GetDistributorByUserID(userId);
                var distributorClients = await _distributionCenterService.GetTopDistributorClients(masterUser.Id);
                return Ok(distributorClients);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetRmsByDistributorId(string id)
        {
            try
            {

                var distributorClients = await _distributionCenterService.GetRmsByDistributorId(id);
                return Ok(distributorClients);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        [Authorize(Roles = "AlphaDistributorAdmin,AlphaDistributorRelationshipManager")]
        public async Task<IActionResult> GetAumDetails()
        {
            try
            {
                var isInternal = role.Contains("AlphaDistributorRelationshipManager");

                var userAumDetails = await _distributionCenterService.GetAumDetails(isInternal, userId);
                return Ok(userAumDetails);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllBranch()
        {
            try
            {
                var allBranches = await _distributionCenterService.GetAllBranches();
                return Ok(allBranches);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch Branches {e.Message} " });
            }
        }

        [HttpGet("[action]/{dmId}")]
        public async Task<IActionResult> GetAttachmentsByDmID(string dmId)
        {
            try
            {
                var attachments = await _distributionCenterService.GetDocumentsByDmId(dmId);
                return Ok(attachments);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Attachments {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAttachmentsById(string Id)
        {
            try
            {
                var attachments = await _distributionCenterService.GetDocumentById(Id);
                return Ok(attachments);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Attachments {e.Message} " });
            }
        }

        [HttpGet("[action]/{dmId}")]
        public async Task<IActionResult> GetBranchByDmId(string dmId)
        {
            try
            {
                var branchData = await _distributionCenterService.GetBranchesByDmId(dmId);
                return Ok(branchData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Branch {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetBranchForDm()
        {
            try
            {
                var dmId = await _distributorMasterUsersRepository.GetDistributorByUserID(userId);
                var branchData = await _distributionCenterService.GetBranchesByDmId(dmId.DistributorMasterId);
                return Ok(branchData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Branch {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetBranchById(string Id)
        {
            try
            {
                var branchData = await _distributionCenterService.GetBranchById(Id);
                return Ok(branchData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Branch {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetAMCBranchById(string Id)
        {
            try
            {
                var branchData = await _distributionCenterService.GetAMCBranchById(Id);
                return Ok(branchData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Branch {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllAMCBranch()
        {
            try
            {
                var branchData = await _distributionCenterService.GetAllAmcBranches();
                return Ok(branchData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Branch {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetAMCRegionById(string Id)
        {
            try
            {
                var regionAMC = await _distributionCenterService.GetAMCRegionById(Id);
                return Ok(regionAMC);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Branch {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllAMCRegion()
        {
            try
            {
                var regionAMC = await _distributionCenterService.GetAllAmcRegions();
                return Ok(regionAMC);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the AMC Regions {e.Message} " });
            }
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetRmDetailsById(string id)
        {
            try
            {
                var clients = await _distributionCenterService.GetRmById(id);
                return Ok(clients);

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddDistributorMasterSharingConfigurations([FromBody] List<DistributorMasterSharingConfigurationCreation> distributorSharingConfiguration)
        {
            try
            {
                await _distributionCenterService.CreateDistributorMasterSharingConfigurations(_mapper.Map<List<ApiContracts.DistributionCenter.DistributorMasterSharingConfiguration>>(distributorSharingConfiguration));
                return Ok(new { message = "Data Insertion Successful" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create Sharing Configuration {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddDistributorMasterStatutoryInfo([FromBody] DistributorMasterStatutoryCreation distributorMasterStatutoryCreation)
        {
            try
            {
                var distributorMasterStatutory = await _distributionCenterService.CreateDistributorMasterStatutoryInfo(_mapper.Map<ApiContracts.DistributionCenter.DistributorMasterStatutory>(distributorMasterStatutoryCreation));
                return Ok(new { message = "Data Insertion Successful", data = distributorMasterStatutory });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Insert the Data {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllDistributors()
        {
            try
            {
                var distributorMasters = await _distributionCenterService.GetAllDistributorsData();
                return Ok(distributorMasters);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]/{distributorId}")]
        public async Task<IActionResult> GetDistributorDetailsById(string distributorId)
        {
            try
            {
                var distributorData = await _distributionCenterService.GetDistributorById(distributorId);
                return Ok(distributorData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]/{distributorId}")]
        public async Task<IActionResult> GetEmpanelmentDetailsByDistributorId(string distributorId)
        {
            try
            {
                var distributorData = await _distributionCenterService.GetEmpanelmentsByDistributorId(distributorId);
                return Ok(distributorData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]/{distributorId}")]
        public async Task<IActionResult> GetPayoutDetailsByDmId(string distributorId)
        {
            try
            {
                var distributorMasterPayoutDetails = await _distributionCenterService.GetPayoutDetailsByDmId(distributorId);
                return Ok(distributorMasterPayoutDetails);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Payout Details {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetPayoutDetailsById(string Id)
        {
            try
            {
                var payoutDetails = await _distributionCenterService.GetPayoutDetailsById(Id);
                return Ok(payoutDetails);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Payout Details {e.Message} " });
            }
        }

        [HttpGet("[action]/{empanelmentId}")]
        public async Task<IActionResult> GetEmpanelmentDetailsById(string empanelmentId)
        {
            try
            {
                var distributorData = await _distributionCenterService.GetEmpanelmentsById(empanelmentId);
                return Ok(distributorData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Empanelements : {e.Message} " });
            }
        }

        [HttpGet("[action]/{dmId}")]
        public async Task<IActionResult> GetStrategiesByDmId(string dmId)
        {
            try
            {
                var distributorStrategies = await _distributionCenterService.GetStrategiesByDmId(dmId);
                return Ok(distributorStrategies);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Strategies : {e.Message} " });
            }
        }

        [HttpGet("[action]/{dmId}")]
        public async Task<IActionResult> GetNonAssignedStrategiesByDmId(string dmId)
        {
            try
            {
                var distributorStrategies = await _distributionCenterService.GetNonStrategiesByDmId(dmId);
                return Ok(distributorStrategies);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]/{distributorId}")]
        public async Task<IActionResult> GetStatutoryDetailsByDmId(string distributorId)
        {
            try
            {
                var distributorData = await _distributionCenterService.GetStatutoryDetailsByDistributorId(distributorId);
                return Ok(distributorData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Get Statutory Details : {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetStatutoryById(string Id)
        {
            try
            {
                var distributorData = await _distributionCenterService.GetStatutoryDetailsById(Id);
                return Ok(distributorData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Get Statutory Details : {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetMasterSharingConfiguration()
        {
            try
            {
                var masterSharingConfiguration = await _distributionCenterService.GetAllDistributorSharingConfigurations();
                return Ok(masterSharingConfiguration);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetMasterSharingConfigurationById(string Id)
        {
            try
            {
                var masterSharingConfiguration = await _distributionCenterService.GetSharingConfigurationById(Id);
                return Ok(masterSharingConfiguration);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetMasterSharingConfigurationByDmId(string dmId)
        {
            try
            {
                var masterSharingConfiguration = await _distributionCenterService.GetSharingConfigurationByDmId(dmId);
                return Ok(masterSharingConfiguration);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetDistributorMasterIdByUserId()
        {
            try
            {
                var distributorMaster = await _distributorMasterUsersRepository.GetDistributorByUserID(userId) ?? throw new Exception($"Distributor User Not Found with UserId: {userId}");
                var result = new
                {
                    id = distributorMaster.DistributorMasterId
                };
                return Ok(result);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Data {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AssignPortfolios(string rmId, [FromBody] List<AssignPortfolio> assignPortflio, string rmType, string rmHierarchy)
        {
            try
            {
                foreach (var portfolio in assignPortflio)
                {
                    await _distributionCenterService.AssignPortfolioToRm(rmId, portfolio.PortfolioId, portfolio.FromDate, rmType, rmHierarchy);
                }

                return Ok(new { message = "Portflio Assigned to RM Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Assign Portfolio  {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AssignPortfoliosToAdmin(string distributorId, [FromBody] List<AssignPortfolio> assignPortfolios)
        {
            try
            {
                foreach (var portfolio in assignPortfolios)
                {
                    await _distributionCenterService.AssignPortfolioToDistributor(distributorId, _mapper.Map<PortfolioDistributorSharingConfigurations>(portfolio.PortfolioDistributorSharingCreation), portfolio.PortfolioId, portfolio.FromDate);
                }
                return Ok(new { message = "Portfolio Assigned to Distributor Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Assign Portfolio  {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> Attachments([FromForm] DistributorAttachmentsCreation distributorAttachmentsData, IFormFile attachment)
        {
            try
            {
                var allowedContentTypes = new List<string>
                {
                    "application/pdf",
                    "application/vnd.ms-excel",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                };

                var allowedExtensions = new List<string> { ".pdf", ".xls", ".xlsx" };
                var fileExtension = Path.GetExtension(attachment.FileName).ToLower();

                if (!allowedContentTypes.Contains(attachment.ContentType) || !allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest("Invalid file type. Only PDF and Excel files are allowed.");
                }

                var filePath = await _fileStorageService.UploadDistributorDocumentsAndAttachments(attachment);
                distributorAttachmentsData.Attachment = filePath;
                await _distributionCenterService.AddAttachments(_mapper.Map<ApiContracts.DistributionCenter.DistributorMasterDocuments>(distributorAttachmentsData));
                return Ok(new { message = $"Attachments Uploaded Successfully : {filePath} " });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Insert the Data {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> CreateCertificates([FromForm] DistributorMasterCerficationCreation distributorAttachmentsData, IFormFile certifcate)
        {
            try
            {

                var filePath = await _fileStorageService.UploadDistributorDocumentsAndAttachments(certifcate);
                distributorAttachmentsData.Attachment = filePath;
                await _distributionCenterService.CreateDistributorCertifications(_mapper.Map<ApiContracts.DistributionCenter.DistributorMasterCertfications>(distributorAttachmentsData));
                return Ok(new { message = $"Attachments Uploaded Successfully : {filePath} " });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Upload Certificates {e.Message} " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> UploadAttachments([FromForm] List<DistributorAttachmentsCreation> distributorAttachments, List<IFormFile> attachments)
        {
            try
            {
                for (int i = 0; i < attachments.Count; i++)
                {
                    var filepath = await _fileStorageService.UploadDistributorDocumentsAndAttachments(attachments[i]);

                    var data = new DistributorAttachmentsCreation
                    {
                        Name = distributorAttachments[i].Name,
                        Type = distributorAttachments[i].Type,
                        Attachment = filepath,
                        Remarks = distributorAttachments[i].Remarks
                    };
                    await _distributionCenterService.AddAttachments(_mapper.Map<ApiContracts.DistributionCenter.DistributorMasterDocuments>(data));
                }
                return Ok(new { message = "Attachments Saved Successfully" });
            }

            catch (Exception ex)
            {
                return BadRequest(new { message = "Failed to upload Attachments" });
            }

        }

        [HttpGet("[action]/{filepath}")]
        public async Task<FileResult> DownloadFile(string filepath)
        {

            var extension = filepath.Split('.').Last();

            //Build the File Path.
            Stream streamData = await _fileStorageService.DownloadDistributorAttachment(filepath);

            //Read the File data into Byte Array.
            byte[] bytes = ReadFully(streamData);

            string downloadFileName;

            string fileName = filepath.Split('/').Last();

            downloadFileName = $"{fileName.Split('.')[0]}-{DateTime.Now}.{extension}";

            //Send the File to Download.
            return File(bytes, "application/octet-stream", downloadFileName);

        }

        private byte[] ReadFully(Stream input)
        {
            byte[] buffer = new byte[16 * 1024];
            using (MemoryStream ms = new MemoryStream())
            {
                int read;
                while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
                {
                    ms.Write(buffer, 0, read);
                }
                return ms.ToArray();
            }
        }


        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllRegion()
        {
            try
            {
                var allRegion = await _distributionCenterService.GetAllRegion();
                return Ok(allRegion);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Regions {e.Message} " });
            }
        }

        [HttpGet("[action]/{dmId}")]
        public async Task<IActionResult> GetRegionByDmId(string dmId)
        {
            try
            {
                var dmRegions = await _distributionCenterService.GetRegionsByDmId(dmId);
                return Ok(dmRegions);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Region {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetRegionForDm()
        {
            try
            {
                var dmId = await _distributorMasterUsersRepository.GetDistributorByUserID(userId);
                var dmRegions = await _distributionCenterService.GetRegionsByDmId(dmId.DistributorMasterId);
                return Ok(dmRegions);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Regions : {e.Message} " });
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetRegionById(string Id)
        {
            try
            {
                var regionData = await _distributionCenterService.GetBranchById(Id);
                return Ok(regionData);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch the Region {e.Message} " });
            }
        }

        [HttpPut("[action]")]
        public async Task<IActionResult> EditDistributorBasicDetails(string Id, [FromBody] EditDistributorMasterDetails editDistributorDetails)
        {
            try
            {
                await _distributionCenterService.UpdateDistributorMasterDetails(Id, editDistributorDetails);
                return Ok(new { message = "Distributor Basic Details Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Distributor {e.Message} " });
            }
        }

        [HttpPut("[action]")]
        public async Task<IActionResult> EditDistributorRM(string Id, [FromBody] EditDistributorRm editDistributorRM)
        {

            try
            {
                await _distributionCenterService.UpdateRm(Id, editDistributorRM);
                return Ok(new { message = " RM Details Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the RM : {e.Message} " });
            }

        }

        [HttpPut("[action]/{Id}")]
        public async Task<IActionResult> EditEmpanelmentById(string Id, [FromBody] EditEmpanelmentDetails editEmpanelmentDetails)
        {

            try
            {
                await _distributionCenterService.UpdateEmpanelmentDetails(Id, editEmpanelmentDetails);
                return Ok(new { message = "Empanelments Edited Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update : {e.Message} " });
            }

        }

        [HttpPut("[action]/{Id}")]
        public async Task<IActionResult> EditFeeSharingConfiguration(string Id, [FromBody] EditFeeSharingConfiguration editFeeSharingConfiguration)
        {

            try
            {
                await _distributionCenterService.UpdateFeeSharingConfiguration(Id, editFeeSharingConfiguration);
                return Ok(new { message = "Fee Sharing Configuration Edited Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update : {e.Message} " });
            }

        }

        [HttpPut("[action]/{Id}")]
        public async Task<IActionResult> EditPayoutDetails(string Id, [FromBody] EditPayoutDetails editPayoutDetails)
        {

            try
            {
                await _distributionCenterService.UpdatePayoutDetils(Id, editPayoutDetails);
                return Ok(new { message = "Payout Details Updated Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Payout Details : {e.Message} " });
            }

        }

        [HttpPut("[action]/{Id}")]
        public async Task<IActionResult> EditDistributorStatutory(string Id, [FromBody] EditDistributorStatutory editDistributorStatutory)
        {

            try
            {
                await _distributionCenterService.UpdateStatutory(Id, editDistributorStatutory);
                return Ok(new { message = "Statutory Details Updated Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update : {e.Message} " });
            }

        }

        [HttpPut("[action]")]
        public async Task<IActionResult> EditBranch(string Id, EditBranch editBranch)
        {
            try
            {
                await _distributionCenterService.UpdateBranch(Id, editBranch);
                return Ok(new { message = "Branch Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Branch {e.Message} " });
            }

        }

        [HttpPut("[action]")]
        public async Task<IActionResult> EditRegion(string Id, EditRegion editRegion)
        {
            try
            {
                await _distributionCenterService.UpdateRegion(Id, editRegion);
                return Ok(new { message = "Region Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Region {e.Message} " });
            }
        }

        [HttpPut("[action]")]
        public async Task<IActionResult> EditAMCBranch(string Id, EditAMCBranch editBranch)
        {
            try
            {
                await _distributionCenterService.UpdateAMCBranch(Id, editBranch);
                return Ok(new { message = "Branch Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Branch {e.Message} " });
            }

        }

        [HttpPut("[action]")]
        public async Task<IActionResult> EditAMCRegion(string Id, EditAMCRegion editRegion)
        {
            try
            {
                await _distributionCenterService.UpdateAMCRegion(Id, editRegion);
                return Ok(new { message = "Region Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Region {e.Message} " });
            }
        }

        [HttpPut("[action]")]
        public async Task<IActionResult> UpdateDistributorStatus(string Id, string status)
        {
            try
            {
                await _distributionCenterService.UpdateDistributorStatus(Id, status);
                return Ok(new { message = "Distributor Status Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update  {e.Message} " });
            }
        }

        [HttpPut("[action]")]
        public async Task<IActionResult> UpdateDistributorRmStatus(string Id, string status)
        {
            try
            {
                await _distributionCenterService.UpdateDistributorRMStatus(Id, status);
                return Ok(new { message = "Distributor RM Status Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update  {e.Message} " });
            }
        }

        [HttpPut("[action]")]
        public async Task<IActionResult> UpdateAttachments([FromForm] EditDistributorMasterDocuments editDistributorDocuments, IFormFile attachment)
        {
            try
            {
                if (attachment != null && attachment.Length > 0)
                {
                    var filePath = await _fileStorageService.UploadDistributorDocumentsAndAttachments(attachment);
                    editDistributorDocuments.Attachment = filePath;
                }
                await _distributionCenterService.UpdateDocuments(editDistributorDocuments.Id, editDistributorDocuments);
                return Ok(new { message = "Distributor Attachment Details Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Distributor Attachments {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetPortfolioDistributorSharingConfiguration(string portfolioId, string distributorMasterSharingConfigurationId)
        {
            try
            {
                var sharingConfig = await _distributionCenterService.GetPortfolioDistributorSharingConfiguration(portfolioId, distributorMasterSharingConfigurationId);
                return Ok(sharingConfig);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Fetch: {e.Message} " });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetDistributorMasterSharingConfiguration(string distributorMasterId, string strategyId)
        {
            try
            {
                var sharingConfig = await _distributionCenterService.GetDistributorMasterSharingConfiguration(distributorMasterId, strategyId);
                return Ok(sharingConfig);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch Sharing Config {e.Message} ", sharingConfigExist = false });
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetStrategiesWithNoFeesAssignedByDmId(string distributorMasterId)
        {
            try
            {
                var sharingConfig = await _distributionCenterService.GetStrategiesWithNoFeesAssignedByDmId(distributorMasterId);
                return Ok(sharingConfig);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Get Sharing Config {e.Message} ", sharingConfigExist = false });
            }
        }

        [HttpDelete("[action]/{Id}")]
        public async Task<IActionResult> DeleteAttachment(string Id)
        {
            try
            {
                await _distributionCenterService.DeleteAttachmentById(Id);
                return Ok(new { message = "Attachement Deleted Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Delete Attachment {e.Message} " });
            }
        }

    }

}