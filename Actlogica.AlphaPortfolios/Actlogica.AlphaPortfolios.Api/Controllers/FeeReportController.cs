using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.Fees;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Fees;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [Authorize]
    public class FeeReportController : ControllerBase
    {

        readonly IFeeTemplateService _feeTemplateService;
        readonly IStrategyModelFeeTemplateService _strategyModelFeeTemplateService;
        readonly IPortfolioFeeTemplateService _portfolioFeeTemplateService;
        private readonly IPortfolioFeeService _portfolioFeeService;
        private readonly IPortfolioFeeRepository _portfolioFeeRepository;
        private readonly IPortfolioFeeTriggeredLogsRepository _portfolioFeeTriggeredLogsRepository;

        public FeeReportController(IFeeTemplateService feeTemplateService, IStrategyModelFeeTemplateService strategyModelFeeTemplateService, IPortfolioFeeTemplateService portfolioFeeTemplateService, IPortfolioFeeService portfolioFeeService, IPortfolioFeeRepository portfolioFeeRepository, IPortfolioFeeTriggeredLogsRepository portfolioFeeTriggeredLogsRepository)
        {
            _feeTemplateService = feeTemplateService;
            _strategyModelFeeTemplateService = strategyModelFeeTemplateService;
            _portfolioFeeTemplateService = portfolioFeeTemplateService;
            _portfolioFeeService = portfolioFeeService;
            _portfolioFeeRepository = portfolioFeeRepository;
            _portfolioFeeTriggeredLogsRepository = portfolioFeeTriggeredLogsRepository;
        }


        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllFeeTemplate()
        {
            return Ok(await _feeTemplateService.GetAllFeeTemplate());
        }


        [HttpPost("[action]")]
        public async Task<IActionResult> AddFeeTemplate(FeeTemplate feeTemplate)
        {
            await _feeTemplateService.AddNewFeeTemplate(feeTemplate);
            return Ok();
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddStrategyModelFeeTemplate(StrategyModelFeeTemplate feeTemplate)
        {
            await _strategyModelFeeTemplateService.AddNewStrategyModelFeeTemplate(feeTemplate);
            return Ok();
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> GetFeeTemplateByModelId(string modelId)
        {
            await _strategyModelFeeTemplateService.GetFeeTemplateByModelId(modelId);
            return Ok();
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddPortfoliolFeeTemplate(PortfolioFeeTemplate feeTemplate)
        {
            await _portfolioFeeTemplateService.AddPortfolioFeeTemplate(feeTemplate);
            return Ok();
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> ComputeFee()
        {

            var portfolioFee = new PortfolioFeeTemplate
            {
                AppliedFromDate = DateTime.Now,
                AppliedToDate = DateTime.Now,
                FixedFeeFrequency = FixedFeeFrequency.Quarterly.ToString(),
                FixedFeePercentage = 1.5,
                PerformanceFeeFrequency = PerformanceFeeFrequency.Yearly.ToString(),
                PerformanceFeeHurdleRate = 10,
                PerformanceFeeSharingPercentage = 15,
                Type = "hybrid",
                PortfolioId = "fe286101fa8041bab8f1337ade259d87"

            };

            var asAtDate = new DateTime(2023, 12, 31);
            await _portfolioFeeService.CalculateFeesForAPortfolio(portfolioFee, asAtDate, "fe286101fa8041bab8f1337ade259d87", "", User.Identity.Name);
            return Ok();
        }


        [HttpGet("[action]")]
        public async Task<IActionResult> CalculateFeesForSingleClient([FromQuery] string clientId)
        {
            try
            {
                var userId = User.Identities.FirstOrDefault();
                var userName = userId.Name;
                var asAtDate = DateTime.Today;
                await _portfolioFeeService.CalculateFeesForSingleClient(userName, clientId, asAtDate);

                return Ok();
            }
            catch (Exception e)
            {

                return BadRequest("Failed to Compute");
            }
        }


        [HttpPost("[action]")]
        public async Task<IActionResult> CalculateFeesForClients(CalculateFees calculateFees)
        {
            try
            {
                var userId = User.Identities.FirstOrDefault();
                var userName = userId.Name;
                var tenantClaim = User.Claims.First(c => c.Type == "tenant");
                var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;

                await _portfolioFeeService.CreateFeeTriggerRequest(userName, calculateFees.PortfolioId, calculateFees.AsAtDate, tenantValue, calculateFees.Purpose, calculateFees.Remarks);

                return Ok();
            }
            catch (Exception e)
            {

                return BadRequest("Failed to Compute");
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetPortfolioFeeTriggeredLogs()
        {
            try
            {
                var logs = await _portfolioFeeService.GetPortfolioFeeTriggeredLogs();

                return Ok(logs);
            }
            catch (Exception e)
            {

                return BadRequest("Failed to Fetch");
            }
        }
        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetPortfolioFeeTriggeredLogById(string Id)
        {
            try
            {
                var log = await _portfolioFeeTriggeredLogsRepository.Get(Id);

                return Ok(log);
            }
            catch (Exception e)
            {

                return BadRequest("Failed to Fetch");
            }
        }

        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetFeeReportDetailsByTriggerLogId(string Id)
        {
            try
            {
                var logs = await _portfolioFeeService.GetFeeReportDetailsByTriggerLogId(Id) ?? throw new Exception($"Invalid Id : {Id}");

                return Ok(logs);
            }
            catch (Exception e)
            {
                return BadRequest($"Failed to Fetch : {e.Message}");
            }
        }

        [HttpPut("[action]")]
        public async Task<IActionResult> UpdateFeeStatus(List<string> portfolioFeeIds, string status)
        {
            try
            {
                await _portfolioFeeService.UpdatePortfolioFeeStatus(portfolioFeeIds, status);
                return Ok(new { Message = "Status Updated Successfully" });
            }
            catch (Exception e)
            {
                return BadRequest($"Failed to Update the Status : {e.Message}");
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetPortfoliosByStatus(string id, string status)
        {
            try
            {
                var logs = await _portfolioFeeService.GetPortfoliosByStatus(id, status);

                return Ok(logs);
            }
            catch (Exception e)
            {
                return BadRequest($"Failed to Fetch : {e.Message}");
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetPortfolioFeeDetailsById(string id)
        {
            try
            {
                var feeReports = await _portfolioFeeRepository.GetPortfolioFeeDetailsById(id);

                return Ok(feeReports);
            }
            catch (Exception e)
            {
                return BadRequest($"Failed to Fetch : {e.Message}");
            }
        }

    }

}