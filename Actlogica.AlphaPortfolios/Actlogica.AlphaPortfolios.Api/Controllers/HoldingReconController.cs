﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System;
using System.Linq;
using Actlogica.AlphaPortfolios.ServiceIntegration.HoldingReconciliation;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class HoldingController : AlphaBaseController
    {
        private readonly IHoldingReconService _holdingReconciliationService;
        private readonly IMapper _mapper;

        public HoldingController(
                 IMapper mapper,
                 IHoldingReconService holdingReconciliationService,
                 IHttpContextAccessor contextAccessor
                ) : base(contextAccessor)
        {
            _holdingReconciliationService = holdingReconciliationService;
            _mapper = mapper;

        }


        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllHoldingReconRequests()
        {

            try
            {
                return Ok(new { status = true, data = await _holdingReconciliationService.GetAllHoldingReconRequests() });
            }
            catch (Exception ex)
            {

                return BadRequest(new { status = false, message = ex.Message });
            }

        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetHoldingReconRequest(string id)
        {

            try
            {
                return Ok(new { status = true, data = await _holdingReconciliationService.GetHoldingReconRequestById(id) });
            }
            catch (Exception ex)
            {

                return BadRequest(new { status = false, message = ex.Message });
            }

        }

        [HttpPost("[action]/{custodianId}")]
        public async Task<IActionResult> UploadReconFile(IFormFile custodyFile, IFormFile fundAccountantFile, string custodianId)
        {

            try
            {
                if (custodyFile == null || custodyFile.Length == 0)
                {
                    return BadRequest(new { status = false, message = " Custody File is Mandatory" });
                }

                await _holdingReconciliationService.UploadReconFile(custodyFile, fundAccountantFile, custodianId, userName);
                return Ok();
            }
            catch (Exception ex)
            {
                return StatusCode(400, new { status = false, message = ex.Message });
            }


        }


        [HttpGet("[action]/{reconRequestId}")]
        public async Task<IActionResult> DownloadFile(string reconRequestId, [FromQuery] string name)
        {

            try
            {
                (Stream streamData, string extension) = await _holdingReconciliationService.DownloadFile(reconRequestId, name);

                //Read the File data into Byte Array.
                byte[] bytes = ReadFully(streamData);

                string fileName = $"{reconRequestId}-{name}.{extension}";

                //Send the File to Download.
                return File(bytes, "application/octet-stream", fileName);
            }
            catch (Exception ex)
            {

                return BadRequest(ex.Message);
            }

        }

        private byte[] ReadFully(Stream input)
        {
            byte[] buffer = new byte[16 * 1024];
            using (MemoryStream ms = new MemoryStream())
            {
                int read;
                while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
                {
                    ms.Write(buffer, 0, read);
                }
                return ms.ToArray();
            }
        }


        // [HttpPost("[action]")]
        // public async Task<IActionResult> UploadFiles(List<IFormFile> files)
        // {
        //     try
        //     {
        //         if (files == null || !files.Any()) return BadRequest($"Invalid parameter received {nameof(files)}");

        //         var reconData = await _holdingReconciliationService.ProcessHoldingRecon(files);

        //         return Ok(reconData);
        //     }
        //     catch (InvalidDataException ex)
        //     {
        //         return Conflict(ex.Message);
        //     }
        //     catch (Exception ex)
        //     {
        //         return BadRequest(ex.Message);
        //     }
        // }
        // [HttpGet("[action]/{holdingReconProcessId}")]
        // public async Task<IActionResult> GetSummary(string holdingReconProcessId)
        // {
        //     try
        //     {
        //         var summaryData = await _holdingReconciliationService.GetSummary(holdingReconProcessId);

        //         return Ok(summaryData);
        //     }
        //     catch (InvalidDataException ex)
        //     {
        //         return Conflict(ex.Message);
        //     }
        //     catch (Exception ex)
        //     {
        //         return BadRequest(ex.Message);
        //     }
        // }
        // [HttpGet("[action]/{holdingReconProcessId}")]
        // public async Task<IActionResult> GetReconResult(string holdingReconProcessId)
        // {
        //     try
        //     {
        //         var reconData = await _holdingReconciliationService.GetReconResult(holdingReconProcessId);

        //         return Ok(reconData);
        //     }
        //     catch (InvalidDataException ex)
        //     {
        //         return Conflict(ex.Message);
        //     }
        //     catch (Exception ex)
        //     {
        //         return BadRequest(ex.Message);
        //     }
        // }
        // [HttpGet("[action]")]
        // public async Task<IActionResult> GetProcessedHoldingRecon()
        // {
        //     try
        //     {
        //         var processedData = await _holdingReconciliationService.GetProcessedHoldingRecon();

        //         return Ok(processedData);
        //     }
        //     catch (InvalidDataException ex)
        //     {
        //         return Conflict(ex.Message);
        //     }
        //     catch (Exception ex)
        //     {
        //         return BadRequest(ex.Message);
        //     }
        // }
    }
}
