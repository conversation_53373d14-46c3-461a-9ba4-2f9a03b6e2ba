﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Actlogica.AlphaPortfolios.ServiceIntegration.CorporateActions;
using Microsoft.Extensions.Logging;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using System;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System.Collections.Generic;
using System.Linq;
using Actlogica.AlphaPortfolios.ApiContracts.CorporateActions;
using Microsoft.AspNetCore.Authorization;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	[Authorize]
	public class CorpActionsController : ControllerBase
	{
		private readonly ILogger<CorpActionsController> _logger;
		private readonly IEquityCorporateActionService _equityCorpActionSvc;
		private readonly IPortfolioService _portfolioService;

		public CorpActionsController(IPortfolioService portfolioService,
			ILogger<CorpActionsController> logger,
			IEquityCorporateActionService equityCorpActionSvc)
		{
			this._logger = logger;
			this._equityCorpActionSvc = equityCorpActionSvc;
			_portfolioService = portfolioService;
		}

		[HttpGet("{symbol}")]
		public async Task<IActionResult> Get(string symbol)
		{
			return Ok(await _equityCorpActionSvc.GetActionsForSymbol(symbol));
		} 

		[HttpGet("[action]/{date}")]
		public async Task<IActionResult> GetForDate(string date)
		{
			DateTime asAtDate;
			if (DateTime.TryParse(date, out asAtDate))
				return Ok(await _equityCorpActionSvc.GetActionsOnDate(asAtDate));
			else
				return BadRequest($"Date: {date} not a valid Date parameter");
		}
		
		[HttpGet("[action]/{rowKey}")]
		public async Task<IActionResult> GetByRowKey(string rowKey)
		{ 
			return Ok(await _equityCorpActionSvc.GetActionByRowKey(rowKey));
		}
		
		[HttpPut("[action]")]
		public async Task<IActionResult> UpdateCorpAction([FromBody] EquityCorporateAction existingCorpAction)
		{
			await _equityCorpActionSvc.UpdateCorporateAction(existingCorpAction);
			return Ok();
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> CalculateCorporateAction(string symbol, string exchange)
		{
			var investmentsWithSymbol = await _portfolioService.GetInvestment(symbol, exchange);

			if (!investmentsWithSymbol.Any())
				return Ok();

			var caToday = await _equityCorpActionSvc.GetActionsForSymbolOnDate(symbol, DateTime.Today.ToString("dd-MMM-yyyy"));
			var caPayload = await _equityCorpActionSvc.BuildCaPayloadFromAction(caToday);

			var corpActionTxns = new List<Transaction>();

			foreach(var investment in investmentsWithSymbol)
			{
				var dividendPaidTillDate = await _portfolioService.CalculateDividendPaidTillDate(investment.PortfolioId);
				var caTxn = await _equityCorpActionSvc.CalculateActionResult(investment, dividendPaidTillDate, caPayload);
				corpActionTxns.Add(caTxn);
			}

			return Ok(corpActionTxns);
		}
	}
}
