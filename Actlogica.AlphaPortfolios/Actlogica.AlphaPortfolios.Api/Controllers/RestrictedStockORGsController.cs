﻿using Actlogica.AlphaPortfolios.ApiContracts.RestrictedStockORGs;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Actlogica.AlphaPortfolios.ServiceIntegration.RestrictedStockORGs;
using Actlogica.AlphaPortfolios.Api.Types;
using System;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [Authorize(Policy = "CommonRolePolicy")]
    public class RestrictedStockORGsController : ControllerBase
    {
        private readonly IRestrictedStockORGService _restrictedStockORGSvc;
        public RestrictedStockORGsController(IRestrictedStockORGService restrictedStockORGService)
        {
            _restrictedStockORGSvc = restrictedStockORGService;
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAll()
        {
            return Ok(await _restrictedStockORGSvc.GetRestrictedStockORGs());
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            if (string.IsNullOrEmpty(id))
                return BadRequest("Id parameter is required.");

            return Ok(await _restrictedStockORGSvc.GetRestrictedStockORGById(id));
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,PrincipleOfficer")]
        [HttpPost("[action]")]
        public async Task<IActionResult> New(RestrictedStockORG newRestrictedStockORG)
        {

            try
            {
                newRestrictedStockORG.Status = RestrictedStockStatus.Active.ToString();
                var add = await _restrictedStockORGSvc.AddRestrictedStockORG(newRestrictedStockORG);
                return Ok(add);
            }
            catch (Exception e)
            {
                return BadRequest(new { status = false, message = e.Message });
            }
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,PrincipleOfficer")]
        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> Update([FromBody] UpdateRestrictedStocksForOrgs existingRestrictedStock, string id)
        {

            try
            {
                await _restrictedStockORGSvc.Edit(id, existingRestrictedStock.AlternativeSecurityIdentifier, existingRestrictedStock.IsinAlternativeSecurity, existingRestrictedStock.ExchangeAlternativeSecurity, existingRestrictedStock.Rationale);
                return Ok(new { status = true, message = "Updated Successfully" });
            }
            catch (Exception e)
            {
                return BadRequest(new { status = false, message = "Failed to Update" });
            }
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,PrincipleOfficer")]
        [HttpDelete("[action]/{restrictedStockORGId}")]
        public async Task<IActionResult> Delete(string restrictedStockORGId)
        {
            await _restrictedStockORGSvc.DeleteRestrictedStockORG(restrictedStockORGId);
            return Ok(new { status = true, message = "Deleted Successfully" });
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,PrincipleOfficer")]
        [HttpPatch("[action]/{restrictedStockOrgId}")]
        public async Task<IActionResult> ChangeStatus(string restrictedStockOrgId, [FromBody] bool status)
        {
            try
            {
                await _restrictedStockORGSvc.ChangeStatus(restrictedStockOrgId, status);
                return Ok();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
    }
}
