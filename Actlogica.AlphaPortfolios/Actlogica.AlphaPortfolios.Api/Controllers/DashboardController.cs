﻿using Actlogica.AlphaPortfolios.ServiceIntegration.Dashboards;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("api/[controller]")]
	[ApiController]
	[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,PrincipleOfficer,CIO,DealerDeskTrader,OperationManager,Operations")]
	public class DashboardController : ControllerBase
	{
		private readonly IDashboardService _dashboardSvc;
		public DashboardController(IDashboardService dashboardService)
		{
			_dashboardSvc = dashboardService;
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetStrategyAnalytics()
		{
			return Ok(await _dashboardSvc.GetStrategiesMetrics());
		}

		[HttpGet("[action]/{take}")]
		public async Task<IActionResult> GetClientAnalytics(int take)
		{
			return Ok(await _dashboardSvc.GetClientsMetrics(take));
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetFundManagerAnalytics()
		{
			return Ok(await _dashboardSvc.GetFundManagerMetrics());
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetIndustryAnalytics()
		{
			return Ok(await _dashboardSvc.GetIndustryMetrics());
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetMarketCapAnalytics()
		{
			return Ok(await _dashboardSvc.GetMarketCapMetrics());
		}

	}
}
