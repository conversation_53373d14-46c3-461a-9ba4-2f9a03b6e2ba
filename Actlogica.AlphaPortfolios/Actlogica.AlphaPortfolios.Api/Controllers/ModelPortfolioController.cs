﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.DirectEquity;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize(Policy = "TenantRolePolicy")]
	public class ModelPortfolioController : ControllerBase
	{
		private readonly IModelPortfolioService _modelPtfSvc;

		public ModelPortfolioController(IModelPortfolioService modelPortfolioService)
		{
			_modelPtfSvc = modelPortfolioService;
		}

		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> GetByModelId(string modelId)
		{
			var portfolioSummary = await _modelPtfSvc.GetByModelId(modelId);
			return Ok(portfolioSummary);
		}

		//[HttpPost("[action]/{modelId}")]
		//public async Task<IActionResult> Create(string modelId, [FromBody] ModelPortfolio portfolio)
		//{
		//	var portfolioSummary = await _modelPtfSvc.Create(modelId, portfolio);
		//	return Ok(portfolioSummary);
		//}

		//[HttpPost("[action]/{modelPortfolioId}")]
		//public async Task<IActionResult> AddStockTransactions(string modelPortfolioId,
		//	[FromBody] List<InvestmentTransaction> transactionsInPortfolio)
		//{
		//	transactionsInPortfolio.ForEach(txn => { txn.SecurityType = SecurityType.Stocks.ToString(); txn.SecuritySubType = SecuritySubType.Listed.ToString(); });
		//	var errors = await _modelPtfSvc.AddInvestmentTransactionsToModelPortfolio(modelPortfolioId, transactionsInPortfolio);
		//	return Ok(errors);
		//}

		//[HttpPost("[action]/{modelPortfolioId}")]
		//public async Task<IActionResult> AddTransactions(string modelPortfolioId,
		//	[FromBody] List<InvestmentTransaction> transactionsInPortfolio)
		//{
		//	var errors = await _modelPtfSvc.AddInvestmentTransactionsToModelPortfolio(modelPortfolioId, transactionsInPortfolio);
		//	return Ok(errors);
		//}

		[HttpGet("[action]/{modelPortfolioId}")]
		public async Task<IActionResult> GetCapitalRegister(string modelPortfolioId, int skip, int take)
		{
			var capitalRegisterTxns = await _modelPtfSvc.GetCapRegTransactionsInModelPortfolio(modelPortfolioId, skip, take);
			return Ok(capitalRegisterTxns);
		}

		[HttpGet("[action]/{modelPortfolioId}")]
		public async Task<IActionResult> GetCashLedger(string modelPortfolioId, int skip, int take)
		{
			var cashLedgers = await _modelPtfSvc.GetCashLedgerTransactionsInModelPortfolio(modelPortfolioId, skip, take);
			return Ok(cashLedgers);
		}

		[HttpGet("[action]/{modelPortfolioId}")]
		public async Task<IActionResult> GetTransactions(string modelPortfolioId, int skip, int take)
		{
			var transactions = await _modelPtfSvc.GetTransactionsInModelPortfolio(modelPortfolioId, skip, take);
			return Ok(transactions);
		}

		[HttpGet("[action]/{modelPortfolioId}")]
		public async Task<IActionResult> GetMarketValue(string modelPortfolioId, DateTime asOnDate)
		{
			return Ok(await _modelPtfSvc.GetMarketValue(modelPortfolioId, asOnDate));
		}
	}
}
