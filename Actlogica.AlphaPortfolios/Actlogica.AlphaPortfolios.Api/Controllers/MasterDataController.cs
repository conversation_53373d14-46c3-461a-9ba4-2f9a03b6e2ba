﻿using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Masters;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
  [Route("[controller]")]
  [ApiController]
  [Authorize]
  public class MasterDataController : AlphaBaseController
  {
    private readonly IMasterDataService _masterDataService;
    private readonly IMapper _mapper;
    private readonly IMasterLookupRepository _masterLookupRepository;
    private readonly IMasterLookupAMCRepository _masterLookupAMCRepository;

    private readonly IClientService _clientService;
    
    public MasterDataController(IMasterDataService masterDataService, IMapper mapper, IClientService clientService, IHttpContextAccessor contextAccessor, 
    IMasterLookupAMCRepository masterLookupAMCRepository,IMasterLookupRepository masterLookupRepository
      ) : base(contextAccessor)
    {
      _masterDataService = masterDataService;
      _masterLookupAMCRepository = masterLookupAMCRepository;
      _masterLookupRepository = masterLookupRepository;
      _mapper = mapper;
      _clientService = clientService;
    }

    [Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
    [HttpGet("[action]")]
    public async Task<IActionResult> GetAllBrokers()
    {
      var registeredBrokers = await _masterDataService.GetBrokers();
      return Ok(registeredBrokers);
    }

    [Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
    [HttpGet("[action]")]
    public async Task<IActionResult> GetAllCustodians()
    {
      var registeredCustodians = await _masterDataService.GetCustodians();
      return Ok(registeredCustodians);
    }

    [Authorize(Policy = "CommonRolePolicy")]
    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetBrokerById(string id)
    {
      var selectedBroker = await _masterDataService.GetBrokerById(id);
      return Ok(selectedBroker);
    }

    [Authorize(Policy = "CommonRolePolicy")]
    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetCustodianById(string id)
    {
      var selectedCustodian = await _masterDataService.GetCustodianById(id);
      return Ok(selectedCustodian);
    }

    [Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
    [HttpGet("[action]/{clientId}")]
    public async Task<IActionResult> GetCustodiansByClientId(string clientId)
    {
      if (role.Contains("AlphaAccountsSubscriber"))
      {
        var client = await _clientService.GetClientByUserId(base.userId);
        if (client == null)
        {
          return BadRequest(new { message = "Invalid", status = false });
        }
        clientId = client.Id;

      }
      var selectedCustodian = await _masterDataService.GetCustodiansByClientId(clientId);
      return Ok(selectedCustodian);
    }

    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager")]
    [HttpPost("[action]")]
    public async Task<IActionResult> RegisterNewBroker([FromBody] BrokerCreation newBroker)
    {
      var registeredBroker = await _masterDataService.RegisterBroker(_mapper.Map<Broker>(newBroker));
      return Ok(registeredBroker);
    }

    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager")]
    [HttpPost("[action]")]
    public async Task<IActionResult> RegisterNewCustodian([FromBody] CustodianCreation newCustodian)
    {
      try
      {
        var registeredCustodian = await _masterDataService.RegisterCustodian(_mapper.Map<Custodian>(newCustodian));
        return Ok(registeredCustodian);
      }
      catch (System.Exception ex)
      {
        return BadRequest(ex.Message);
      }
    }

    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager")]
    [HttpPut("[action]/{id}")]
    public async Task<IActionResult> UpdateBroker(string id, [FromBody] EditBroker broker)
    {
      try
      {

        await _masterDataService.UpdateBroker(id, broker);
        return Ok(new { message = "Succesfully Updated Broker", status = true });
      }
      catch (Exception e)
      {

        return BadRequest(e.Message);
      }
    }

    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager")]
    [HttpPut("[action]/{id}")]
    public async Task<IActionResult> UpdateCustodian(string id, [FromBody] EditCustodian custodian)
    {
      try
      {
        await _masterDataService.UpdateCustodian(id, custodian);
        return Ok(new { message = "Succesfully Updated Custodian", status = true });
      }
      catch (Exception e)
      {

        return BadRequest(e.Message);
      }
    }

    [HttpGet("[action]/{type}")]
    public async Task<IActionResult> GetLookupByType(string type)
    {
      return Ok(await _masterDataService.GetLookupValuesByType(type));
    }

    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetLookupById(string id)
    {
      return Ok(await _masterLookupRepository.Get(id));
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SaveLookupValues([FromBody] IEnumerable<MasterLookupValues> lookupValues)
    {
      await _masterDataService.SaveLookupValues(lookupValues);
      return Ok();
    }

    [HttpPut("[action]")]
    public async Task<IActionResult> EditLookupValues(string id, [FromBody] MasterLookupValues lookupValues)
    {

      await _masterDataService.EditLookupValues(id ,lookupValues);
      return Ok();
    }
   

    [HttpGet("[action]/{type}")]
    public async Task<IActionResult> GetAMCLookupByType(string type)
    {
      return Ok(await _masterDataService.GetAMCLookupValuesByType(type));
    }

    [HttpPost("[action]")]
    public async Task<IActionResult> SaveAMCLookupValues([FromBody] IEnumerable<MasterLookupValuesAMC> lookupValues)
    {
      await _masterDataService.SaveAMCLookupValues(lookupValues);
      return Ok();
    }

     [HttpPut("[action]")]
    public async Task<IActionResult> EditAMCLookupValues(string id, [FromBody] MasterLookupValuesAMC lookupValues)
    {
      await _masterDataService.EditAMCLookupValues(id, lookupValues);
      return Ok();
    }

    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetAMCLookupById(string id)
    {
      return Ok(await _masterLookupAMCRepository.Get(id));
    }
  }

}
