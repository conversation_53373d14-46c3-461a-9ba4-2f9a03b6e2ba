﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,Operations,OperationManager,PrincipleOfficer,ResearchAnalyst")]
	public class DeviationAnalysisController : ControllerBase
	{
		private readonly IPortfolioService _portfolioService;
		private readonly IModelPortfolioService _modelPortfolioService;
		private readonly IStrategyService _strategyService;
		private readonly IStrategyModelService _strategyModelSvc;
		private readonly IClientOrderManagementService _clientOrderMgmtSvc;

		public DeviationAnalysisController(IPortfolioService portfolioService, IModelPortfolioService modelPortfolioService
			, IStrategyService strategyService, IStrategyModelService strategyModelSvc, IClientOrderManagementService clientOrderMgmtSvc)
		{
			_portfolioService = portfolioService;
			_modelPortfolioService = modelPortfolioService;
			_strategyService = strategyService;
			_strategyModelSvc = strategyModelSvc;
			_clientOrderMgmtSvc = clientOrderMgmtSvc;
		}

		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> ClientsInModel(string modelId)
		{
			var clientsSummary = await _strategyModelSvc.GetClientsByModelId(modelId);
			return Ok(clientsSummary);
		}

		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> ActiveClientsInModel(string modelId)
		{
			var clientsSummary = await _strategyModelSvc.GetActiveClientsByModelId(modelId);
			return Ok(clientsSummary);
		}

		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> SecuritiesInModel(string modelId)
		{
			var modelSecurities = await _strategyModelSvc.GetSecuritiesByModelId(modelId);
			modelSecurities = modelSecurities.Where(models => models.Weight > 0);
			return Ok(modelSecurities);
		}

		[HttpPost("[action]/{modelId}")]
		public async Task<IActionResult> RunAllClientsInModel(string modelId, bool allSecurities, string isin)
		{
			try
			{
				var deviationReport = await _clientOrderMgmtSvc.DeviationAllClients(modelId, allSecurities, isin);

				return Ok(deviationReport);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpPost("[action]/{modelId}")]
		public async Task<IActionResult> RunSpecificClientsInModel([FromBody] IEnumerable<string> clientStrategyCodes,
			string modelId, bool allSecurities, string isin)
		{
			var deviationReport = await _clientOrderMgmtSvc.DeviationSpecificClients(modelId, clientStrategyCodes, allSecurities, isin);
			return Ok(deviationReport);
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> PrepareCustomisedOrdersFromDeviation([FromBody] IEnumerable<ModelDeviationReport> modelDeviationReports)
		{
			var ordersFromDeviation = await _clientOrderMgmtSvc.PrepareCustomisedOrdersForDeviations(modelDeviationReports);
			return Ok(ordersFromDeviation);
		}
	}
}
