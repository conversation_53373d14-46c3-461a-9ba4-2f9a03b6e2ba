﻿using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.Api.Utilities;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;
using Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Communications;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.TradeIdeaManagement;
using Actlogica.AlphaPortfolios.Utils.Dates;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using System.Text;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class OrderController : AlphaBaseController
	{
		private readonly IClientOrderManagementService _clientOrderSvc;
		private readonly IPoolOrderManagementService _poolOrderSvc;
		private readonly IFileStorageService _fileStorageService;
		private readonly IStrategyService _strategyService;
		private readonly ITradeOrderService _tradeOrderService;
		private readonly ILogger<OrderController> _logger;
		private readonly IBuyTradeIdeaService _buyTradeIdeaService;
		private readonly ISellTradeIdeaService _sellTradeIdeaService;
		private readonly IClientService _clientService;
		private readonly IBrokerRepository _brokerRepository;
		private readonly IOptions<ConnectionStrings> _options;
		private readonly IOptions<PerformanceEngineConfigs> _perfEngineConfig;

		private readonly IAlphaNotificationService _alphaNotificationService;

		public OrderController(
				IClientOrderManagementService clientOrderSvc
				, IPoolOrderManagementService poolOrderSvc
				, IFileStorageService fileStorageService
				, IStrategyService strategyService
				, ITradeOrderService tradeOrderService
				, IBuyTradeIdeaService buyTradeIdeaService
				, ISellTradeIdeaService sellTradeIdeaService
				, IBrokerRepository brokerRepository
				, ILogger<OrderController> logger,
				IOptions<ConnectionStrings> options, IOptions<PerformanceEngineConfigs> perfEngineConfig, IAlphaNotificationService alphaNotificationService, IHttpContextAccessor contextAccessor
				) : base(contextAccessor)
		{
			_clientOrderSvc = clientOrderSvc;
			_poolOrderSvc = poolOrderSvc;
			_fileStorageService = fileStorageService;
			_strategyService = strategyService;
			_tradeOrderService = tradeOrderService;
			_buyTradeIdeaService = buyTradeIdeaService;
			_logger = logger;
			_sellTradeIdeaService = sellTradeIdeaService;
			_options = options;
			_perfEngineConfig = perfEngineConfig;
			_clientOrderSvc = clientOrderSvc;

			_alphaNotificationService = alphaNotificationService;
			_brokerRepository = brokerRepository;
			_clientOrderSvc.SetCreatedBy(base.userName);
			_buyTradeIdeaService.SetCreatedBy(base.userName);
			_sellTradeIdeaService.SetCreatedBy(base.userName);
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,Operations,AlphaAccountsSubscriber")]
		[HttpGet("[action]")]
		public async Task<IActionResult> PrepareOrdersForNewPortfolio(string clientId, string modelId, string custodianId, double capital, string brokerTradingAccNumber)
		{
			try
			{
				if (role.Contains("AlphaAccountsSubscriber"))
				{
					var client = await _clientService.GetClientByUserId(base.userId);
					//Can Only Create Porfolio for their Clients
					if (clientId! == client.Id)
					{
						return BadRequest(new { message = $"Failed to Create Portfolios. Reason: Invalid Client Id", status = false });
					}
				}
				var computedOrders = await _clientOrderSvc.PrepareOrdersForNewPortfolio(clientId, modelId, custodianId, capital, brokerTradingAccNumber);
				return Ok(computedOrders);

			}
			catch (InvalidOperationException ex)
			{
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}


		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpGet("[action]")]
		public async Task<IActionResult> PrepareOrdersForAdditionalCapital(string portfolioId, string modelId, double capital)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.PrepareOrdersForAdditionalCapital(portfolioId, modelId, capital);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpGet("[action]")]
		public async Task<IActionResult> PrepareOrdersForCapitalWithdrawal(string portfolioId, string modelId, double withdrawalAmount)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.PrepareOrdersForCapitalWithdrawal(portfolioId, modelId, withdrawalAmount);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpGet("[action]")]
		public async Task<IActionResult> CompletePortfolioExit(string portfolioId, string modelId)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.PrepareOrdersForCompletePortfolioExit(portfolioId, modelId);
				await _clientOrderSvc.CreateClientOrders(computedOrders);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpGet("[action]/{buyTradeIdeaId}")]
		public async Task<IActionResult> PrepareOrdersForNewBuyIdea(string buyTradeIdeaId)
		{
			var buyTradeIdea = await _buyTradeIdeaService.GetBuyTradeIdeaById(buyTradeIdeaId);
			if (buyTradeIdea == null)
				return NotFound("Invalid request. Please contact the administrator.");

			var clientOrderEntries = await _buyTradeIdeaService.PrepareOrdersForBuyIdea(buyTradeIdea);
			return Ok(clientOrderEntries);
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpGet("[action]/{sellTradeIdeaId}")]
		public async Task<IActionResult> PrepareOrdersForNewSellIdea(string sellTradeIdeaId)
		{
			var sellTradeIdea = await _sellTradeIdeaService.GetSellTradeIdeaById(sellTradeIdeaId);
			if (sellTradeIdea == null)
				return NotFound("Invalid request. Please contact the administrator.");

			if (sellTradeIdea.IsSellAll)
				return Ok(await _sellTradeIdeaService.PrepareOrdersForSellAllIdea(sellTradeIdea));
			else
				return Ok(await _sellTradeIdeaService.PrepareOrdersForSellIdea(sellTradeIdea));
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpPost("[action]")]
		public async Task<IActionResult> PrepareOrdersForCustomTrade([FromBody] IEnumerable<CustomisedOrder> customisedOrders)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.PrepareOrdersForCustomTrade(customisedOrders);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpPost("[action]/{targetWeight}")]
		public async Task<IActionResult> CalculateBulkOrdersByTargetWeight([FromBody] IEnumerable<InvestmentSearchResult> customisedOrders, double targetWeight, string exchange)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.CalculateBulkOrdersByTargetWeight(customisedOrders, targetWeight, exchange);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpPost("[action]/{targetPortfolioWeight}")]
		public async Task<IActionResult> CalculateBulkOrdersByPortfolioWeight([FromBody] IEnumerable<InvestmentSearchResult> customisedOrders, double targetPortfolioWeight, string exchange)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.CalculateBulkOrdersByPortfolioWeight(customisedOrders, targetPortfolioWeight, exchange);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpPost("[action]/{cashWeight}")]
		public async Task<IActionResult> CalculateBulkOrdersByCashWeight([FromBody] IEnumerable<InvestmentSearchResult> customisedOrders, double cashWeight, string exchange)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.CalculateBulkOrdersByCashWeight(customisedOrders, cashWeight, exchange);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpPost("[action]/{cashToBuy}")]
		public async Task<IActionResult> CalculateBulkOrdersByCashValue([FromBody] IEnumerable<InvestmentSearchResult> customisedOrders, double cashToBuy, string exchange)
		{
			try
			{
				var computedOrders = await _clientOrderSvc.CalculateBulkOrdersByCashValue(customisedOrders, cashToBuy, exchange);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpPost("[action]")]
		public async Task<IActionResult> PrepareOrdersForBulkTrades([FromBody] IEnumerable<InvestmentSearchResult> bulkOrders)
		{
			try
			{
				var customisedOrders = await _clientOrderSvc.PrepareCustomOrdersForBulkTrades(bulkOrders);

				var computedOrders = await _clientOrderSvc.PrepareOrdersForCustomTrade(customisedOrders);

				await _clientOrderSvc.CreateClientOrders(computedOrders);
				return Ok(computedOrders);
			}
			catch (InvalidOperationException ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,FundManager,ResearchAnalyst,OperationManager,Operations")]
		[HttpPost("[action]")]
		public async Task<IActionResult> SaveDeviationOrders([FromBody] IEnumerable<CustomisedOrder> customisedOrders)
		{
			try
			{
				customisedOrders.ToList().ForEach(cord => cord.Remarks = "Deviation");
				var computedOrders = await _clientOrderSvc.PrepareOrdersForCustomTrade(customisedOrders);
				computedOrders.ToList().ForEach(coe => coe.OrderRationale = $"Deviation trade based on analysis report.");
				await _clientOrderSvc.CreateClientOrders(computedOrders);

				return Ok();
			}
			catch (InvalidOperationException ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				_logger.LogError(ex.Message, ex);
				return BadRequest("There was a problem with the request. Please try again or contact support.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> SaveClientOrdersInDraft([FromBody] IEnumerable<ClientOrderEntry> clientOrderEntries)
		{
			await _clientOrderSvc.CreateClientOrders(clientOrderEntries);
			return Ok();
		}

		[HttpGet("[action]/{orderStatus}")]
		public async Task<IActionResult> GetOrdersByStatus(string orderStatus)
		{
			var userRole = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
			var userId = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
			var clientOrders = await _clientOrderSvc.GetOrdersByStatus(orderStatus, userRole, userId);
			return Ok(clientOrders);
		}

		[HttpGet("[action]/{orderStatus}")]
		public async Task<IActionResult> GetOrdersSummaryByModelAndStatus(string orderStatus)
		{
			var userRole = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
			var userId = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
			var clientOrders = await _clientOrderSvc.GetOrdersByStrategyModel(orderStatus, userRole, userId);
			return Ok(clientOrders);
		}

		[HttpGet("[action]/{strategyModelId}/{orderStatus}")]
		public async Task<IActionResult> GetOrdersDetailsByModelIdAndStatus(string orderStatus, string strategyModelId)
		{
			var clientOrders = await _clientOrderSvc.GetOrdersByModelId(orderStatus, strategyModelId);
			return Ok(clientOrders);
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpPost("[action]")]
		public async Task<IActionResult> PreparePoolOrdersForSelectedClientOrders([FromBody] IEnumerable<string> clientOrderEntryIds)
		{
			if (clientOrderEntryIds == null || !clientOrderEntryIds.Any())
				return BadRequest("No orders found in request.");

			var draftClientOrderEntries = await _clientOrderSvc.GetOrdersByIdByStatus(clientOrderEntryIds.ToList(), $"{OrderStatus.Draft}");
			var clientOrderEntries = draftClientOrderEntries.Where(coe => clientOrderEntryIds.Contains(coe.Id));

			if (clientOrderEntries.Any(coe => coe.OrderStatus != OrderStatus.Draft))
				return BadRequest("Only orders in Draft status can be sent to exchange for execution. Please review your request.");

			var validatedClientOrderEntries = await _tradeOrderService.RevalidateDraftOrders(clientOrderEntries);
			// if (validatedClientOrderEntries.Any(coe => coe.ValidityStatus == "Invalid"))
			// 	return BadRequest(validatedClientOrderEntries.OrderByDescending(o => o.ValidityStatus));

			try
			{
				var createdOrders = new List<PoolOrderSummary>();
				createdOrders.AddRange(await _tradeOrderService.PrepareDraftPoolOrders(validatedClientOrderEntries.Where(coe => coe.ValidityStatus == null)));
				createdOrders.AddRange(await _tradeOrderService.PrepareDraftMfOrders(draftClientOrderEntries.Where(coe => coe.ValidityStatus == null)));
				return Ok(new { validOrder = createdOrders, invalidOrder = validatedClientOrderEntries.Where(coe => coe.ValidityStatus != null) });
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpPost("[action]")]
		public async Task<IActionResult> SendDraftOrdersToExchange([FromBody] IEnumerable<ClientOrderEntry> clientOrderEntries, [FromQuery] bool sendMailToBroker, [FromQuery] string mainBrokerId, [FromQuery] bool csvFormat)
		{

			if (string.IsNullOrEmpty(mainBrokerId))
			{
				return BadRequest("Main Broker Id is required.");
			}

			var requestedBroker = await _brokerRepository.Get(mainBrokerId);

			if (requestedBroker == null)
			{
				return BadRequest("No Brokers found for the requested Id.");
			}

			if (clientOrderEntries == null || !clientOrderEntries.Any())
				return BadRequest("No orders found in request.");

			if (clientOrderEntries.Any(coe => coe.OrderStatus != OrderStatus.Draft))
				return BadRequest("Only orders in Draft status can be sent to exchange for execution. Please review your request.");

			var validatedClientOrderEntries = await _tradeOrderService.RevalidateDraftOrders(clientOrderEntries);
			if (validatedClientOrderEntries.Any(coe => coe.ValidityStatus == "Invalid"))
				return BadRequest(validatedClientOrderEntries);

			//FIXME: This entire LOGIC should be changed 
			var exchange = validatedClientOrderEntries.FirstOrDefault().Exchange;

			try
			{
				var ordersInDraft = clientOrderEntries.Where(coe => coe.OrderStatus == OrderStatus.Draft).ToList();

				var createdTradeOrders = await _tradeOrderService.CreatePoolOrdersForBroker(ordersInDraft, User.Identity.Name);

				var orderAsCsv = "";

				if (exchange == "BSE")
				{
					//Check if all the orders are of BSE
					var isValid = validatedClientOrderEntries.All(coe => coe.Exchange.Equals("BSE", StringComparison.CurrentCultureIgnoreCase));
					if (!isValid)
					{
						throw new Exception("Not Every order has same exchange");
					}

					byte[] csvBytes = null;
					byte[] txtBytes = null;
					string fileName = $"Orders-{DateTime.Now}.csv";
					string txtFileName = $"{createdTradeOrders.FirstOrDefault().StrategyCode}-{createdTradeOrders.FirstOrDefault().TradingAccountNo}-{createdTradeOrders.FirstOrDefault().OrderNo}.txt";

					if (csvFormat)
					{
						if (requestedBroker.Name.ToLower().Contains("philip capital"))
						{
							csvBytes = _tradeOrderService.GenerateCsvForPhilipCapital(exchange, createdTradeOrders);

							var dateTime = DateTime.Now;

							fileName = $"Philip Capital-{dateTime}.csv";
						}
						else  // For other Brokers
						{
							csvBytes = _tradeOrderService.GeneratePoolOrderCsvFile(createdTradeOrders);
						}
					}
					else
					{
						using (MemoryStream stream = new())
						using (StreamWriter writer = new(stream))
						{
							writer.Write(csvBytes);
							writer.Flush();
							stream.Position = 0;
							txtBytes = ReadFully(stream);
						}
					}

					if (sendMailToBroker)
					{
						// Always generate both files for zipping
						if (csvBytes == null)
							csvBytes = _tradeOrderService.GeneratePoolOrderCsvFile(createdTradeOrders);

						if (txtBytes == null)
						{
							orderAsCsv = _tradeOrderService.GenerateStockOdinFileForBse(createdTradeOrders);
							using (MemoryStream stream = new())
							using (StreamWriter writer = new(stream))
							{
								writer.Write(orderAsCsv);
								writer.Flush();
								stream.Position = 0;
								txtBytes = ReadFully(stream);
							}
						}

						using (var zipStream = new MemoryStream())
						{
							using (var archive = new System.IO.Compression.ZipArchive(zipStream, System.IO.Compression.ZipArchiveMode.Create, true))
							{
								var csvEntry = archive.CreateEntry(fileName);
								using (var entryStream = csvEntry.Open())
								{
									entryStream.Write(csvBytes, 0, csvBytes.Length);
								}

								var txtEntry = archive.CreateEntry(txtFileName);
								using (var entryStream = txtEntry.Open())
								{
									entryStream.Write(txtBytes, 0, txtBytes.Length);
								}
							}
							zipStream.Position = 0;

							var orderAsExcel = await _tradeOrderService.GeneratePoolOrderExcelFile(createdTradeOrders);
							var broker = await _brokerRepository.Get(createdTradeOrders.FirstOrDefault().BrokerId);
							await _alphaNotificationService.SendOrdersToBroker(broker.OrderEmail, txtBytes, orderAsExcel, createdTradeOrders.ToList(), tenantName);

							return File(zipStream.ToArray(), "application/zip", $"Orders-{DateTime.Now:yyyyMMddHHmmss}.zip");
						}
					}
					else
					{
						if (csvFormat)
						{
							return File(csvBytes, "text/csv", fileName);
						}
						else
						{
							return File(txtBytes, "application/octet-stream", txtFileName);
						}
					}
				}

				else
				{
					//Check if all the orders are of NSE
					var isValid = validatedClientOrderEntries.All(coe => coe.Exchange.Equals("NSE", StringComparison.CurrentCultureIgnoreCase));
					if (!isValid)
					{
						throw new Exception("Not Every order has same exchange");
					}

					byte[] csvBytes = null;
					byte[] txtBytes = null;
					string fileName = $"Orders-{DateTime.Now}.csv";
					string txtFileName = $"{createdTradeOrders.FirstOrDefault().StrategyCode}-{createdTradeOrders.FirstOrDefault().TradingAccountNo}-{createdTradeOrders.FirstOrDefault().OrderNo}.txt";

					if (csvFormat)
					{
						if (requestedBroker.Name.ToLower().Contains("philip capital"))
						{
							csvBytes = _tradeOrderService.GenerateCsvForPhilipCapital(exchange, createdTradeOrders);

							var dateTime = DateTime.Now;

							fileName = $"Philip Capital-{dateTime}.csv";
						}
						else  // For other Brokers
						{
							csvBytes = _tradeOrderService.GeneratePoolOrderCsvFile(createdTradeOrders);
						}
					}
					else
					{
						orderAsCsv = _tradeOrderService.GenerateStockOdinFile(createdTradeOrders);
						using (MemoryStream stream = new())
						using (StreamWriter writer = new(stream))
						{
							writer.Write(orderAsCsv);
							writer.Flush();
							stream.Position = 0;
							txtBytes = ReadFully(stream);
						}
					}

					if (sendMailToBroker)
					{
						// Always generate both files for zipping
						if (csvBytes == null)
							csvBytes = _tradeOrderService.GeneratePoolOrderCsvFile(createdTradeOrders);

						if (txtBytes == null)
						{
							using (MemoryStream stream = new())
							using (StreamWriter writer = new(stream))
							{
								writer.Write(orderAsCsv);
								writer.Flush();
								stream.Position = 0;
								txtBytes = ReadFully(stream);
							}
						}

						using (var zipStream = new MemoryStream())
						{
							using (var archive = new System.IO.Compression.ZipArchive(zipStream, System.IO.Compression.ZipArchiveMode.Create, true))
							{
								var csvEntry = archive.CreateEntry(fileName);
								using (var entryStream = csvEntry.Open())
								{
									entryStream.Write(csvBytes, 0, csvBytes.Length);
								}

								var txtEntry = archive.CreateEntry(txtFileName);
								using (var entryStream = txtEntry.Open())
								{
									entryStream.Write(txtBytes, 0, txtBytes.Length);
								}
							}
							zipStream.Position = 0;

							var orderAsExcel = await _tradeOrderService.GeneratePoolOrderExcelFile(createdTradeOrders);
							var broker = await _brokerRepository.Get(createdTradeOrders.FirstOrDefault().BrokerId);
							await _alphaNotificationService.SendOrdersToBroker(broker.OrderEmail, txtBytes, orderAsExcel, createdTradeOrders.ToList(), tenantName);

							return File(zipStream.ToArray(), "application/zip", $"Orders-{DateTime.Now:yyyyMMddHHmmss}.zip");
						}
					}
					else
					{
						if (csvFormat)
						{
							return File(csvBytes, "text/csv", fileName);
						}
						else
						{
							return File(txtBytes, "application/octet-stream", txtFileName);
						}
					}
					//var result = new HttpResponseMessage(System.Net.HttpStatusCode.OK);
					//result.Content = new StringContent(orderAsCsv);
					//result.Content.Headers.ContentType = new MediaTypeHeaderValue("text/csv");
					//result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") 
					//{ 
					//	FileName = $"{createdTradeOrders.FirstOrDefault().StrategyCode}-{createdTradeOrders.FirstOrDefault().TradingAccountNo}-{createdTradeOrders.FirstOrDefault().OrderNo}.txt" 
					//};
					//return Ok(result);

				}

			}

			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpPost("[action]")]
		public async Task<IActionResult> SendDraftMfOrdersToExchange([FromBody] IEnumerable<ClientOrderEntry> clientOrderEntries)
		{
			if (clientOrderEntries == null || !clientOrderEntries.Any())
				return BadRequest("No orders found in request.");

			if (clientOrderEntries.Any(coe => coe.OrderStatus != OrderStatus.Draft))
				return BadRequest("Only orders in Draft status can be sent to exchange for execution. Please review your request.");

			var validatedClientOrderEntries = await _tradeOrderService.RevalidateDraftOrders(clientOrderEntries);
			if (validatedClientOrderEntries.Any(coe => coe.ValidityStatus == "Invalid"))
				return BadRequest(validatedClientOrderEntries);

			try
			{
				var ordersInDraft = clientOrderEntries.Where(coe => coe.OrderStatus == OrderStatus.Draft).ToList();
				var createdTradeOrders = await _tradeOrderService.CreateMfOrdersForBseStar(ordersInDraft, User.Identity.Name);

				var orderFileByte = await _tradeOrderService.GenerateBseStarMfFile(createdTradeOrders);
				var stream = new MemoryStream(orderFileByte);
				return File(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"MFBseOrder-{DateTime.Today.ToString("ddMMyyyy")}-{DateTime.Now.Ticks}.xlsx");
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpGet("[action]/{orderStatus}")]
		public async Task<IActionResult> GetPoolOrdersByStatus(OrderStatus orderStatus)
		{
			var tradeOrders = await _tradeOrderService.GetOrdersByStatus(orderStatus);
			return Ok(tradeOrders);
		}
		[HttpGet("[action]")]
		public async Task<IActionResult> GetSettlementSummaryList()
		{
			var settlementSummaryList = await _tradeOrderService.GetSettlementSummaryList();
			return Ok(settlementSummaryList);
		}
		[AllowAnonymous]
		[HttpPost("[action]")]
		public async Task<IActionResult> UploadSettlementFile(IFormFile file, bool isMutualFund = false)
		{
			try
			{
				//step 1:
				var filePath = await _fileStorageService.UploadTradeFile(file);

				//step 2:
				//add an entry in the TradeOrderSettlement table with a file path uploaded above
				var tradeOrderSettlementId = await _tradeOrderService.AddNewSettlementFile(User.Identity.Name, filePath, 0);

				//step 3:
				//Parse the file and map the data to TradeOrderSettlementFileLog with all entries as rows and a foreign key ref to the above created record
				var settlementDetailsFromFile = new TradeOrderSettlementFileUpload();
				if (isMutualFund)
					settlementDetailsFromFile = await _fileStorageService.DownloadAndReadMfFile(filePath);
				else
					settlementDetailsFromFile = await _fileStorageService.DownloadAndReadSettlementFile(filePath);

				//step 4:
				await _tradeOrderService.SaveFileData(settlementDetailsFromFile, tradeOrderSettlementId);

				return Ok(tradeOrderSettlementId);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpPost("[action]/{orderSettlementFileId}")]
		public async Task<IActionResult> CalculatePoolOrderSettlement(string orderSettlementFileId)
		{
			//step 5: Get all TradeOrderSettlementFileLogEntries using which the pool order stttlement should happen
			var settlementFileUpdatedEntries = await _tradeOrderService.GetTradeSettlementFileLogEntries(orderSettlementFileId);
			if (settlementFileUpdatedEntries == null || !settlementFileUpdatedEntries.Any())
				return BadRequest("Settlement data not found for the file symbol passed.");

			//step 4: Get all TradeOrders that have "PendingOrderQuantity" > 0, which will fetch all trade orders belonging to more than one
			//poolorder which will be required to calculate expected settlement values
			var tradeOrdersPendingSettlement = await _tradeOrderService.GetTradeOrdersPendingSettlement();
			if (tradeOrdersPendingSettlement == null || !tradeOrdersPendingSettlement.Any())
				return BadRequest("No trade orders pending settlement.");

			//step 5: foreach client order in TradeOrders calculate expected settlement values
			//Send the result back to the caller as a collection of OrderSettlementInClientAccount
			var clientSettlementCalcEntries =
				await _tradeOrderService.CalculateClientAllocations(settlementFileUpdatedEntries);

			return Ok(clientSettlementCalcEntries);
		}

		//[HttpPut("[action]/{orderSettlementFileId}")]
		//public async Task<IActionResult> UpdateClientPortfolios(string orderSettlementFileId)
		//{
		//	var clientSettlementCalcEntries = await _tradeOrderService.UpdateSettlementAndTradeOrderEntries(orderSettlementFileId, User.Identity.Name);

		//	var updatedPortfolios = await _clientOrderSvc.UpdateClientPortfolios(clientSettlementCalcEntries);

		//	var tenantClaim = User.Claims.FirstOrDefault(c => c.Type == "tenant");
		//	var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
		//	var tenantNameForQueue = tenantValue == "testing" ? "preprod" : tenantValue == "uat" ? "preprod" : tenantValue;
		//	foreach (var portfolio in updatedPortfolios.AsParallel())
		//	{
		//		await AlphaPortfolioServiceBusInterface.TriggerPortfolioPerformanceEngineRun(tenantValue, portfolio.Id, DateTime.Today.ConvertToIst(),
		//			_options.Value.AlphaPServiceBusConnectionString, _perfEngineConfig.Value.PortfolioPerfRequestQueue.Replace("<tenant>", tenantNameForQueue));
		//	}

		//	return Ok();
		//}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpPost("[action]")]
		public async Task<IActionResult> SendDraftOrdersToCancel([FromBody] IEnumerable<ClientOrderEntry> clientOrderEntries)
		{
			if (clientOrderEntries == null || !clientOrderEntries.Any())
				return BadRequest("No orders found in request.");

			if (clientOrderEntries.Any(coe => coe.OrderStatus != OrderStatus.Draft))
				return BadRequest("Only orders in Draft status can be sent to exchange for execution. Please review your request.");

			try
			{
				var ordersInDraft = clientOrderEntries.Where(coe => coe.OrderStatus == OrderStatus.Draft).ToList();
				await _clientOrderSvc.SendDraftOrdersToCancel(ordersInDraft, User.Identity.Name);
				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpPost("[action]")]
		public async Task<IActionResult> CancelSentToExchangeOrder(UpdateOrder updateOrder)
		{

			try
			{
				await _clientOrderSvc.CancelSentToExchangeOrder(updateOrder.OrderId, updateOrder.Rationale);
				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpPost("[action]")]
		public async Task<IActionResult> MoveSentToExchangeOrderToDraft(UpdateOrder updateOrder)
		{

			try
			{
				await _clientOrderSvc.MoveSentToExchangeOrderToDraft(updateOrder.OrderId, updateOrder.Rationale);
				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		private byte[] ReadFully(Stream input)
		{
			byte[] buffer = new byte[16 * 1024];
			using (MemoryStream ms = new MemoryStream())
			{
				int read;
				while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
				{
					ms.Write(buffer, 0, read);
				}
				return ms.ToArray();
			}
		}
	}
}
