﻿using Actlogica.AlphaPortfolios.ApiContracts.Transformer;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Transformer;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Pipelines;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("api/[controller]")]
	[ApiController]
	[Authorize]
	public class TransformerIntegrationController : ControllerBase
	{
		private readonly ITransformerIntegrationService _transformerIntegrationSvc;
		private readonly IFileStorageService _fileStorageService;
		private readonly IAlphaTransformerIntegrationRepository _integrationRepo;

		public TransformerIntegrationController(ITransformerIntegrationService transformerIntegrationSvc, 
			IFileStorageService fileStorageService, IAlphaTransformerIntegrationRepository integrationRepo)
		{
			_transformerIntegrationSvc = transformerIntegrationSvc;
			_fileStorageService = fileStorageService;
			_integrationRepo = integrationRepo;
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetSupportedConfigurations()
		{
			var integrations = await _transformerIntegrationSvc.GetIntegrationsAvailable();
			return Ok(integrations);
		}
		
		[HttpGet("[action]")]
		public async Task<IActionResult> GetSettlementConfigurations()
		{
			var integrations = await _transformerIntegrationSvc.GetSettlementIntegrationsAvailable();
			return Ok(integrations);
		}
		
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllocationConfigurations()
		{
			var integrations = await _transformerIntegrationSvc.GetAllocationIntegrationsAvailable();
			return Ok(integrations);
		}
		
		[HttpGet("[action]/{orderSettlementId}")]
		public async Task<IActionResult> GetRequestsForAllocationFile(string orderSettlementId)
		{
			var existingRequests = await _transformerIntegrationSvc.GetTransformerRequestsByType(TransformType.SettlementAllocation, orderSettlementId);

			//Custody Name, Date Generated, Status, Download Button
			var requests = new List<AllocationFileRequest>();
			foreach (var existingRequest in existingRequests)
			{
				var integrationProject = await _transformerIntegrationSvc.GetIntegrationByProjectCode(existingRequest.TransformerProjectCode);
				var request = new AllocationFileRequest
				{
					CustodyName = integrationProject.ToName,
					ProcessingStatus = existingRequest.ProcessingStatus,
					DateGenerated = existingRequest.CreatedDate.ToString("dd-MM-yyyy"),
					FileRequestId = existingRequest.Id
				};
				requests.Add(request);
			}

      return Ok(requests);
		}
		
		[HttpGet("[action]")]
		public async Task<IActionResult> GetRequestsForReconBankTxnFile()
		{
			var integrations = await _transformerIntegrationSvc.GetTransformerRequestsByType(TransformType.ReconBankTxn);
			return Ok(integrations);
		}

		[HttpPost("[action]/{transformerRequestId}")]
		public async Task<IActionResult> AllocationFile(IFormFile file, string transformerRequestId)
		{
			var transformerRequest = await _integrationRepo.GetTransformerRequest(transformerRequestId);
			try
			{
				using var stream = file.OpenReadStream();
				using var ms = new MemoryStream();
				await stream.CopyToAsync(ms);
				var fileBytes = ms.ToArray();

				var transformedFilePath = await _fileStorageService.UploadAllocationFile(fileBytes, file.FileName);
				transformerRequest.OutputFilePath = transformedFilePath;
				transformerRequest.ProcessingStatus = $"{TransformerProcessingStatus.Processed}";
				await _integrationRepo.UpdateTransformerRequest(transformerRequest);

				return Ok();
			}
			catch(Exception ex)
			{
				transformerRequest.ProcessingStatus = $"{TransformerProcessingStatus.Failed}";
				transformerRequest.FailureMessage = ex.Message;
				transformerRequest.FailureDescription = ex.StackTrace;
				await _integrationRepo.UpdateTransformerRequest(transformerRequest);
				return BadRequest("Error processing allocation file.");
			}
		}

		[HttpGet("[action]/{transformerRequestId}")]
		public async Task<IActionResult> DownloadAllocationFile(string transformerRequestId)
		{
			var transformerRequest = await _integrationRepo.GetTransformerRequest(transformerRequestId);
			if (transformerRequest == null)
				return BadRequest("Invalid request. Please contact support.");

			//Build the File Path.
			var streamData = await _fileStorageService.DownloadAllocationFile(transformerRequest.OutputFilePath);//Path.Combine(this.Environment.WebRootPath, "Files/") + fileName;

			//Read the File data into Byte Array.
			byte[] fileBytes = ReadFully(streamData);

			const string contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
			HttpContext.Response.ContentType = contentType;
			HttpContext.Response.Headers.Add("Access-Control-Expose-Headers", "Content-Disposition");

			var fileContentResult = new FileContentResult(fileBytes, contentType)
			{
				FileDownloadName = $"{transformerRequest.OutputFilePath}"
			};

			return fileContentResult;
		}

		private byte[] ReadFully(Stream input)
		{
			byte[] buffer = new byte[16 * 1024];
			using (MemoryStream ms = new MemoryStream())
			{
				int read;
				while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
				{
					ms.Write(buffer, 0, read);
				}
				return ms.ToArray();
			}
		}
	}

	public class AllocationFileRequest
	{
		public string CustodyName { get; set; }
		public string ProcessingStatus { get; set; }
		public string DateGenerated { get; set; }
		public string FileRequestId { get; set; }
	}
}
