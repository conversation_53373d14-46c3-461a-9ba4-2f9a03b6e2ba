using System;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{

    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class StrategicDeploymentController : AlphaBaseController
    {


        private readonly IStrategicDeploymentService _strategicDeploymentService;

        private readonly IMapper _mapper;


        public StrategicDeploymentController(IStrategicDeploymentService strategicDeploymentService, IMapper mapper, IHttpContextAccessor contextAccessor) : base(contextAccessor)
        {
            _strategicDeploymentService = strategicDeploymentService;
            _mapper = mapper;
            _strategicDeploymentService.SetCreatedBy(base.userName);
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> NewDeployment(StrategicDeploymentBody deployment)
        {
            try
            {
                var userName = User.Identity.Name;
                var strategicDeployment = _mapper.Map<StrategicDeployment>(deployment);
                strategicDeployment.CreatedBy = userName;
                await _strategicDeploymentService.NewDeployment(strategicDeployment);
                return new JsonResult(new { status = true, message = "Successfully Created New Deployment" });
            }
            catch (Exception e)
            {

                return StatusCode(400, new JsonResult(new { status = false, message = e.Message }));
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAll()
        {

            try
            {
                return Ok(await _strategicDeploymentService.GetAll());

            }

            catch (Exception e)
            {

                return new JsonResult(new { message = "Sorry there's some error in fetching" });
            }
        }


        [HttpGet("[action]/{trackerId}")]

        public async Task<IActionResult> TriggerInstallment(string trackerId)
        {

            try
            {
                await _strategicDeploymentService.ExecuteInstallment(trackerId);
                return Ok();
            }
            catch (Exception e)
            {
                return StatusCode(400, new JsonResult(new { message = $"{e.Message}", status = false }));
            }

        }


        [HttpGet("[action]")]

        public async Task<IActionResult> GetDeploymentTrackers()
        {

            try
            {
                return Ok(await _strategicDeploymentService.GetDeploymentTracker());
            }
            catch (Exception e)
            {
                return StatusCode(400, new JsonResult(new { status = false }));
            }

        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetSTPTrackerOrderDetails([FromQuery] string trackerId, [FromQuery] string stpType)
        {
            if (String.IsNullOrEmpty(trackerId))
            {
                return BadRequest(new { message = "Tracker Id is required", status = false });
            }

            if (stpType.ToLower() != "buy" && stpType.ToLower() != "sell")
            {
                return BadRequest(new { message = "Stp type should be either buy or sell", status = false });
            }

            var type = "STRATEGICDEPLOYMENTREDEMPTION";
            if (stpType.ToLower() == "buy")
            {
                type = "STRATEGICDEPLOYMENTINSTALLMENT";
            }
            var orders = await _strategicDeploymentService.GetSTPTrackerOrders(trackerId, type);

            return Ok(orders);

        }


    }

}