﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class AccountingController : ControllerBase
	{
		private readonly IPortfolioService _portfolioService;
		private readonly IModelPortfolioService _modelPortfolioService;

		public AccountingController(IPortfolioService portfolioService, IModelPortfolioService modelPortfolioService)
		{
			_portfolioService = portfolioService;
			_modelPortfolioService = modelPortfolioService;
		}

		[HttpGet("[action]/{strategyId}")]
		public async Task<IActionResult> GetPoolLedger(string strategyId)
		{
			var ledger = await _portfolioService.GetPoolLedger(strategyId);
			return Ok(ledger);
		}

		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetPortfolioLedger(string portfolioId)
		{
			var ledger = await _portfolioService.GetPortfolioLedger(portfolioId);
			return Ok(ledger);
		}
		
		[HttpGet("[action]/{strategyId}")]
		public async Task<IActionResult> GetPoolRegister(string strategyId)
		{
			var register = await _portfolioService.GetPoolRegister(strategyId);
			return Ok(register);
		}

		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetPortfolioRegister(string portfolioId)
		{
			var register = await _portfolioService.GetPortfolioRegister(portfolioId);
			return Ok(register);
		}

		[HttpPost("[action]/{portfolioId}")]
		public async Task<IActionResult> AddCapital(string portfolioId, double amount, bool isModelPortfolio)
		{
			if (isModelPortfolio)
				await _modelPortfolioService.AddCapital(portfolioId, amount);
			else
				await _portfolioService.AddCapital(portfolioId, amount, isModelPortfolio);
			return Ok();
		}
		
		[HttpPost("[action]/{portfolioId}")]
		public async Task<IActionResult> WithdrawCapital(string portfolioId, double amount, bool isModelPortfolio)
		{
			await _portfolioService.WithdrawCapital(portfolioId, amount, isModelPortfolio);
			return Ok();
		}
	}
}
