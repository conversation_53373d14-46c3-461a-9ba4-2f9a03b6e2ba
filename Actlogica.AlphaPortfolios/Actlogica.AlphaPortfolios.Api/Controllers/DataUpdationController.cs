using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.DataUpdates;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Capital;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.DataUpdate;
using Actlogica.AlphaPortfolios.ServiceIntegration.Income;
using AutoMapper;
using CsvHelper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{

    [Route("[controller]")]
    [Authorize]
    [ApiController]

    public class DataUpdationController : AlphaBaseController
    {
        private readonly IMapper _mapper;
        private readonly IDataUpdate _dataUpdate;
        private readonly ICsvParserService _csvParserService;
        private readonly IFileStorageService _fileStorageService;
        private readonly IDataUpdateRepository _dataUpdateRepository;
        private readonly IIncomeExpenseService _incomeExpenseService;
        private readonly ICapitalRegisterService _capitalRegisterService;


        public DataUpdationController(IFileStorageService fileStorageService, IIncomeExpenseService incomeExpenseService, 
        ICsvParserService csvParserService, IMapper mapper, IHttpContextAccessor contextAccessor, IDataUpdate dataUpdate, ICapitalRegisterService capitalRegisterService, IDataUpdateRepository dataUpdateRepository ) : base(contextAccessor)
        {
            _mapper = mapper;
            _dataUpdate = dataUpdate;
            _csvParserService = csvParserService;
            _fileStorageService = fileStorageService;
            _dataUpdateRepository = dataUpdateRepository;
            _incomeExpenseService = incomeExpenseService;
            _capitalRegisterService = capitalRegisterService;

        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllStatus()
        {

            try
            {
                return Ok(await _dataUpdateRepository.GetAll());

            }

            catch (Exception e)
            {

                return new JsonResult(new { message = "Fetching Failed" });

            }

        }


        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetStatusById(string id)
        {

            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("Id parameter is required.");

            }

            return Ok(await _dataUpdateRepository.GetStatusById(id));
        }


        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetSingleEntryForCapitalRegister(string id)
        {

            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("Id parameter is required.");

            }

            return Ok(await _dataUpdateRepository.GetSingleEntryForCapitalRegister(id));
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetSingleEntryForIncomeExpense(string id)
        {

            if (string.IsNullOrEmpty(id))
            {
                return BadRequest("Id parameter is required.");

            }

            return Ok(await _dataUpdateRepository.GetSingleEntryForIncomeExpense(id));
        }


        [HttpPost("[action]")]
        public async Task<IActionResult> UploadCapitalRegisterFileAsync(IFormFile file)
        {
            
            var canUpload = await _dataUpdate.CanUploadFile();

            if(!canUpload)
            {
                return BadRequest(new { message = "File Upload Restricted", status = false });
            }

            if (file.Length == 0)
            {
                
                return BadRequest(new { message = "Upload a valid file", status = false });
            }

            try
            {
                //If parsing failed throw error to the client
                var stream = file.OpenReadStream();
                var parseFile = _csvParserService.ParseCsvForCapitalRegisterCsvEntry(stream);

                //If parsing is successful upload the file to blob container
                var filePath = await _fileStorageService.UploadCapitalRegisterFile(file);

                var dataUpdateCapitalRegister = new DataUpdateFileUploads
                {
                    Type = ApiContracts.DataUpdates.DataUpdateType.CapitalRegister,
                    FilePath = filePath,
                    Status = ApiContracts.DataUpdates.DataUpdateStatus.Submitted,
                    CreatedBy = base.userName,
                    FailureMessage = null,
                    FailureDescription = null,
                    UpdateType = ApiContracts.DataUpdates.DataUpdateMethod.FileUpload
                };

                var tenantClaim = User.Claims.First(c => c.Type == "tenant");
			    var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;

                await _dataUpdate.InitiateDataUpdate(dataUpdateCapitalRegister, tenantValue);
            }

            catch (HeaderValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR : Failed to Parse the CSV, due to invalid headers: {e.InvalidHeaders}. Please Upload with Appropriate Headers", status = false });

            }

            catch (FieldValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR: Failed to Parse the Csv, due to invalid field : {e.Field}. Please Upload Appropriate Data", status = false });
            }

            catch (Exception e)
            {

                return BadRequest(new { message = $"ERROR: Failed to Update the Capital Register, {e.Message}", status = false });
            }

            return Ok(new { message = "File Uploaded for Processing", status = true });

        }


        [HttpGet("[action]")]
        public async Task<IActionResult> GetCapitalRegisterImportsAsync(string portfolioId)
        {

            return Ok(await _capitalRegisterService.GetCapitalRegisterImports(portfolioId));
        }


        [HttpPost("[action]")]
        public async Task<IActionResult> CreateCapitalRegister([FromBody] CapitalRegisterCreation newCapitalRegister)
        {

            try
            {
                var dataCapitalRegister = new DataUpdateFileUploads
                {
                    Type = ApiContracts.DataUpdates.DataUpdateType.CapitalRegister,
                    FilePath = "null",
                    Status = ApiContracts.DataUpdates.DataUpdateStatus.Submitted,
                    CreatedBy = base.userName,
                    FailureMessage = null,
                    FailureDescription = null,
                    UpdateType = ApiContracts.DataUpdates.DataUpdateMethod.SingleEntry
                };

                await _dataUpdate.InitiateCapitalRegisterDataUpdateEntry(dataCapitalRegister, _mapper.Map<AlphaPortfolios.ApiContracts.Portfolios.PortfolioCapitalRegister>(newCapitalRegister));
            }

            catch (System.Exception ex)
            {
                
                return BadRequest(ex.Message);
            }

            return Ok(new { message = "Capital Register data inserted successfully", status = true });

        }

        [HttpDelete("[action]")]
        public async Task<IActionResult> DeleteCapitalRegister(string faName, IFormFile file)
        {
            if (file.Length == 0)
            {

                return BadRequest(new { message = "Upload a valid file", status = false });
            }

            if (faName == null)
            {
                
                return UnprocessableEntity(new { message = "FaName is required", status = false });
            }

            try
            {

                var stream = file.OpenReadStream();
                var parseFile = _csvParserService.ParseCsvForCapitalRegisterDeleteCsvEntry(stream);
                var filePath = await _fileStorageService.UploadCapitalRegisterFile(file);

                await _capitalRegisterService.DeleteCapitalRegisterImport(faName, parseFile);

            }

            catch (HeaderValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR : Failed to Parse the CSV, due to invalid headers: {e.InvalidHeaders}. Please Upload with Appropriate Headers", status = false });
            }

            catch (FieldValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR: Failed to Parse the Csv, due to invalid field : {e.Field}. Please Upload Appropriate Data", status = false });
            }

            catch (Exception e)
            {

                return BadRequest(new { message = $"ERROR: Failed to Upload file for Delete Request, {e.Message}", status = false });
            }

            return Ok(new { message = "Uploaded the file for Delete Request", status = true });
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> UploadIncomeExpenseFileAsync(IFormFile file)
        {
            
            var canUpload = await _dataUpdate.CanUploadFile();

            if(!canUpload)
            {
                    return BadRequest(new { message = "File Upload Restricted", status = false });
            }


            if (file.Length == 0)
            {
                
                return BadRequest(new { message = "Upload a valid file", status = false });
            }


            try
            {
               
                var stream = file.OpenReadStream();
                var parseFile = _csvParserService.ParseCsvForIncomeExpenseCsvEntry(stream);

                
                var filePath = await _fileStorageService.UploadIncomeExpenseFile(file);

                var dataUpdateIncomeExpense = new DataUpdateFileUploads
                {
                    Type = ApiContracts.DataUpdates.DataUpdateType.IncomeExpense,
                    FilePath = filePath,
                    Status = ApiContracts.DataUpdates.DataUpdateStatus.Submitted,
                    CreatedBy = base.userName,
                    FailureMessage = null,
                    FailureDescription = null,
                    UpdateType = ApiContracts.DataUpdates.DataUpdateMethod.FileUpload
                };
                
                var tenantClaim = User.Claims.First(c => c.Type == "tenant");
			    var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
                
                await _dataUpdate.InitiateDataUpdate(dataUpdateIncomeExpense, tenantValue);


            }

            catch (HeaderValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR : Failed to Parse the CSV, due to invalid headers: {e.InvalidHeaders}. Please Upload with Appropriate Headers", status = false });

            }

            catch (FieldValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR: Failed to Parse the Csv, due to invalid field : {e.Field}. Please Upload Appropriate Data", status = false });
            }

            catch (Exception e)
            {

                return BadRequest(new { message = $"ERROR: Failed to Update the Income Expense, {e.Message}", status = false });
            }

            return Ok(new { message = "File Uploaded for Processing", status = true });

        }


        [HttpGet("[action]")]
        public async Task<IActionResult> GetIncomeExpense(string portfolioId)
        {

            return Ok(await _incomeExpenseService.GetIncomeExpense(portfolioId));
        }


        [HttpPost("[action]")]
        public async Task<IActionResult> CreateIncomeExpense([FromBody] IncomeExpenseEntryCreation newIncomeExpenseEntry)
        {
            try
            {
                var dataUpdateIncomeExpense = new DataUpdateFileUploads
                {
                    Type = ApiContracts.DataUpdates.DataUpdateType.IncomeExpense,
                    FilePath = "null",
                    Status = ApiContracts.DataUpdates.DataUpdateStatus.Submitted,
                    CreatedBy = base.userName,
                    FailureMessage = null,
                    FailureDescription = null,
                    UpdateType = ApiContracts.DataUpdates.DataUpdateMethod.SingleEntry
                };

                await _dataUpdate.InitiateIncomeExpenseDataUpdateEntry(dataUpdateIncomeExpense, _mapper.Map<AlphaPortfolios.ApiContracts.Portfolios.PortfolioCashLedger>(newIncomeExpenseEntry));

            }

            catch (System.Exception ex)
            {

                return BadRequest(ex.Message);
            }

            return Ok(new { message = "Income Expense data inserted Successfully", status = true });

        }

        [HttpDelete("[action]")]
        public async Task<IActionResult> DeleteIncomeExpense(string faName, IFormFile file)
        {
            if (file.Length == 0)
            {
                
                return BadRequest(new { message = "Upload a valid file", status = false });
            }

            if (faName == null)
            {

                return UnprocessableEntity(new { message = "FaName is required", status = false });
            }

            try
            {
                
                var stream = file.OpenReadStream();
                var parseFile = _csvParserService.ParseCsvForIncomeExpenseDeleteCsvEntry(stream);

                var filePath = await _fileStorageService.UploadIncomeExpenseFile(file);

                await _incomeExpenseService.DeleteIncomeExpense(faName, parseFile);

            }

            catch (HeaderValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR : Failed to Parse the CSV, due to invalid headers: {e.InvalidHeaders}. Please Upload with Appropriate Headers", status = false });

            }

            catch (FieldValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR: Failed to Parse the Csv, due to invalid field : {e.Field}. Please Upload Appropriate Data", status = false });
            }

            catch (Exception e)
            {

                return BadRequest(new { message = $"ERROR: Failed to Upload file for Delete Request, {e.Message}", status = false });
            }

            return Ok(new { message = "Uploaded the file for Delete Request", status = true });
        }

        [HttpGet("[action]/{dataUpdationRequestId}")]
		public async Task<FileResult> DownloadFile(string dataUpdationRequestId)
		{
			
            var dataUpdationRequest = await _dataUpdateRepository.Get(dataUpdationRequestId);
			
            if (dataUpdationRequest == null)
            {

	    		throw new System.Exception("Data Updation File with request id is not found");
            }

			var extension = dataUpdationRequest.FilePath.Split('.').Last();

			//Build the File Path.
			Stream streamData = await _fileStorageService.DownloadDataUpdateFile(dataUpdationRequest.FilePath);

			//Read the File data into Byte Array.
			byte[] bytes = ReadFully(streamData);

			string fileName;

			if (dataUpdationRequest != null)
            {
                
                fileName = $"{dataUpdationRequest.CreatedBy} - {dataUpdationRequest.Type}.{extension}";
            }

			else
            {

				fileName = $"{dataUpdationRequestId}-{dataUpdationRequest.Type}.{extension}";
            }

			//Send the File to Download.
			return File(bytes, "application/octet-stream", fileName);

		}

        private byte[] ReadFully(Stream input)
		{
			byte[] buffer = new byte[16 * 1024];
			using (MemoryStream ms = new MemoryStream())
			{
				int read;
				while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
				{
					ms.Write(buffer, 0, read);
				}
				return ms.ToArray();
			}
		}
        
        [HttpGet("[action]")]
		public async Task<bool> CanProceedUpdation()
		{
            return await _dataUpdate.CanUploadFile();
		}

    }
}
