﻿using Actlogica.AlphaPortfolios.Api.Utilities;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;
using Actlogica.AlphaPortfolios.ApiContracts.Orders.TradeAllocationFiles;
using Actlogica.AlphaPortfolios.ApiContracts.Transformer;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.RabbitMQ;
using Actlogica.AlphaPortfolios.ServiceIntegration.Transformer;
using Actlogica.AlphaPortfolios.Utils.Dates;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class OrderSettlementController : AlphaBaseController
	{
		private readonly IClientOrderManagementService _clientOrderSvc;
		private readonly IFileStorageService _fileStorageService;
		private readonly ITradeOrderService _tradeOrderService;
		private readonly IMasterDataService _masterDataService;
		private readonly ICustodyAllocationFileGenerator _custodyAllocationFileGenerator;
		private readonly IOptions<ConnectionStrings> _options;
		private readonly IOptions<PerformanceEngineConfigs> _perfEngineConfig;
		private readonly ITransformerIntegrationService _transformerIntegrationSvc;
		private readonly IGeneralSettingService _generalSettingService;
		private readonly IAlphaTransformerIntegrationRepository _alphaTransformerIntegrationRepo;

		private readonly IRabbitMqService _rabbitMqService;

		public OrderSettlementController(
				IClientOrderManagementService clientOrderSvc
				, IFileStorageService fileStorageService, ICustodyAllocationFileGenerator allocationFileGenerator
				, ITradeOrderService tradeOrderService, IMasterDataService masterDataService, IOptions<ConnectionStrings> options
			, IOptions<PerformanceEngineConfigs> perfEngineConfig, ITransformerIntegrationService transformerIntegrationSvc
			, IGeneralSettingService generalSettingService, IAlphaTransformerIntegrationRepository alphaTransformerIntegrationRepo, IRabbitMqService rabbitMqService, IHttpContextAccessor contextAccessor
				) : base(contextAccessor)
		{
			_clientOrderSvc = clientOrderSvc;
			_fileStorageService = fileStorageService;
			_tradeOrderService = tradeOrderService;
			_masterDataService = masterDataService;
			_custodyAllocationFileGenerator = allocationFileGenerator;
			_options = options;
			_perfEngineConfig = perfEngineConfig;
			_transformerIntegrationSvc = transformerIntegrationSvc;
			_generalSettingService = generalSettingService;
			_alphaTransformerIntegrationRepo = alphaTransformerIntegrationRepo;
			_rabbitMqService = rabbitMqService;
			_clientOrderSvc.SetCreatedBy(base.userName);
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetSettlementSummaryList()
		{
			var settlementSummaryList = (await _tradeOrderService.GetSettlementSummaryList()).OrderByDescending(s => s.CreatedDate);
			return Ok(settlementSummaryList);
		}

		[HttpGet("[action]/{orderSettlementProcessId}")]
		public async Task<IActionResult> GetSettlementStatus(string orderSettlementProcessId)
		{
			var settlementSummaryList = await _tradeOrderService.GetSettlementStatus(orderSettlementProcessId);
			return Ok(settlementSummaryList);
		}

		[AllowAnonymous]
		[HttpPost("[action]/{transformCode}")]
		public async Task<IActionResult> UploadSettlementFile(IFormFile file, int transformCode)
		{
			var tradeOrderSettlementId = string.Empty;
			try
			{
				var host = $"{this.Request.Scheme}://{this.Request.Host}{this.Request.PathBase}";
				var settings = Task.Run(() => _generalSettingService.GetAll()).Result;
				if (settings == null)
					throw new InvalidOperationException("Storage key not configured for tenant");

				var tenantClaim = settings.FirstOrDefault(s => s.Key == "TenantName");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;

				//step 1:
				var filePath = await _fileStorageService.UploadSettlementFile(file);

				//step 2:
				//add an entry in the TradeOrderSettlement table with a file path uploaded above
				tradeOrderSettlementId = await _tradeOrderService.AddNewSettlementFile(User.Identity.Name, filePath, transformCode);

				var token = Request.Headers["Authorization"].FirstOrDefault();
				var callBackUrl = $"{host}/ordersettlement/ProcessAlphaSettlementFile/{tradeOrderSettlementId}";
				var transformerPayload = new TransformerCallBackPayload
				{
					AlphaToken = token,
					CallBackUrl = callBackUrl,
					HttpType = RestSharp.Method.POST,
					Endpoint = "",
					FileName = file.FileName
				};

				using (MemoryStream memoryStream = new MemoryStream())
				{
					await file.CopyToAsync(memoryStream);
					transformerPayload.FileBytes = memoryStream.ToArray();
				}
				//step 3:
				//Invoke Transformer to migrate this file and transform
				await _transformerIntegrationSvc.InvokeTransformation(transformCode, transformerPayload);

				return Ok(tradeOrderSettlementId);
			}
			catch (Exception ex)
			{
				if (!string.IsNullOrEmpty(tradeOrderSettlementId))
					await _tradeOrderService.UpdateSettlementFileStatus(tradeOrderSettlementId, TransformerProcessingStatus.Failed, string.Empty, string.Empty, ex);
				return BadRequest($"Error: {ex.Message}. Stack: {ex.StackTrace}");
			}
		}

		[HttpPost("[action]/{settlementRequestId}")]
		public async Task<IActionResult> ProcessAlphaSettlementFile(IFormFile file, string settlementRequestId)
		{
			try
			{
				//step 1:
				var alphaFilePath = await _fileStorageService.UploadSettlementFile(file);

				//step 2:
				//Parse the file and add the data to TradeOrderSettlementFileLog with all entries as rows and a foreign key ref to the above created record
				var settlementDetailsFromFile = await _fileStorageService.DownloadAndReadSettlementFile(alphaFilePath);
				await _tradeOrderService.SaveFileData(settlementDetailsFromFile, settlementRequestId);

				//step 3: Get all TradeOrderSettlementFileLogEntries using which the pool order stttlement should happen
				var settlementFileUpdatedEntries = await _tradeOrderService.GetTradeSettlementFileLogEntries(settlementRequestId);
				if (settlementFileUpdatedEntries == null || !settlementFileUpdatedEntries.Any())
					return BadRequest("Settlement data not found for the file symbol passed.");

				//step 4: Get all TradeOrders that have "PendingOrderQuantity" > 0, which will fetch all trade orders belonging to more than one
				//poolorder which will be required to calculate expected settlement values
				var tradeOrdersPendingSettlement = await _tradeOrderService.GetTradeOrdersPendingSettlement();
				if (tradeOrdersPendingSettlement == null || !tradeOrdersPendingSettlement.Any())
					return BadRequest("No trade orders pending settlement.");

				//step 6: foreach client order in TradeOrders calculate expected settlement values
				//Send the result back to the caller as a collection of OrderSettlementInClientAccount
				var clientSettlementCalcEntries =
					await _tradeOrderService.CalculateClientAllocations(settlementFileUpdatedEntries);

				await _tradeOrderService.SaveClientAllocationCalculations(clientSettlementCalcEntries);

				await _tradeOrderService.UpdateSettlementFileStatus(settlementRequestId, TransformerProcessingStatus.Processed, string.Empty, alphaFilePath);

			}
			catch (Exception ex)
			{
				await _tradeOrderService.UpdateSettlementFileStatus(settlementRequestId, TransformerProcessingStatus.Failed, string.Empty, string.Empty, ex);
				return BadRequest();
			}

			return Ok();
		}

		[HttpGet("[action]/{orderSettlementFileId}")]
		public async Task<IActionResult> GetOrderSettlementDetails(string orderSettlementFileId, int skip = 0, int take = 10)
		{

			var clientSettlementCalcEntries =
				await _tradeOrderService.GetSettlementAllocationsFiltered(orderSettlementFileId, skip, take);

			return Ok(clientSettlementCalcEntries);
		}

		[HttpPost("[action]/{orderSettlementFileId}")]
		public async Task<IActionResult> GetAllocationFileGenerated(string orderSettlementFileId)
		{
			var allocationFilesGenerated =
				await _tradeOrderService.GetGeneratedAllocationFiles(orderSettlementFileId);

			return Ok(allocationFilesGenerated);
		}

		[HttpPost("[action]/{projectCode}")]
		public async Task<IActionResult> RequestAllocationFile(string orderSettlementFileId, int projectCode)
		{
			try
			{
				var transformerProject = await _transformerIntegrationSvc.GetIntegrationByProjectCode(projectCode);
				if (transformerProject == null)
					return BadRequest("Invalid request for allocation file, please check the request.");

				var allocationFileRequests = await _alphaTransformerIntegrationRepo.GetRequestsForAllocation(orderSettlementFileId);
				if (allocationFileRequests.Any(req => req.TransformerProjectCode == projectCode))
					return BadRequest($"Allocation file for {transformerProject.ToName} has already been generated.");

				var allocationResults = await _tradeOrderService.GetSettlementAllocations(orderSettlementFileId);
				if (!allocationResults.Any())
					return BadRequest("Settlement process does not have any allocations included.");

				var custodian = await _masterDataService.GetCustodianById(transformerProject.ToRefId);
				if (custodian == null)
					return BadRequest("Invalid request, please contact administrator.");

				var fileBytes = await _custodyAllocationFileGenerator.GenerateFile(allocationResults, custodian);
				var host = $"{this.Request.Scheme}://{this.Request.Host}{this.Request.PathBase}";
				var alphaAllocationFileName = $"{orderSettlementFileId}-{custodian.Name.Trim()}-{allocationResults.Count()}-{DateTime.Now.Ticks}.xlsx";
				var alphaFilePath = await _fileStorageService.UploadAllocationFile(fileBytes, alphaAllocationFileName);

				var newTransformRequest = new TransformerFileRequest
				{
					AlphaFilePath = alphaFilePath,
					AlphaTransformerIntegrationId = transformerProject.Id,
					ProcessingStatus = $"{TransformerProcessingStatus.Submitted}",
					RequestedBy = User.Identity.Name,
					TransformerProjectCode = projectCode,
					TransformType = TransformType.SettlementAllocation,
					MetaDataRefId = orderSettlementFileId
				};
				var transformRequestId = await _transformerIntegrationSvc.RegisterNewTransformRequest(newTransformRequest);

				var token = Request.Headers["Authorization"].FirstOrDefault();
				var callBackUrl = $"{host}/api/transformerintegration/allocationfile/{transformRequestId}";
				//var callBackUrl = $"https://qa.alphap.api.actlogica.com/transformerintegration/allocationfile/{transformRequestId}";
				var transformerPayload = new TransformerCallBackPayload
				{
					AlphaToken = token,
					CallBackUrl = callBackUrl,
					HttpType = RestSharp.Method.POST,
					Endpoint = "",
					FileName = alphaAllocationFileName,
					FileBytes = fileBytes
				};

				//step 3:
				//Invoke Transformer to migrate this file and transform
				await _transformerIntegrationSvc.InvokeTransformation(projectCode, transformerPayload);

				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}

		}

		[HttpPut("[action]/{orderSettlementFileId}")]
		public async Task<IActionResult> UpdateClientPortfoliosBeta(string orderSettlementFileId, bool cancelPendingOrders = false, bool releasePayout = false)
		{
			var currentTime = DateTime.UtcNow.TimeOfDay;
			var startTime = new TimeSpan(15, 30, 0); // 9 PM IST
			var endTime = new TimeSpan(2, 30, 0);    // 8 AM IST

			if (currentTime >= startTime || currentTime < endTime)
			{
				return BadRequest($"Service is not available between {startTime} PM and {endTime} AM. Current Time is {currentTime}");
			}

			var orderSettlementProcessDetails = await _tradeOrderService.GetSettlementStatus(orderSettlementFileId);
			if (orderSettlementProcessDetails != null)
			{
				if (orderSettlementProcessDetails.ProcessingStatus == $"{TransformerProcessingStatus.Settled}" || orderSettlementProcessDetails.ProcessingStatus == $"{TransformerProcessingStatus.Abandoned}")
					return BadRequest($"The Order is Already {orderSettlementProcessDetails.ProcessingStatus} ");
			}
			else
				return BadRequest("Invalid Settlement Process ID. Please contact support.");

			// Update Trade Order Entries
			//await _tradeOrderService.UpdateSettlementAndTradeOrderEntries(orderSettlementFileId, User.Identity.Name);

			try
			{
				var message = new QueueMessage<SettlementMessage>
				{
					Message = new SettlementMessage
					{
						Id = orderSettlementFileId,
						CancelPendingOrders = cancelPendingOrders,
						ReleasePayout = releasePayout

					},
					MessageType = MessageType.Settlement
				};

				var settings = new JsonSerializerSettings
				{
					ContractResolver = new CamelCasePropertyNamesContractResolver(),
					Formatting = Formatting.Indented,
					Converters = [new StringEnumConverter()]
				};

				var messageSerialised = JsonConvert.SerializeObject(message, settings);
				_rabbitMqService.PublishMessage(messageSerialised);
				return Ok();

			}
			catch (Exception)
			{

				return BadRequest(new { message = "Failed to Update Portfolio", status = "error" });
			}

		}


		[HttpPut("[action]/{orderSettlementFileId}")]
		public async Task<IActionResult> AbandonSettlement(string orderSettlementFileId)
		{

			try
			{
				await _tradeOrderService.AbandonSettlement(orderSettlementFileId);
				return Ok();
			}
			catch (Exception e)
			{

				return BadRequest(new { message = $"Error : {e.Message} ", status = false });
			}

		}
		[HttpPut("[action]/{orderSettlementFileId}")]
		public async Task<IActionResult> UpdateClientPortfolios(string orderSettlementFileId, bool cancelPendingOrders = false, bool releasePayout = false)
		{
			var currentTime = DateTime.UtcNow.TimeOfDay;
			var startTime = new TimeSpan(15, 30, 0); // 9 PM IST
			var endTime = new TimeSpan(2, 30, 0);    // 8 AM IST

			if (currentTime >= startTime || currentTime < endTime)
			{
				return BadRequest($"Service is not available between {startTime} PM and {endTime} AM. Current Time is {currentTime}");
			}

			var orderSettlementProcessDetails = await _tradeOrderService.GetSettlementStatus(orderSettlementFileId);
			if (orderSettlementProcessDetails != null)
			{
				if (orderSettlementProcessDetails.ProcessingStatus == $"{TransformerProcessingStatus.Settled}" || orderSettlementProcessDetails.ProcessingStatus == $"{TransformerProcessingStatus.Abandoned}")
					return BadRequest("The portfolios have already been updated.");
			}
			else
				return BadRequest("Invalid Settlement Process ID. Please contact support.");

			var clientSettlementCalcEntries = await _tradeOrderService.UpdateSettlementAndTradeOrderEntries(orderSettlementFileId, User.Identity.Name);

			var updatedPortfolios = await _clientOrderSvc.UpdateClientPortfolios(orderSettlementFileId, cancelPendingOrders, releasePayout);

			var tenantClaim = User.Claims.FirstOrDefault(c => c.Type == "tenant");
			var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
			var tenantNameForQueue = tenantValue == "testing" ? "preprod" : tenantValue == "uat" ? "preprod" : tenantValue;
			foreach (var portfolio in updatedPortfolios.AsParallel())
			{
				try
				{
					await AlphaPortfolioServiceBusInterface.TriggerPortfolioPerformanceEngineRun(tenantValue, portfolio.Id, DateTime.Today.ConvertToIst(),
						_options.Value.AlphaPServiceBusConnectionString, _perfEngineConfig.Value.PortfolioPerfRequestQueue.Replace("<tenant>", tenantNameForQueue));
				}
				catch (Exception ex)
				{

				}
			}

			await _tradeOrderService.UpdateSettlementFileStatus(orderSettlementFileId, ApiContracts.Transformer.TransformerProcessingStatus.Settled);

			return Ok();
		}

		[HttpPut("[action]/{orderSettlementFileId}")]
		public async Task<IActionResult> CloseOrders(string orderSettlementFileId, bool cancelPendingOrders = false)
		{
			var clientSettlementCalcEntries = await _tradeOrderService.UpdateSettlementAndTradeOrderEntries(orderSettlementFileId, User.Identity.Name);

			await _clientOrderSvc.CloseOrders(clientSettlementCalcEntries, cancelPendingOrders);

			await _tradeOrderService.UpdateSettlementFileStatus(orderSettlementFileId, ApiContracts.Transformer.TransformerProcessingStatus.Settled);
			return Ok();
		}


		[HttpGet("[action]")]
		public async Task<IActionResult> ValidateProcessEod()
		{
			try
			{
				var validations = await _clientOrderSvc.ValidateProcessEod();

				return Ok(new { validations, status = true });
			}
			catch (Exception ex)
			{
				return BadRequest(new { message = "Failed to Validate", status = false });
			}
		}


		[HttpGet("[action]/{orderSettlementFileId}")]
		public async Task<FileResult> DownloadFile(string orderSettlementFileId)
		{

			var settlementData = await _tradeOrderService.GetSettlementAllocations(orderSettlementFileId);

			if (settlementData == null)
			{
				throw new System.Exception("No Data found for requested ID.");
			}

			var csvBuilder = new StringBuilder();

			csvBuilder.AppendLine
			("Name, Client Custody Code,Scrip Code, ISIN, Scrip Name, Trade Type, Trade Date, Settlement Quantity,Pending Quantity, Market Rate, Market Amount, Settlement Brokerage, Expected Brokerage, Service Tax, Net Rate, Settlement Amount, Net Amount, Exchange, Custody Clearing Code, Strategy Name, Client Demat Number, Client Fa Code, Settlement Date, Party Code, Contract Number");

			foreach (var record in settlementData)
			{
				var pendingQuantity = record.OriginalQuantityOrdered-record.Quantity;

				csvBuilder.AppendLine
				($"{record.ClientShortName},{record.ClientCustodyCode},{record.ScripCode},{record.Isin},{record.ScripName},{record.SellBuy},{record.TradeDate}," +
				$"{record.Quantity},{pendingQuantity},{record.MarketRate},{record.MarketAmount},{record.ActualBrokerage},{record.ExpectedBrokerage},{record.ServiceTax},"+
				$"{record.NetRate},{record.SttAmount},{record.NetAmount},{record.Exchange},{record.CustodyClearingCode},{record.StrategyName},{record.ClientDematNumber},"+
				$"{record.ClientFaCode},{record.SettlementDate},{record.PartyCode},{record.ContractNumber}");
			}

			byte[] csvBytes = Encoding.UTF8.GetBytes(csvBuilder.ToString());

			string fileName = "Order Settlement.csv";

			return File(csvBytes, "text/csv", fileName);

		}

		[HttpGet("[action]/{orderSettlementProcessId}")]
		public async Task<IActionResult> GetAllocationsCount (string orderSettlementProcessId)
		{
			var totalAllocations = await _tradeOrderService.GetTotalAllocations(orderSettlementProcessId);
			return Ok(totalAllocations);
		}


	}
}
