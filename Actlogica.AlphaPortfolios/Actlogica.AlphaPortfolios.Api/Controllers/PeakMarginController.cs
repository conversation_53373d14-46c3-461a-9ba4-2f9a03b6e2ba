


using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.ApiContracts.Reports.Payloads;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.Holding;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [Route("[controller]")]
    [ApiController]
    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,Operations,OperationManager,DealerDeskTrader,PrincipleOfficer,CIO")]
    public class PeakMarginController : ControllerBase
    {

        private readonly IReportsService _reportService;

        private readonly IPeakMarginReport _peakMarginReport;
        public PeakMarginController(IReportsService reportService, IPeakMarginReport peakMarginReport)
        {

            _reportService = reportService;
            _peakMarginReport = peakMarginReport;


        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader,OperationManager,Operations")]
        [HttpPost("[action]")]
        public async Task<IActionResult> GenerateReportAutomatic([FromBody] PeakMarginAutomaticBody peakMarginBody)
        {

            try
            {
                var requestPayload = new PeakMarginRequestPayload { Format = $"{ReportFormat.Xlsx}", PortfolioIds = peakMarginBody.PortfolioId, PeakMarginPct = peakMarginBody.PeakMarginPct, Status = peakMarginBody.Status, Type = "Automatic", Amount = 0, FromDate = peakMarginBody.FromDate, ToDate = peakMarginBody.ToDate };
                var request = new ApiContracts.Reports.ReportRequest
                {
                    ClientId = "Not Applicable",
                    Format = $"{ReportFormat.Xlsx}",
                    PortfolioId = "Not Applicable",
                    ReportType = $"{ReportType.PeakMargin}",
                    RequestDate = System.DateTime.Now,
                    RequestedBy = User.Identity.Name,
                    Status = $"{ReportStatus.Submitted}",
                    RequestPayload = JsonConvert.SerializeObject(requestPayload)
                };

                var tenantClaim = User.Claims.First(c => c.Type == "tenant");
                var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
                request.TenantName = tenantValue;
                var reportRqst = await _reportService.InitiateReports(request);
                return Ok(new { data = reportRqst, status = true });
            }
            catch (Exception ex)
            {
                return BadRequest(new { status = false, message = ex.Message });
            }


        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader,OperationManager,Operations")]
        [HttpPost("[action]")]
        public async Task<IActionResult> GenerateReportManual([FromBody] PeakMarginManualBody peakMarginBody)
        {

            try
            {
                var requestPayload = new PeakMarginRequestPayload { Format = $"{ReportFormat.Xlsx}", PortfolioIds = peakMarginBody.PortfolioId, Amount = peakMarginBody.Amount, Type = "Manual", PeakMarginPct = peakMarginBody.PeakMarginPct };
                var request = new ApiContracts.Reports.ReportRequest
                {
                    ClientId = "Not Applicable",
                    Format = $"{ReportFormat.Xlsx}",
                    PortfolioId = "Not Applicable",
                    ReportType = $"{ReportType.PeakMargin}",
                    RequestDate = System.DateTime.Today,
                    RequestedBy = User.Identity.Name,
                    Status = $"{ReportStatus.Submitted}",
                    RequestPayload = JsonConvert.SerializeObject(requestPayload)
                };

                var tenantClaim = User.Claims.First(c => c.Type == "tenant");
                var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
                request.TenantName = tenantValue;
                var reportRqst = await _reportService.InitiateReports(request);
                return Ok(new { data = reportRqst, status = true });
            }
            catch (Exception ex)
            {
                return BadRequest(new { status = false, message = ex.Message });
            }


        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader,OperationManager,Operations")]
        [HttpGet("[action]/{peakMarginId}")]
        public async Task<IActionResult> ReversePeakMargin([FromQuery] string mode, string peakMarginId)
        {

            if (mode != null && mode.ToLower() == "manual")
            {
                await _peakMarginReport.ReversePeakMarginForManual(peakMarginId);
                return Ok();
            }
            else
            {
                await _peakMarginReport.ReversePeakMargin(peakMarginId);
                return Ok();
            }
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetPeakMarginSubmittedReports()
        {
            try
            {
                var reports = await _reportService.GetPeakMarginReportRequest();

                return Ok(reports);
            }
            catch (Exception e)
            {

                return BadRequest("Failed to Get");
            }

        }



    }

}