﻿using Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [Authorize]
    public class CustomisedTradeController : ControllerBase
    {
		private readonly ISecurityMasterService _securityMasterService;
		private readonly ILogger<SecuritiesMasterController> _logger;
		private readonly IMapper _mapper;
		public CustomisedTradeController(ISecurityMasterService securityMasterService,
			ILogger<SecuritiesMasterController> logger, IMapper mapper)
		{
			_securityMasterService = securityMasterService;
			_logger = logger;
			_mapper = mapper;
		}
		[HttpPost("[action]/{portfolioId}")]
		public async Task<IActionResult> Add(string portfolioId, [FromBody] IEnumerable<CustomisedOrder> newModel)
		{
			if (string.IsNullOrEmpty(portfolioId))
				return BadRequest("PortfolioId parameter is required.");

			return Ok(newModel);
		}

	}
}
