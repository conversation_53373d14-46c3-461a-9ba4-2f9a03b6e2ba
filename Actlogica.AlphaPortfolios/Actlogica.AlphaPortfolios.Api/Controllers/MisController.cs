﻿using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Quartz;
using System;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("api/[controller]")]
	[ApiController]
	[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,PrincipleOfficer,CIO,FundManager,OperationManager,Operations,AlphaRelationshipManager")]
	public class MisController : ControllerBase
	{
		private readonly IReportsService _service;
		private readonly IClientService _clientService;
		private readonly IGeneralSettingService _generalSettings;
		private readonly ISchedulerFactory _schedulerFactory;

		public MisController(IReportsService service, IClientService clientService, IGeneralSettingService generalSettingService, ISchedulerFactory schedulerFactory)
		{
			_service = service;
			_clientService = clientService;
			_generalSettings = generalSettingService;
			_schedulerFactory = schedulerFactory;
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> Test()
		{

			var scheduler = await _schedulerFactory.GetScheduler("QuartzScheduler");
			var jobKey = new JobKey("SystematicDeploymentJob");
			var details = await scheduler.GetTriggersOfJob(jobKey);
			// scheduler.RescheduleJob(details.ToList()[0].Key,"");
			return Ok();
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> Holdings(string asAt, bool withAcquisitionRate)
		{
			try
			{
				var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" , WithAcquistionRate = withAcquisitionRate};

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_Holdings}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> HoldingsLatest(bool withAcquisitionRate)
		{
			try
			{
				var asAtDate = DateTime.Now.AddDays(-1).Date.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" , WithAcquistionRate = withAcquisitionRate };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_Holdings_Latest}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> Dividends(string asAt)
		{
			try
			{
				var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_Dividends}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> IncomeExpense(string asAt)
		{
			try
			{
				var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_IncomeExpense}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> ClientMaster(string asAt)
		{
			try
			{
				var asAtDate = DateTime.ParseExact(asAt, "yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_ClientMaster}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> CapitalRegister(string fromDate,string toDate)
		{
			try
			{
				var parsedFromDate = DateTime.ParseExact(fromDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);
				var parsedToDate = DateTime.ParseExact(toDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { FromDate = parsedFromDate, ToDate = parsedToDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_CapitalRegister}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> Trades(string fromDate, string toDate)
		{
			try
			{
				var fromDateTime = DateTime.ParseExact(fromDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);
				var toDateTime = DateTime.ParseExact(toDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = toDateTime, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}", FromDate = fromDateTime, ToDate = toDateTime };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_Trades}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}


		[HttpPost("[action]")]
		public async Task<IActionResult> Transactions(string fromDate, string toDate)
		{
			try
			{
				var fromDateTime = DateTime.ParseExact(fromDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);
				var toDateTime = DateTime.ParseExact(toDate, "yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = toDateTime, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}", FromDate = fromDateTime, ToDate = toDateTime };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_Transactions}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> ModelPortfolioTransactions()
		{
			try
			{
				var asAtDate = DateTime.Now.AddDays(-1).Date.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_ModelPortfolioTransactions}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}
		[HttpPost("[action]")]
		public async Task<IActionResult> ModelPortfolioHoldingsLatest()
		{
			try
			{
				var asAtDate = DateTime.Now.AddDays(-1).Date.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);

				var generalSettings = await _generalSettings.GetAll();
				var misEmailRecipients = generalSettings.FirstOrDefault(v => v.Key == "MisEmailRecipients");
				var requestPayload = new { AsAtDate = asAtDate, ToEmail = misEmailRecipients.Value, Format = $"{ReportFormat.Xlsx}" };

				var request = new ApiContracts.Reports.ReportRequest
				{
					ClientId = "Not Applicable",
					Format = $"{ReportFormat.Xlsx}",
					PortfolioId = "Not Applicable",
					ReportType = $"{ReportType.Mis_ModelPortfolioHoldingsLatest}",
					RequestDate = System.DateTime.Today,
					RequestedBy = User.Identity.Name,
					Status = $"{ReportStatus.Submitted}",
					RequestPayload = JsonConvert.SerializeObject(requestPayload)
				};

				var tenantClaim = User.Claims.First(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				request.TenantName = tenantValue;
				return Ok(await _service.InitiateReports(request));
			}
			catch (FormatException ex)
			{
				return BadRequest("Invalid date format.");
			}
			catch (Exception ex)
			{
				return BadRequest("Unknown error. Please check the date format or contact administrator.");
			}
		}
	}
}
