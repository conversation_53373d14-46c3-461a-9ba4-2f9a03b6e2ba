﻿using Actlogica.AlphaPortfolios.Api.Utilities;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.TradeIdeaManagement;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	[Authorize]
	public class SellTradeIdeasController : AlphaBaseController
	{
		private readonly ISellTradeIdeaService _tradeSellIdeaSvc;
		private readonly IFileStorageService _fileStorageService;
		private readonly ITradeOrderService _tradeOrderService;
		private readonly IStrategyModelService _strategyModelService;
		private readonly IUriService _uriService;
		private readonly IClientOrderManagementService _clientOrderSvc;

		public SellTradeIdeasController(ISellTradeIdeaService tradeSellIdeaService, IFileStorageService fileStorageService, ITradeOrderService tradeOrderService, IStrategyModelService strategyModelService,
		IUriService uriService,
		IClientOrderManagementService clientOrderSvc
		, IHttpContextAccessor contextAccessor) : base(contextAccessor)
		{
			_tradeSellIdeaSvc = tradeSellIdeaService;
			_fileStorageService = fileStorageService;
			_tradeOrderService = tradeOrderService;
			_strategyModelService = strategyModelService;
			_uriService = uriService;
			_clientOrderSvc = clientOrderSvc;
			_tradeSellIdeaSvc.SetCreatedBy(base.userName);
			_clientOrderSvc.SetCreatedBy(base.userName);
		}


		[HttpGet("[action]/{TradeSellIdeaId}")]
		public async Task<IActionResult> GetSellTradeIdeaById(string TradeSellIdeaId)
		{
			var TradeSellIdea = await _tradeSellIdeaSvc.GetSellTradeIdeaById(TradeSellIdeaId);
			return Ok(TradeSellIdea);
		}

		[Authorize(Policy = "TradeIdeaAccessPolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllByStatus([FromQuery] PaginationFilter filter)
		{
			try
			{
				var route = Request.Path.Value;
				var validFilter = new PaginationFilter(filter.PageNumber, filter.PageSize, filter.OrderBy, filter.FilterBy, filter.FilterValue);
				var data = await _tradeSellIdeaSvc.GetAllSellTradeIdeasBasedUserRoles(validFilter, role, userId);
				var pagedReponse = PaginationHelper.CreatePagedReponse(data.Items, validFilter, data.TotalItems, _uriService, route);
				return Ok(pagedReponse);
			}
			catch (Exception e)
			{

				return BadRequest("Failed to Fetch");
			}
		}
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAll()
		{
			return Ok(await _tradeSellIdeaSvc.GetAllSellTradeIdeas());
		}


		[HttpPost("[action]")]
		public async Task<IActionResult> New([FromForm] IFormFile file, [FromForm] SellTradeIdeaModel newTradeSellIdea)
		{
			try
			{
				if (newTradeSellIdea.Status.ToLower() != "draft" && newTradeSellIdea.Status.ToLower() != "senttofm")
				{
					return BadRequest("Status should be either draft or sentToFM");
				}

				if (file == null || file.Length == 0)
				{
					return BadRequest(new JsonResult(new { status = false, message = "Attatchment File is Required" }));
				}

				//File should be less or equal to 5MB
				if (file.Length > 5 * Math.Pow(10, 6))
				{
					return BadRequest(new JsonResult(new { status = false, message = "Attatchment Size should be less than 5MB" }));

				}
				if (string.IsNullOrEmpty(newTradeSellIdea.SecurityName))
					return BadRequest("Security Name parameter is required.");

				if (!newTradeSellIdea.IsSellAll && newTradeSellIdea.Change <= 0.00)
					return BadRequest("Invalid change % received. Change has to be greater than 0.");


				return Ok(await _tradeSellIdeaSvc.AddNewSellTradeIdea(file, newTradeSellIdea, User.Identity.Name));
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpPut("[action]/{sellTradeIdeaId}")]
		public async Task<IActionResult> ApproveIdea(string sellTradeIdeaId)
		{
			try
			{
				await _tradeSellIdeaSvc.ApproveIdea(sellTradeIdeaId);

				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpPut("[action]/{sellTradeIdeaId}")]
		public async Task<IActionResult> RejectIdea(string sellTradeIdeaId)
		{
			try
			{
				await _tradeSellIdeaSvc.RejectIdea(sellTradeIdeaId);

				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpPut("[action]/{sellTradeIdeaId}")]
		public async Task<IActionResult> AbandonIdea(string sellTradeIdeaId)
		{
			try
			{
				await _tradeSellIdeaSvc.AbandonIdea(sellTradeIdeaId);

				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}


		[HttpDelete("[action]/{TradeSellIdeaId}")]
		public async Task<IActionResult> Delete(string TradeSellIdeaId)
		{
			try
			{
				await _tradeSellIdeaSvc.DeleteSellTradeIdea(TradeSellIdeaId);
				return Ok();
			}
			catch (Exception e)
			{
				return StatusCode(400, new JsonResult(new { status = false, message = e.Message }));
			}
		}

		[HttpPut("[action]/{id}")]
		public async Task<IActionResult> Update([FromForm] IFormFile file, [FromForm] SellTradeIdeaModel existingTradeIdea, string id)
		{

			try
			{
				if (existingTradeIdea.Status.ToLower() != "draft" && existingTradeIdea.Status.ToLower() != "senttofm")
				{
					return BadRequest("Can Only update to Draft and Send To FM");
				}
				await _tradeSellIdeaSvc.UpdateSellTradeIdea(file, existingTradeIdea, id, User.Identity.Name);
				return Ok(existingTradeIdea);
			}
			catch (Exception e)
			{

				return BadRequest(e.Message);
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> UploadSettlementFile(IFormFile file)
		{
			//step 1:
			var filePath = await _fileStorageService.UploadTradeFile(file);

			//step 2:
			//add an entry in the TradeOrderSettlement table with a file path uploaded above
			var tradeOrderSettlementId = await _tradeSellIdeaSvc.AddNewTradeSellIdeaFile(User.Identity.Name, filePath);

			//step 3:
			//Parse the file and add the data to TradeOrderSettlementFileLog with all entries as rows and a foreign key ref to the above created record
			//var settlementDetailsFromFile = await _fileStorageService.DownloadAndReadFile(filePath);
			//await _tradeOrderService.SaveFileData(settlementDetailsFromFile, tradeOrderSettlementId);

			return Ok(new { filePath });
		}
		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> GetTradeSellIdeaModelSecuritybyModelId(string modelId)
		{
			var securitiesInModel = await _strategyModelService.GetSellTradeIdeaModelSecurityId(modelId);
			return Ok(securitiesInModel);
		}

		//public async FileResult OnGetDownloadFile(string fileName)
		//{
		//    //Build the File Path.
		//    string path = await _fileStorageService.DownloadFile(fileName);//Path.Combine(this.Environment.WebRootPath, "Files/") + fileName;

		//    //Read the File data into Byte Array.
		//    byte[] bytes = System.IO.File.ReadAllBytes(path);

		//    //Send the File to Download.
		//    return File(bytes, "application/octet-stream", fileName);
		//}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> CalculateTradeIdeaWeight(TradeIdeaCalculateWeight requestModel)
		{
			try
			{
				var securitiesInModel = await _tradeSellIdeaSvc.CalculateTradeIdeaWeight(requestModel);
				return Ok(securitiesInModel);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}
		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> GetTradeIdeaModelSecuritybyModelId(string modelId)
		{
			var securitiesInModel = await _strategyModelService.GetSellTradeIdeaModelSecurityId(modelId);
			return Ok(securitiesInModel);
		}
		[HttpGet("[action]/{fileName}")]
		public async Task<FileResult> DownloadFile(string fileName)
		{
			//Build the File Path.
			Stream streamData = await _fileStorageService.DownloadTradeFile(fileName);//Path.Combine(this.Environment.WebRootPath, "Files/") + fileName;

			//Read the File data into Byte Array.
			byte[] bytes = ReadFully(streamData);

			//Send the File to Download.
			return File(bytes, "application/octet-stream", fileName);
		}
		[HttpPut("[action]/{sellTradeIdeaId}")]
		public async Task<IActionResult> CreateOrdersForIdea(string sellTradeIdeaId)
		{
			try
			{
				var sellTradeIdea = await _tradeSellIdeaSvc.GetSellTradeIdeaById(sellTradeIdeaId);
				if (sellTradeIdea == null)
					return NotFound("Invalid request. Please contact the administrator.");

				if (sellTradeIdea.IsSellAll)
					await _clientOrderSvc.CreateClientOrders(await _tradeSellIdeaSvc.PrepareOrdersForSellAllIdea(sellTradeIdea));
				else
					await _clientOrderSvc.CreateClientOrders(await _tradeSellIdeaSvc.PrepareOrdersForSellIdea(sellTradeIdea));

				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}
		private byte[] ReadFully(Stream input)
		{
			byte[] buffer = new byte[16 * 1024];
			using (MemoryStream ms = new MemoryStream())
			{
				int read;
				while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
				{
					ms.Write(buffer, 0, read);
				}
				return ms.ToArray();
			}
		}
	}
}
