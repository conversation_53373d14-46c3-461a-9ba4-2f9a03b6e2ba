﻿using Actlogica.AlphaPortfolios.Api.Types.PortfolioPerformance;
using Actlogica.AlphaPortfolios.Api.Utilities;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage.PerformanceEngine;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.Utils.Dates;
using AutoMapper;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("api/[controller]")]
	[ApiController]
	//[Authorize]
	public class PerformanceEngineController : ControllerBase
	{
		private readonly IModelPortfolioRepository _modelPortfolioRepository;
		private readonly IPortfolioRepository _portfolioRepository;
		private readonly IPortfolioService _portfolioService;
		private readonly IOptions<ConnectionStrings> _connStrOptions;
		private readonly IOptions<PerformanceEngineConfigs> _perfEngineConfig;
		private readonly IMarketIndexDataStorageRepository _marketIndexStorageRepo;
		private readonly IPortfolioPerformanceStorageRepository _performanceStorageRepo;
		private readonly IPortfolioAnalyticsStorageRepository _analyticsStorageRepo;

		public PerformanceEngineController(IModelPortfolioRepository modelPortfolioRepository, IPortfolioRepository portfolioRepository
			, IOptions<ConnectionStrings> connStrOptions, IOptions<PerformanceEngineConfigs> perfEngineConfig, 
			IMarketIndexDataStorageRepository marketIndexRepo, IPortfolioService portfolioService
		 	, IGeneralSettingService generalSettingService, IMapper mapper)
		{

			var settings = Task.Run(() => generalSettingService.GetAll()).Result;
			if (settings == null)
				throw new InvalidOperationException("Storage key not configured for tenant");

			var tenantClaim = settings.FirstOrDefault(s => s.Key == "TenantName");
			var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
			var storageAccountKey = settings.FirstOrDefault(s => s.Key == "StorageAccountKey");
				
			_modelPortfolioRepository = modelPortfolioRepository;
			_portfolioRepository = portfolioRepository;
			_connStrOptions = connStrOptions;
			_perfEngineConfig = perfEngineConfig;
			_marketIndexStorageRepo = marketIndexRepo;
			_portfolioService = portfolioService;
			_analyticsStorageRepo = new PortfolioAnalyticsStorageRepository(mapper, tenantValue, storageAccountKey.Value);
			_performanceStorageRepo = new PortfolioPerformanceStorageRepository(mapper, tenantValue, storageAccountKey.Value);
		}

		[HttpGet("[action]/{secretKey}")]
		public async Task<IActionResult> InvokeModelPortfolioPerformance(string secretKey)
		{
			if (string.IsNullOrEmpty(secretKey))
				return Unauthorized();

			if (secretKey != _perfEngineConfig.Value.PortfolioPerfFnSecretKey)
				return Unauthorized();

			var tenantClaim = User.Claims.FirstOrDefault(c => c.Type == "tenant");
			var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;

			var modelPortfolios = await _modelPortfolioRepository.GetAll();
			foreach(var modelPortfolio in modelPortfolios)
			{
				//await AlphaPortfolioServiceBusInterface.TriggerModelPortfolioPerformanceEngineRun(tenantValue, 
				//	modelPortfolio.Id, DateTime.Today.AddDays(-1).ConvertToIst(), 
				//	_connStrOptions.Value.AlphaPServiceBusConnectionString, _perfEngineConfig.Value.PortfolioPerfRequestQueue.Replace("<tenant>", tenantValue));
			}

			return Ok("Thumbs up! wink wink!");
		}

		[HttpGet("[action]/{secretKey}")]
		public async Task<IActionResult> InvokePortfolioPerformance(string secretKey)
		{
			if (string.IsNullOrEmpty(secretKey))
				return Unauthorized();

			if (secretKey != _perfEngineConfig.Value.PortfolioPerfFnSecretKey)
				return Unauthorized();

			var tenantClaim = User.Claims.FirstOrDefault(c => c.Type == "tenant");
			var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;

			var portfolios = await _portfolioRepository.GetAll();
			foreach(var portfolio in portfolios)
			{
				await AlphaPortfolioServiceBusInterface.TriggerPortfolioPerformanceEngineRun(tenantValue,
					portfolio.Id, DateTime.Today.AddDays(-1).ConvertToIst(),
					_connStrOptions.Value.AlphaPServiceBusConnectionString, _perfEngineConfig.Value.PortfolioPerfRequestQueue.Replace("<tenant>", tenantValue));
			}

			return Ok("Thumbs up! wink wink!");
		}

		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetBenchmarkHistory(string portfolioId, int indexCode = 266)
		{
			var portfolio = await _portfolioService.GetPortfolioById(portfolioId);
			if (portfolio == null)
				return BadRequest("Invalid portfolio ID");

			if (portfolio.BenchmarkIndexCode > 0)
				indexCode = portfolio.BenchmarkIndexCode;

			var indexHistory = await _marketIndexStorageRepo.GetAllCloseValues($"{indexCode}");

			var benchmarkValidDataRange = indexHistory.Where(iv => iv.AsAtDate >= portfolio.StartDate && !string.IsNullOrEmpty(iv.Close)).Select(iv => new BenchmarkHistoryResponse { AsAtDate = iv.AsAtDate, Close = Convert.ToDouble(iv.Close) });

			var adjBchValues = new List<BenchmarkHistoryResponse>();
			var startValue = Convert.ToDouble(benchmarkValidDataRange.OrderBy(d => d.AsAtDate).FirstOrDefault().Close);
			foreach(var indexValue in benchmarkValidDataRange.OrderBy(d => d.AsAtDate))
			{
				var close = Math.Round((indexValue.Close / startValue) * 100, 2);
				indexValue.Close = close;
				adjBchValues.Add(new BenchmarkHistoryResponse { Close = close, AsAtDate = indexValue.AsAtDate });
			}

			return Ok(adjBchValues);
		}
		
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetAnalyticsHistory(string portfolioId, DateTime asAtDate)
		{
			var analytics = await _analyticsStorageRepo.GetAnalyticsForTheDay(portfolioId, asAtDate);

			var portfolioAnalytics = new PortfolioAnalytics();
			var sectorBreakup = new List<ExposureWithReturns>();
			var marketCapBreakup = new List<ExposureWithReturns>();
			foreach(var sectorAnalytic in analytics.Where(an => an.AggregateType == "Industry"))
			{
				var sectorExposure = new ExposureWithReturns
				{
					Name = sectorAnalytic.InvestmentName,
					Weight = Math.Round(sectorAnalytic.Weight, 2),
					Return = Math.Round(sectorAnalytic.Xirr, 2)
				};
				sectorBreakup.Add(sectorExposure);
			}
			portfolioAnalytics.SectorBreakup = sectorBreakup;
			portfolioAnalytics.MarketCapBreakup = marketCapBreakup;

			return Ok(portfolioAnalytics);
		}


	}
}
