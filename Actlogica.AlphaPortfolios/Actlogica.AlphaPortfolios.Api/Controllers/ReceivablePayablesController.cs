using Actlogica.AlphaPortfolios.ApiContracts.ReceivablePayables;
using Actlogica.AlphaPortfolios.ApiContracts.TradeCompliance;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.ServiceIntegration.ReceivablePayables;
using Actlogica.AlphaPortfolios.ServiceIntegration.TradeCompliance;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.ServiceBus;
using System;

using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{

    [Route("[controller]")]
    [Authorize]
    [ApiController]

    public class ReceivablePayablesController : AlphaBaseController
    {
        private readonly IMapper _mapper;
        private readonly IPortfolioReceivableService _portfolioReceivableService;

        public ReceivablePayablesController(IMapper mapper, IHttpContextAccessor contextAccessor, IPortfolioReceivableService portfolioReceivableService) : base(contextAccessor)
        {
            _mapper = mapper;
            _portfolioReceivableService = portfolioReceivableService;
        }

        [HttpGet("[action]/{transactionType}")]
        public async Task<IActionResult> GetByTransactionType(string transactionType)
        {
            try
            {
                var receivablePayables = await _portfolioReceivableService.GetByType(transactionType);
                return Ok(receivablePayables);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var receivablePayables = await _portfolioReceivableService.GetById(id);
                return Ok(receivablePayables);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> AddReceivableOrPayable(ReceivablePayablesCreation receivablePayables)
        {
            try
            {
                await _portfolioReceivableService.AddReceivableOrPayable(receivablePayables);
                return Ok(new { Message = "Receivable / Payable Created  Successfully" });

            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> Book(BookPayableReceivable bookPayableReceivable)
        {
            try
            {
                await _portfolioReceivableService.BookPayableReceivable(bookPayableReceivable);
                return Ok(new { Message = " Receivable / Payable Booked Successfully " });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }


    }
}
