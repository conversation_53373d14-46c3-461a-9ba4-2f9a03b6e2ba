using Actlogica.AlphaPortfolios.ApiContracts.TradeCompliance;
using Actlogica.AlphaPortfolios.ServiceIntegration.TradeCompliance;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;

using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{

    [Route("[controller]")]
    [Authorize]
    [ApiController]

    public class TradeComplianceController : AlphaBaseController
    {
        private readonly IMapper _mapper;
        private readonly ITradeComplianceService _tradeComplianceService;

        public TradeComplianceController(IMapper mapper, IHttpContextAccessor contextAccessor, ITradeComplianceService tradeComplianceService) : base(contextAccessor)
        {
            _mapper = mapper;
            _tradeComplianceService = tradeComplianceService;

        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllPreTradeRuleTypes()
        {
            try
            {
                var preTradeRuleTypes = await _tradeComplianceService.GetAllPreTradeRuleTypes();
                return Ok(preTradeRuleTypes);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetPreTradeRuleTypeById(string id)
        {
            try
            {
                var preTradeRule = await _tradeComplianceService.GetPreTradeRuleTypeById(id);
                return Ok(preTradeRule);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> CreatePreTradeRuleType(PreTradeRuleTypeCreation preTradeRuleTypeCreation)
        {
            try
            {
                await _tradeComplianceService.CreatePreTradeRuleType(preTradeRuleTypeCreation);
                return Ok(new { message = "Pre Trade Rule Type Created Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create the Pre Trade Rule Type : {e.Message} " });
            }

        }

        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> EditPreTradeRuleType(string id, PreTradeRuleTypeCreation preTradeRuleTypeCreation)
        {
            try
            {
                await _tradeComplianceService.UpdatePreTradeRuleType(id, _mapper.Map<ApiContracts.TradeCompliance.PreTradeRuleTypeCreation>(preTradeRuleTypeCreation));
                return Ok(new { message = "Pre Trade Rule Type Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Sub Type : {e.Message} " });
            }

        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllPreTradeRuleSubTypes()
        {
            try
            {
                var preTradeRuleTypes = await _tradeComplianceService.GetAllPreTradeRuleSubTypes();
                return Ok(preTradeRuleTypes);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetPreTradeRuleSubTypeById(string id)
        {
            try
            {
                var preTradeRule = await _tradeComplianceService.GetPreTradeRuleSubTypeById(id);
                return Ok(preTradeRule);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> CreatePreTradeRuleSubType(PreTradeRuleSubTypeCreation preTradeRuleSubTypeCreation)
        {
            try
            {
                await _tradeComplianceService.CreatePreTradeRuleSubType(preTradeRuleSubTypeCreation);
                return Ok(new { message = "Pre Trade Rule Sub Type Created Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create the Pre Trade Rule Sub Type : {e.Message} " });
            }

        }

        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> EditPreTradeRuleSubType(string id, PreTradeRuleSubTypeCreation preTradeRuleSubTypeCreation)
        {
            try
            {
                await _tradeComplianceService.UpdatePreTradeRuleSubType(id, _mapper.Map<ApiContracts.TradeCompliance.PreTradeRuleSubTypeCreation>(preTradeRuleSubTypeCreation));
                return Ok(new { message = "Pre Trade Rule Type Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Sub Type : {e.Message} " });
            }

        }
        [HttpGet("[action]/{type}")]
        public async Task<IActionResult> GetAllPreTradeRules(string type)
        {
            try
            {
                var preTradeRuleTypes = await _tradeComplianceService.GetAllPreTradeRules(type);
                return Ok(preTradeRuleTypes);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetPreTradeRuleById(string id, string level)
        {
            try
            {
                var preTradeRule = await _tradeComplianceService.GetPreTradeRuleById(id, level);
                return Ok(preTradeRule);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> CreatePreTradeRule(PreTradeRuleCreation preTradeRuleCreation)
        {
            try
            {
                await _tradeComplianceService.CreatePreTradeRule(preTradeRuleCreation);
                return Ok(new { message = "Pre Trade Rule Created Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Create the Pre Trade Rule : {e.Message} " });
            }

        }

        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> EditPreTradeRule(string id, EditPreTradeRule editPreTradeRule)
        {
            try
            {
                await _tradeComplianceService.UpdatePreTradeRule(id, editPreTradeRule);
                return Ok(new { message = "Pre Trade Rule Updated Successfully" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to Update the Pre Trade Rule : {e.Message} " });
            }

        }

        [HttpGet("[action]/{typeId}")]
        public async Task<IActionResult> GetPreTradeSubTypesByTypeId(string typeId)
        {
            try
            {
                var preTradeRule = await _tradeComplianceService.GetAllPreTradeRuleTypes(typeId);
                return Ok(preTradeRule);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed to fetch : {e.Message} " });
            }
        }
        

    }
}
