﻿using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class StrategyModelController : AlphaBaseController
	{
		private readonly IStrategyModelService _strategyModelSvc;
		private readonly IStrategyService _strategySvc;

		private readonly IClientService _clientService;

		private readonly IPortfolioService _portfolioService;

		public StrategyModelController(IStrategyModelService strategyModelSvc, IStrategyService strategySvc, IClientService clientService, IPortfolioService portfolioService, IHttpContextAccessor contextAccessor
			) : base(contextAccessor)
		{
			_strategyModelSvc = strategyModelSvc;
			_strategySvc = strategySvc;
			_clientService = clientService;
			_portfolioService = portfolioService;
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> GetModel(string modelId)
		{
			return Ok(await _strategyModelSvc.GetModelById(modelId));
		}


		[HttpGet("[action]/{strategyId}")]
		public async Task<IActionResult> GetModelsInStrategy(string strategyId, [FromQuery] bool openModel)
		{
			if (string.IsNullOrEmpty(strategyId))
				return BadRequest("StrategyId parameter is required.");

			var thisStrategy = await _strategySvc.GetStrategyById(strategyId);
			if (thisStrategy == null)
				return NotFound("Strategy does not exist in Alpha Portfolio.");

			if (openModel)
			{
				return Ok(await _strategyModelSvc.GetOpenModelsInStrategy(strategyId));
			}
			else
			{
				return Ok(await _strategyModelSvc.GetModelsInStrategy(strategyId));
			}
		}


		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]/{strategyId}")]
		public async Task<IActionResult> Add(string strategyId, [FromBody] Model newModel)
		{
			if (string.IsNullOrEmpty(strategyId))
				return BadRequest("StrategyId parameter is required.");

			var thisStrategy = await _strategySvc.GetStrategyById(strategyId);
			if (thisStrategy == null)
				return NotFound("Strategy does not exist in Alpha Portfolio.");

			return Ok(await _strategyModelSvc.Create(strategyId, newModel));
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]/{strategyId}")]
		public async Task<IActionResult> Create(string strategyId, [FromBody] Model newModel)
		{
			if (string.IsNullOrEmpty(strategyId))
				return BadRequest("StrategyId parameter is required.");

			var thisStrategy = await _strategySvc.GetStrategyById(strategyId);
			if (thisStrategy == null)
				return NotFound("Strategy does not exist in Alpha Portfolio.");

			return Ok(await _strategyModelSvc.Create(strategyId, newModel));
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPut("[action]/{strategyId}")]
		public async Task<IActionResult> Update(string strategyId, [FromBody] Model newModel)
		{
			if (string.IsNullOrEmpty(strategyId))
				return BadRequest("StrategyId parameter is required.");

			var thisStrategy = await _strategySvc.GetStrategyById(strategyId);
			if (thisStrategy == null)
				return NotFound("Strategy does not exist in Alpha Portfolio.");

			await _strategyModelSvc.Edit(newModel);

			return Ok();
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAll()
		{
			try
			{
				var userRoles = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
				var userId = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
				return Ok(await _strategyModelSvc.GetAll(userRoles, userId));
			}
			catch (Exception e)
			{
				return BadRequest("Failed to Fetch Strategy Models");
			}
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> GetAllStrategyModelsOfStrategies([FromBody] string[] strategyId)
		{
			try
			{
				var userRoles = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
				var userId = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
				return Ok(await _strategyModelSvc.GetAllStrategyModelsOfStrategies(strategyId, userRoles, userId));
			}
			catch (Exception e)
			{
				return BadRequest("Failed to Fetch Strategy Models");
			}
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllWithStrategy()
		{
			try
			{
				var userRoles = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
				var userId = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
				return Ok(await _strategyModelSvc.GetStrategyModelsWithStrategyBasedOnUserRoles(userRoles, userId));
			}
			catch (Exception e)
			{
				return BadRequest("Failed to Fetch Strategy Models");
			}
		}


		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> ClientsInModel([FromBody] List<string> modelId)
		{
			var clientsSummary = await _strategyModelSvc.GetClientsByGroupOfModelId(modelId);
			return Ok(clientsSummary);
		}

		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> ClientsInModelByModelId(string modelId)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);

				var portfolios = await _portfolioService.GetByClientId(client.Id);

				//Check whether the user has
				if (!portfolios.Any(p => p.ModelId == modelId))
				{
					return Ok(new List<ApiContracts.Clients.ClientWithPortfolioSummary>());
				}
				else
				{
					var clientsInModel = await _strategyModelSvc.GetClientsByModelId(modelId);
					return Ok(clientsInModel.Where(cm => cm.ClientId == client.Id));
				}

			}
			var clientsSummary = await _strategyModelSvc.GetClientsByModelId(modelId);
			return Ok(clientsSummary);
		}


		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> GetModelsForPeakMargin([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate, PeakMarginModel peakMarginModel)
		{
			if (!fromDate.HasValue || !toDate.HasValue)
			{
				return BadRequest("Provide from and to date");
			}

			if (fromDate > toDate)
			{
				return BadRequest("toDate Should Be less than FromDate");
			}

			var actualFromDate = fromDate.Value;
			var actualToDate = toDate.Value;


			var models = await _strategyModelSvc.GetModelsForPeakMargin(peakMarginModel.StrategyId, peakMarginModel.OrderStatus, actualFromDate, actualToDate);
			return Ok(models);
		}




	}
}
