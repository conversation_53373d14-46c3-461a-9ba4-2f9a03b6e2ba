﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using System.Linq;
using AutoMapper;
using System.Collections;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using System;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	[Authorize]
	public class ClientsController : AlphaBaseController
	{
		private readonly IClientService _clientSvc;
		private readonly ICsvParserService _csvParserService;

		private readonly IFileStorageService _fileStorageService;

		private readonly IMapper _mapper;
		public ClientsController(IClientService clientService, IMapper mapper, ICsvParserService csvParserService, IFileStorageService fileStorageService, IHttpContextAccessor contextAccessor) : base(contextAccessor)
		{
			_clientSvc = clientService;
			_mapper = mapper;
			_csvParserService = csvParserService;
			_fileStorageService = fileStorageService;
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> ClientsWithMoreThanOneActivePortfolios()
		{

			return Ok(await _clientSvc.GetClientsWithMoreThanOneActivePortfolios());
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllActiveClients()
		{

			return Ok(await _clientSvc.GetAllActiveClients());
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAll()
		{
			try
			{
				var userRoles = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
				var userId = User.Claims.FirstOrDefault(c => c.Type == "sub");
				if ((userRoles.Length == 1 && (userRoles[0] == "FundManager" || userRoles[0] == "ResearchAnalyst")) || (userRoles.Length == 2 && userRoles.Contains("FundManager") && userRoles.Contains("ResearchAnalyst")))
				{
					return Ok(await _clientSvc.GetClientsBasedOnUserRole(userRoles, userId.Value));
				}
				if (role.Contains("AlphaAccountsSubscriber"))
				{
					var client = await _clientSvc.GetClientByUserId(base.userId);

					return Ok(new List<Client> { client });
				}
				return Ok(await _clientSvc.GetClients());
			}
			catch (Exception ex)
			{
				return BadRequest(new { message = $"Failed to Fetch. Reason: {ex.Message}", status = false });
			}

		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> PaginateClients(string? query, int skip = 0)
		{
			try
			{
				var userRoles = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
				var userId = User.Claims.FirstOrDefault(c => c.Type == "sub");
				if ((userRoles.Length == 1 && (userRoles[0] == "FundManager" || userRoles[0] == "ResearchAnalyst")) || (userRoles.Length == 2 && userRoles.Contains("FundManager") && userRoles.Contains("ResearchAnalyst")))
				{
					return Ok(await _clientSvc.GetClientsBasedOnUserRole(userRoles, userId.Value, skip));
				}
				if (role.Contains("AlphaAccountsSubscriber"))
				{
					var client = await _clientSvc.GetClientByUserId(base.userId);

					return Ok(new List<Client> { client });
				}
				var filteredClients = await _clientSvc.GetClientsFiltered(query, skip);
				return Ok(filteredClients);
			}
			catch (Exception e)
			{
				return BadRequest(new { message = $"Failed to fetch. {e.Message}", status = false });
			}
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> SearchClients(string name)
		{
			try
			{
				if (string.IsNullOrEmpty(name))
					return BadRequest("Atleast one search parameter (name, PAN) is required.");

				var clientsByName = await _clientSvc.GetClientsBulkSearch(name);
				return Ok(clientsByName);
			}
			catch (Exception ex)
			{
				return BadRequest(new { message = $"Failed to Fetch. Reason: {ex.Message}", status = false });
			}
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetClients()
		{
			return Ok(await _clientSvc.GetClients(20));
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]/{searchString}")]
		public async Task<IActionResult> GetClientsBySearchText(string searchString)
		{
			return Ok(await _clientSvc.GetClients(searchString));
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]/{name}")]
		public async Task<IActionResult> GetByName(string name)
		{
			if (string.IsNullOrEmpty(name))
				return BadRequest("Name parameter is required.");

			return Ok(await _clientSvc.GetClientsByName(name));
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{id}")]
		public async Task<IActionResult> GetById(string id)
		{
			if (string.IsNullOrEmpty(id))
				return BadRequest("Id parameter is required.");


			if (role.Contains("AlphaAlphaAccountsSubscriber"))
			{
				return Ok(await _clientSvc.GetClientByUserId(base.userId));
			}
			return Ok(await _clientSvc.GetClientById(id));
		}

		[Authorize(Policy = "CommonRolePolicy")]

		[HttpGet("[action]/{pan}")]
		public async Task<IActionResult> GetByPan(string pan)
		{
			if (string.IsNullOrEmpty(pan))
				return BadRequest("PAN parameter is required.");

			return Ok(await _clientSvc.GetClientByPan(pan));
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,Operations")]

		[HttpGet("[action]/clientId")]
		public async Task<IActionResult> GetClientDetailsById(string clientId)
		{

			try
			{
				var clientDetails = await _clientSvc.GetClientFullDetailsById(clientId);
				return Ok(new { data = clientDetails, requestedAt = DateTime.Now });
			}
			catch (Exception ex)
			{

				return StatusCode(500, new { message = $"Failed to Update Client {ex.Message}", status = false });
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> New([FromForm] IFormFile agreement, [FromForm] CreateClient newClient)
		{
			//Upload the agreement file into blob storage
			//File should be less or equal to 5MB
			//Agreement is optional
			if (agreement != null && agreement.Length > 5 * Math.Pow(10, 6))
			{
				return BadRequest(new JsonResult(new { status = false, message = "Attatchment Size should be less than 5MB" }));

			}

			return Ok(await _clientSvc.AddClient(newClient, agreement, orgId, tenantName, email));
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,Operations")]
		[HttpPut("[action]/{clientId}")]
		public async Task<IActionResult> UpdateClient(string clientId, EditClient editClient)
		{
			try
			{
				await _clientSvc.UpdateClient(clientId, editClient);
				return StatusCode(201, new { message = $"Sucessfully Updated Client", status = true });
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { message = $"Failed to Update Client {ex.Message}", status = false });
			}
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> BulkCreation(IFormFile file)
		{

			if (file.Length == 0)
			{
				return BadRequest(new { message = "Upload a valid file", status = false });
			}

			try
			{
				//If parsing failed throw error to the client
				var stream = file.OpenReadStream();
				var parseFile = _csvParserService.ParseCsvForClientBulkCreation(stream);

				//If parsing is successful upload the file to blob container
				var filePath = await _fileStorageService.UploadClientBulkCreationFile(file);

				await _clientSvc.CreateBulkClientsFromFile(parseFile);

				return StatusCode(201, new { message = "Succesfully Created Clients", status = true });
			}
			catch (Exception ex)
			{
				return BadRequest(new { message = $"Failed to Create Clients in Bulk. Reason: {ex.Message}", status = false });
			}


		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,Operations")]
		[HttpPut("[action]")]
		public async Task<IActionResult> Update(string id, [FromBody] Client existingClient)
		{
			if (string.IsNullOrEmpty(id))
				return BadRequest("Id parameter is required.");

			var thisClient = await _clientSvc.GetClientById(id);
			if (thisClient == null)
				return NotFound("Client does not exist in Alpha Portfolio.");

			var clientWithThisPan = await _clientSvc.GetClientByPan(existingClient.Pan);
			if (clientWithThisPan != null && clientWithThisPan.Phone != existingClient.Phone
				&& clientWithThisPan.FirstName != existingClient.FirstName && clientWithThisPan.LastName == existingClient.LastName)
				return BadRequest("Client with PAN already exists");

			existingClient.Id = id;
			await _clientSvc.UpdateClient(existingClient);
			return Ok();
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{clientId}")]
		public async Task<IActionResult> GetAllBankAccounts(string clientId)
		{
			try
			{
				if (role.Contains("AlphaAccountsSubscriber"))
				{
					var client = await _clientSvc.GetClientByUserId(base.userId);
					clientId = client.Id;
				}
				var banks = await _clientSvc.GetClientBanks(clientId);
				return Ok(banks);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}

		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]/{code}")]
		public async Task<IActionResult> GetByCode(string code)
		{
			if (string.IsNullOrEmpty(code))
				return BadRequest("Code parameter is required.");

			return Ok(await _clientSvc.GetClientByCode(code));
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]/{accountNumber}")]
		public async Task<IActionResult> GetBankByAccountNo(string accountNumber)
		{
			if (string.IsNullOrEmpty(accountNumber))
				return BadRequest("Account Number parameter is required.");

			return Ok(await _clientSvc.GetBankByAccountNo(accountNumber));
		}

		[Authorize(Roles = "AlphaAccountsSubscriber")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetClientIdOfUser()
		{
			try
			{
				return Ok(await _clientSvc.GetClientByUserId(userId));
			}
			catch (Exception ex)
			{
				return BadRequest(new { message = $"Failed to Fetch. {ex.Message}", status = false });
			}

		}

		[HttpPost("[action]")]
		public async Task<IActionResult> CreateNewClient([FromBody] CreateNewClient createClient)
		{
			
			try
			{
				await _clientSvc.AddNewClient(createClient, orgId, tenantName, email);
				return StatusCode(201, new { message = "Succesfully Created Client", status = false });
			}
			catch (Exception ex)
			{

				return StatusCode(500, new { message = $"Failed to Create Client {ex.Message}", status = false });
			}
		}

	}
}
