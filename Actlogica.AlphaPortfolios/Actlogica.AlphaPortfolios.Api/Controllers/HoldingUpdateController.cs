using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.ModelHoldingManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioHoldingManagement;
using CsvHelper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Documents.SystemFunctions;
using System;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [Route("[controller]")]
    [Authorize(Policy = "AlphaEssentialsPolicy"
    )]
    [ApiController]

    public class HoldingUpdateController : ControllerBase
    {

        private readonly IFileStorageService _fileStorageService;
        private readonly IPortfolioHoldingUpdateService _portfolioHoldingUpdateService;

        private readonly IModelHoldingUpdateService _modelHoldingUpdateService;

        private readonly ICsvParserService _csvParserService;
        public HoldingUpdateController(IFileStorageService fileStorageService, IPortfolioHoldingUpdateService portfolioHoldingUpdateService, IModelHoldingUpdateService modelHoldingUpdateService, ICsvParserService csvParserService)
        {

            _fileStorageService = fileStorageService;
            _portfolioHoldingUpdateService = portfolioHoldingUpdateService;
            _csvParserService = csvParserService;
            _modelHoldingUpdateService = modelHoldingUpdateService;
        }


        [HttpPost("[action]")]
        public async Task<IActionResult> UploadPortfolioHoldingUpdateFile(IFormFile file, string faName)
        {

            if (file.Length == 0)
            {
                return BadRequest(new { message = "Upload a valid file", status = false });
            }

            if (faName == null)
            {

                return UnprocessableEntity(new { message = "FaName is required", status = false });
            }

            try
            {
                //If parsing failed throw error to the client
                var stream = file.OpenReadStream();
                var parseFile = _csvParserService.ParseCsvForPortfolioHoldingUpdate(stream);

                //If parsing is successful upload the file to blob container
                var filePath = await _fileStorageService.UploadPortfolioHoldingFile(file);

                //Update the portfolioHoldingUpdate table with the fileId
                await _portfolioHoldingUpdateService.AddNewPortfolioHoldingUpdate(User.Identity.Name, filePath, faName, parseFile);
            }
            catch (HeaderValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR : Failed to Parse the CSV, due to invalid headers: {e.InvalidHeaders}. Please Upload with Appropriate Headers", status = false });

            }
            catch (FieldValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR: Failed to Parse the Csv, due to invalid field : {e.Field}. Please Upload Appropriate Data", status = false });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"ERROR: Failed to Update the Portfolio, {e.Message}", status = false });
            }

            return Ok(new { message = "Uploaded the file", status = true });

        }



        [HttpPost("[action]")]
        public async Task<IActionResult> UploadModelHoldingUpdateFile(IFormFile file, string faName)
        {

            if (file.Length == 0)
            {
                return BadRequest("Upload a valid file");
            }

            if (faName == null)
            {

                return UnprocessableEntity("FaName is required");
            }

            try
            {
                var stream = file.OpenReadStream();
                var parseFile = _csvParserService.ParseCsvForModelHoldingUpdate(stream);

                //If parsing is successful upload the file to blob container
                var filePath = await _fileStorageService.UploadPortfolioHoldingFile(file);

                //Update the portfolioHoldingUpdate table with the fileId
                await _modelHoldingUpdateService.AddNewModelHoldingUpdate(User.Identity.Name, filePath, faName, parseFile);
            }
            catch (HeaderValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR : Failed to Parse the CSV, due to invalid headers: {e.InvalidHeaders}. Please Upload with Appropriate Headers", status = false });

            }
            catch (FieldValidationException e)
            {

                return UnprocessableEntity(new { message = $"ERROR: Failed to Parse the Csv, due to invalid field : {e.Field}. Please Upload Appropriate Data", status = false });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"ERROR: Failed to Update the Portfolio, {e.Message}", status = false });
            }

            return Ok(new { message = "Uploaded the file", status = true });
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetPortfolioHoldingUpdatesByFaName(string faName)
        {

            try
            {
                return Ok(await _portfolioHoldingUpdateService.GetPortoflioHoldingUpdateByFaName(faName));
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "Failed", status = false });
            }

        }


        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllHoldingUpdates()
        {

            try
            {
                return Ok(await _portfolioHoldingUpdateService.GetAllPortfolioHoldingUpdates());
            }
            catch (Exception)
            {

                return StatusCode(500, new { message = "Failed", status = false });
            }

        }




    }



}