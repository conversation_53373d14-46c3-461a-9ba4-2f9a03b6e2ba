using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.Api.Utilities;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.DistributionCenter;
using Actlogica.AlphaPortfolios.ApiContracts.Fees;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage.PerformanceEngine;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.DistributionCenter;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.PerformanceEngine.Portfolio;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class PortfolioController : AlphaBaseController
	{
		private readonly IPortfolioService _portfolioService;
		private readonly IStrategyModelRepository _strategyModelRepository;
		private readonly PortfolioAnalyticsStorageRepository _portfolioAnalyticsService;
		private readonly PortfolioAumStorageRepository _portfolioAumService;
		private readonly IMapper _mapper;
		private readonly IClientOrderManagementService _clientOrderSvc;

		private readonly IFileStorageService _fileStorageService;

		private readonly ICsvParserService _csvParserService;
		private Data.Entity.Db.AlphaPortfolioDbContext _dbContext;
		private readonly IOptions<ConnectionStrings> _connStrConfigs;
		private readonly IOptions<PerformanceEngineConfigs> _perfEngineConfig;

		private readonly IGeneralSettingService _generalSettingService;

		private readonly IClientService _clientService;
		private readonly IDistributionCenterService _distributionCenterService;

		public PortfolioController(IPortfolioService portfolioService, IMapper mapper,
		Data.Entity.Db.AlphaPortfolioDbContext dbContext,
			 IClientOrderManagementService clientOrderSvc
		 	, IGeneralSettingService generalSettingService,
			IClientsRepository clientRepo,
			IStrategyModelRepository strategyModelRepository,
			IFileStorageService fileStorageService,
			ICsvParserService csvParserService,
			IOptions<ConnectionStrings> connStrConfigs,
			 IOptions<PerformanceEngineConfigs> perfEngineConfig
			, IHttpContextAccessor contextAccessor,
			IClientService clientService,
			IDistributionCenterService distributionCenterService
			) : base(contextAccessor)
		{
			var settings = Task.Run(() => generalSettingService.GetAll()).Result;
			if (settings == null)
				throw new InvalidOperationException("Storage key not configured for tenant");

			var tenantClaim = settings.FirstOrDefault(s => s.Key == "TenantName");
			var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
			var tenantNameForQueue = tenantValue == "testing" ? "preprod" : tenantValue == "uat" ? "preprod" : tenantValue;
			var storageAccountKey = settings.FirstOrDefault(s => s.Key == "StorageAccountKey");
			_portfolioService = portfolioService;
			_portfolioAnalyticsService = new PortfolioAnalyticsStorageRepository(mapper, tenantValue, storageAccountKey.Value);
			_portfolioAumService = new PortfolioAumStorageRepository(mapper, tenantValue, storageAccountKey.Value);
			_mapper = mapper;
			_dbContext = dbContext;
			_clientOrderSvc = clientOrderSvc;
			_strategyModelRepository = strategyModelRepository;
			_fileStorageService = fileStorageService;
			_csvParserService = csvParserService;
			_connStrConfigs = connStrConfigs;
			_perfEngineConfig = perfEngineConfig;
			_generalSettingService = generalSettingService;
			_clientService = clientService;
			_distributionCenterService = distributionCenterService;
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{clientId}")]
		public async Task<IActionResult> GetByClientId(string clientId)
		{
			try
			{
				var userRoles = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
				var userId = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;

				if 
				(
					(userRoles.Length == 1 && 
					(userRoles[0] == "FundManager" || userRoles[0] == "ResearchAnalyst")) || 
					(userRoles.Length == 2 && userRoles.Contains("FundManager") 
					&& userRoles.Contains("ResearchAnalyst"))
				)
				{
					return Ok(await _portfolioService.GetPortfoliosOfClientBasedUserRoles(clientId, userId));
				}

				else if (role.Contains("AlphaDistributorRelationshipManager") || role.Contains ("AlphaDistributorAdmin")) //OR DistributorRM
				{
                	var isInternal = role.Contains("AlphaDistributorRelationshipManager");

					var rmPortfolios = await _portfolioService.GetPortfoliosOfClientBasedRM(clientId, userId, isInternal);
				}
				
				else if (role.Contains("AlphaAccountsSubscriber"))
				{
					var client = await _clientService.GetClientByUserId(base.userId);
					clientId = client.Id;
				}

				var portfolioSummary = await _portfolioService.GetPortfoliosOfClient(clientId);
				return Ok(portfolioSummary);
			}
			catch (Exception ex)
			{
				return BadRequest($"Failed to Fetch Portfolios for the client {ex.Message}");
			}
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{clientId}")]
		public async Task<IActionResult> GetAllActivePortfoliosByClientId(string clientId)
		{
			try
			{
				if (role.Contains("AlphaAccountsSubscriber"))
				{
					var client = await _clientService.GetClientByUserId(base.userId);
					clientId = client.Id;
				}
				var portfolios = await _portfolioService.GetAllActivePortfoliosByClientId(clientId);
				return Ok(portfolios);

			}
			catch (Exception)
			{
				return BadRequest("Failed to Fetch Portfolios");
			}
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{clientId}")]
		public async Task<IActionResult> GetPortfoliosForSTPDestination(string clientId)
		{
			try
			{
				if (role.Contains("AlphaAccountsSubscriber"))
				{
					var client = await _clientService.GetClientByUserId(base.userId);
					clientId = client.Id;
				}
				var portfolios = await _portfolioService.GetPortfoliosForSTPDestination(clientId);
				return Ok(portfolios);

			}
			catch (Exception)
			{
				return BadRequest("Failed to Fetch Portfolios");
			}
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> GetByModelId(string modelId)
		{
			var portfolioSummary = await _portfolioService.GetPortfoliosInModel(modelId);
			return Ok(portfolioSummary);
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> GetByModelIds([FromBody] string[] modelIds)
		{
			var portfolioSummary = await _portfolioService.GetPortfoliosInModel(modelIds);
			return Ok(portfolioSummary);
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> GetPortfoliosForPeakMargin([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate, [FromBody] PeakMarginPortfolio peakMarginPortfolio)
		{
			try
			{
				if (!fromDate.HasValue || !toDate.HasValue)
				{
					return BadRequest("Provide from and to date");
				}

				if (fromDate > toDate)
				{
					return BadRequest("toDate Should Be less than FromDate");
				}

				var actualFromDate = fromDate.Value;
				var actualToDate = toDate.Value;
				var portfolioSummary = await _portfolioService.GetPortfoliosInModelForPeakMargin(peakMarginPortfolio.ModelId, peakMarginPortfolio.OrderStatus, actualFromDate, actualToDate);
				return Ok(portfolioSummary);
			}
			catch (Exception e)
			{

				return BadRequest("Failed to Get Portfolios");
			}
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{clientStrategyCode}")]
		public async Task<IActionResult> ClientStrategyCodeExists(string clientStrategyCode)
		{
			var portfolioSummary = await _portfolioService.ClientStrategyCodeExists(clientStrategyCode);
			return Ok(portfolioSummary);
		}


		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{custodianPortfolioCode}")]
		public async Task<IActionResult> CustodianPortfolioCodeExists(string custodianPortfolioCode)
		{
			var custodianPortfolioCodeExists = await _portfolioService.CustodianPortfolioCodeExists(custodianPortfolioCode);
			return Ok(custodianPortfolioCodeExists);
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{id}")]
		public async Task<IActionResult> GetById(string id)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);

				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(id, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var portfolioSummary = await _portfolioService.GetPortfolioById(id);
			portfolioSummary.CashPosition = await _portfolioService.GetPortfolioCashPosition(id);
			return Ok(portfolioSummary);
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetPortfolioDetailsById(string portfolioId)
		{
			try
			{
				var portfolioDetails = await _portfolioService.GetFullPortfolioDetailsById(portfolioId);
				return Ok(new { data = portfolioDetails, requestedAt = DateTime.Now });
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { message = $"Failed to Fetch Portfolio {ex.Message}", status = false });
			}
		}

		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetHoldingsByPortfolioId(string portfolioId)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var holdingSummary = await _portfolioService.GetInvestmentHoldingsByPortfolioId(portfolioId);
			return Ok(holdingSummary);
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetCapitalRegisterByPortfolioId(string portfolioId)
		{

			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var capitalRegister = await _portfolioService.GetPortfolioRegister(portfolioId);
			return Ok(capitalRegister);
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,Operations,AlphaAccountsSubscriber")]
		[HttpPost("[action]")]
		public async Task<IActionResult> Create([FromBody] CreatePortfolio portfolio)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);
				//Can Only Create Porfolio for their Clients
				if (portfolio.Portfolio.ClientId! == client.Id)
				{
					return BadRequest(new { message = $"Failed to Create Portfolios. Reason: Invalid Client Id", status = false });
				}

			}

			try
			{
				portfolio.PortfolioFeeTemplateBody.PerformanceFeeFrequency = PerformanceFeeFrequency.HalfYearly.ToString();  //to be fixed
				var portfolioComponent = _mapper.Map<Portfolio>(portfolio.Portfolio);
				var portfolioNomineeDetail = _mapper.Map<IEnumerable<PortfolioNomineeDetails>>(portfolio.PortfolioNomineeDetailsBody);
				var portfolioPreference = _mapper.Map<PortfolioPreference>(portfolio.PortfolioPreference);
				var portfolioRmDetails = _mapper.Map<IEnumerable<PortfolioRMDetail>>(portfolio.PortfolioRMDetail);
				var portfolioTransactionsPrefernce = _mapper.Map<PortfolioTransactionsPrefernce>(portfolio.PortfolioTransactionsPreference);
				var clientCustodian = _mapper.Map<ClientCustodian>(portfolio.ClientCustodian);
				var clientBank = _mapper.Map<ClientBank>(portfolio.ClientBankBody);
				var broker = portfolio.Broker != null ? _mapper.Map<ClientBroker>(portfolio.Broker) : null;
				var portfolioFeeTemplate = _mapper.Map<PortfolioFeeTemplate>(portfolio.PortfolioFeeTemplateBody);
				var license = await _generalSettingService.GetByKey("ARNNumber");
				var sharingConfiguration = _mapper.Map<PortfolioDistributorSharingConfigurations>(portfolio.PortfolioDistributorSharingCreation);
				var portfolioForeignBankDetails= _mapper.Map<PortfolioForeignBankDetails>(portfolio.PortfolioForeignBankCreation);
				
				//If the portfolio to be assigned to Distributor Admin 
				var portfolioEntity = await _portfolioService.Create(portfolioComponent, portfolioNomineeDetail, portfolioPreference, portfolioRmDetails, portfolioTransactionsPrefernce, clientCustodian, clientBank, broker, portfolioFeeTemplate, portfolio.CapitalToDeploy, portfolio.Orders, license.Value, sharingConfiguration, portfolioForeignBankDetails);
				
				if(portfolio.PortfolioDistributorSharingCreation != null)
				{
					await _distributionCenterService.AssignPortfolioToDistributor(portfolio.DistributorMasterId, _mapper.Map<PortfolioDistributorSharingConfigurations>(portfolio.PortfolioDistributorSharingCreation), portfolioEntity.Id, portfolio.FromDate);
				}

				return StatusCode(201, new { message = "Succesfully Created Portfolios", status = false });
			}
			catch (Exception ex)
			{

				return StatusCode(500, new { message = $"Failed to Create Portfolio {ex.Message}", status = false });
			}
		}

		[HttpPatch("[action]/{portfolioId}")]
		public async Task<IActionResult> UpdatePortfolio(string portfolioId, [FromBody] EditPortfolio portfolio)
		{
			try
			{
				await _portfolioService.UpdatePortfolio(portfolioId, portfolio);
				return StatusCode(201, new { message = $"Sucessfully Updated Portfolio", status = true });
			}
			catch (Exception ex)
			{
				return StatusCode(500, new { message = $"Failed to Update Portfolio {ex.Message}", status = false });
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,Operations")]
		[HttpPost("[action]")]
		public async Task<IActionResult> CreatePortfolioInBulk(IFormFile file)
		{
			if (file.Length == 0)
			{
				return BadRequest(new { message = "Upload a valid file", status = false });
			}

			try
			{
				//If parsing failed throw error to the client
				var stream = file.OpenReadStream();
				var parseFile = _csvParserService.ParseCsvForBulkPortfolioCreation(stream);

				//If parsing is successful upload the file to blob container
				var filePath = await _fileStorageService.UploadClientBulkCreationFile(file);

				await _portfolioService.CreatePortfolioInBulkFromFile(parseFile);

				return StatusCode(201, new { message = "Created Portfolio Successfully", status = true });
			}
			catch (Exception ex)
			{
				return BadRequest(new { message = $"Failed to Create Portfolios in Bulk. Reason: {ex.Message}", status = false });
			}

		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,Operations,CIO,FundManager,ResearchAnalyst")]
		[HttpPost("[action]")]
		public async Task<IActionResult> AdditionalCapitalDeployment([FromBody] AdditionalCapitalDeploymentModel additionalDeploymentModel)
		{
			var portfolio = await _portfolioService.GetPortfolioById(additionalDeploymentModel.PortfolioId);

			if (additionalDeploymentModel.ShouldCreateCapitalRegisterEntry)
				await _portfolioService.AddCapital(portfolio.Id, additionalDeploymentModel.NewCapital, false);

			await _clientOrderSvc.CreateClientOrders(additionalDeploymentModel.Orders);

			return Ok(portfolio);
		}

		//[HttpPost("[action]/{portfolioId}")]
		//public async Task<IActionResult> AddStockTransactions(string portfolioId, string clientId,
		//	[FromBody] IEnumerable<InvestmentTransaction> transactionsInPortfolio)
		//{
		//	var errors = await _portfolioService.AddTransactionsToInvestment(clientId, portfolioId, transactionsInPortfolio);
		//	return Ok(errors);
		//}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetCashPosition(string portfolioId)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var ptfCashPosition = await _portfolioService.GetPortfolioCashPosition(portfolioId);
			return Ok(ptfCashPosition);
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetCapitalRegister(string portfolioId, int skip = 0, int take = 10)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{

				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var capitalRegisterTxns = await _portfolioService.GetCapitalRegisterByPortfolioId(portfolioId, skip, take);
			return Ok(capitalRegisterTxns);
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetCashLedger(string portfolioId, int skip = 0, int take = 10)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{

				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var cashLedgers = await _portfolioService.GetCashLedgerTransactionsInPortfolio(portfolioId, skip, take);
			return Ok(cashLedgers);
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetTransactions(string portfolioId, int skip = 0, int take = 10)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{

				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var transactions = await _portfolioService.GetTransactionsInPortfolio(portfolioId, skip, take);
			return Ok(transactions);
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetAllocation(string portfolioId, string aggregationType)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{

				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}

			var transactions = await _portfolioService.GetAssetClassAllocation(portfolioId);
			return Ok(transactions);
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpPost("[action]/{portfolioId}")]
		public async Task<IActionResult> CalculatePerformanceForGivenDate(string portfolioId, DateTime asAtDate)
		{
			var tenantClaim = User.Claims.FirstOrDefault(c => c.Type == "tenant");
			var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
			await PortfolioPerformanceEngine.TriggerOnQueue(tenantValue, portfolioId, asAtDate, _perfEngineConfig.Value.PortfolioPerfFnSecretKey
				, _perfEngineConfig.Value.PortfolioPerfRequestQueue, _connStrConfigs.Value.AlphaPServiceBusConnectionString);

			return Ok();
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpPost("[action]/{portfolioId}")]
		public async Task<IActionResult> GetHoldingAsAtDate(string portfolioId, string requestedDate)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{

				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var asAtDate = DateTime.Today.AddDays(-1);
			try
			{
				asAtDate = DateTime.Parse(requestedDate);
			}
			catch (Exception ex)
			{
				return BadRequest("Invalid request date value. Please note the RequestedDate should be in yyyy-mm-dd format.");
			}
			var analyticsHistory = await _portfolioAnalyticsService.GetAnalyticsForTheDay(portfolioId, asAtDate);

			if (!analyticsHistory.Any())
			{
				var tenantClaim = User.Claims.FirstOrDefault(c => c.Type == "tenant");
				var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
				var tenantNameForQueue = tenantValue == "testing" ? "preprod" : tenantValue == "uat" ? "preprod" : tenantValue;
				await PortfolioPerformanceEngine.CalculateHistory(_dbContext, tenantValue, _mapper, portfolioId, asAtDate);
				analyticsHistory = await _portfolioAnalyticsService.GetAnalyticsForTheDay(portfolioId, asAtDate);
				analyticsHistory.ToList().ForEach(a => a.Xirr = Math.Round(a.Xirr, 2));
			}

			return Ok(analyticsHistory.Where(a => a.MarketValue > 0.00));
		}

		[Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetAumHistory(string portfolioId)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{

				var client = await _clientService.GetClientByUserId(base.userId);
				var exist = await _portfolioService.CheckPortfolioIsInGivenClient(portfolioId, client.Id);
				if (!exist)
				{
					return BadRequest(new { message = "Invalid Id", status = false });
				}

			}
			var aumHistory = await _portfolioAumService.GetAumHistory(portfolioId);

			return Ok(aumHistory.OrderBy(aum => aum.AsAtDate));
		}

		[Authorize(Policy = "CommonRolePolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetInvestmentInAllPortfolios(string isin, string modelId)
		{
			try
			{
				var searchResults = await _portfolioService.GetInvestmentDetailsInModel(isin, modelId);
				return Ok(searchResults);
			}
			catch (InvalidOperationException ex)
			{
				return BadRequest(ex.Message);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}

		}

		[HttpGet("[action]")]
		public async Task<IActionResult> PortfoliosWithDmIdNull(string clientId)
		{
			try
			{
				var searchResults = await _portfolioService.PortfoliosWithDmIdNull(clientId);
				return Ok(searchResults);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}

		}

	}
}
