﻿using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class TransactionController : ControllerBase
	{
		private readonly IPortfolioService _portfolioService;

		public TransactionController(IPortfolioService portfolioService)
		{

		}
	}
}
