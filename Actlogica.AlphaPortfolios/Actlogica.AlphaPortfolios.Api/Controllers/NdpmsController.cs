using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Ndpms;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;


namespace Actlogica.AlphaPortfolios.Api.Controllers
{

    [Route("[controller]")]
    [AllowAnonymous]
    [ApiController]

    public class NdpmsController : ControllerBase
    {
        private readonly IMapper _mapper;
        private readonly INdpmsService _ndpmsService;
        private readonly IGeneralSettingService _generalSettingService;


        public NdpmsController(IMapper mapper, INdpmsService ndpmsService, IGeneralSettingService generalSettingService)
        {
            _mapper = mapper;
            _ndpmsService = ndpmsService;
            _generalSettingService = generalSettingService;
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> TriggerMail([FromBody] List<string> clientOrderEntries)
        {
            try
            {
                await _ndpmsService.SendMailWithLink(clientOrderEntries);
                return Ok(new { message = "Successful" });
            }

            catch (Exception)
            {
                return BadRequest(new { message = "Failed" });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> VerifyOTP(string token, string otp)
        {
            try
            {
                var result = await _ndpmsService.VerifyOtp(token, otp);
                return Ok(result);
            }

            catch (Exception e)
            {
                return BadRequest(new { message = $"Failed " });
            }

        }

        [HttpPost("[action]")]
        public async Task<IActionResult> VerifyToken(string token)
        {
            try
            {
                var result = await _ndpmsService.VerifyToken(token);
                return Ok(new { message = "Successful" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = e.Message });
            }

        }

        [HttpPut("[action]")]
        public async Task<IActionResult> ApproveOrder(List<string> clientOrderId)
        {
            try
            {
                await _ndpmsService.UpdateStatus(clientOrderId);
                return Ok(new { message = "Orders Moved To Draft" });
            }

            catch (Exception e)
            {
                return BadRequest(new { message = e.Message });
            }

        }

        [HttpGet("[action]/{key}")]
        public async Task<IActionResult> GetByKey(string key)
        {
            var setting = await _generalSettingService.GetByKey(key);
            return Ok(setting);
        }

    }
}