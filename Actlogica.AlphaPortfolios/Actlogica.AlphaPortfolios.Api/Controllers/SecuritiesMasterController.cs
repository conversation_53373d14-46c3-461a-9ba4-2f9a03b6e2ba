﻿using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	[Authorize]
	public class SecuritiesMasterController : Controller
	{
		private readonly ISecurityMasterService _securityMasterService;
		private readonly ILogger<SecuritiesMasterController> _logger;
		private readonly IMapper _mapper;
		public SecuritiesMasterController(ISecurityMasterService securityMasterService,
			ILogger<SecuritiesMasterController> logger, IMapper mapper)
		{
			_securityMasterService = securityMasterService;
			_logger = logger;
			_mapper = mapper;
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetSecurityByIsin(string isin, string assetType, string exchange)
		{
			if (assetType == "Mutual Fund")
			{
				var mfSecurity = await _securityMasterService.GetMutualFundByIsin(isin);
				return Ok(_mapper.Map<ModelSecurity>(mfSecurity));
			}

			var stockSecurity = await _securityMasterService.GetStockDetailsByIsin(isin, exchange);
			return Ok(_mapper.Map<ModelSecurity>(stockSecurity));
		}
	}
}
