﻿using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	//[Authorize]
	public class OrderReconController : ControllerBase
  {
		private readonly ITradeOrderService _tradeOrderService;
		private readonly IPortfolioService _portfolioService;

		public OrderReconController(ITradeOrderService tradeOrderService, IPortfolioService portfolioService)
		{
			_tradeOrderService = tradeOrderService;
			_portfolioService = portfolioService;
		}

		[HttpPut("[action]/{portfolioId}")]
		public async Task<IActionResult> AutoReconcileTradeOrdersHeld(string portfolioId)
		{
			await _tradeOrderService.ReconcileTradeOrderHeldAmount(portfolioId, DateTime.Today, "AutoRelease");
			return Ok();
		}

		[HttpGet("[action]/{portfolioId}")]
		public async Task<IActionResult> GetCurrentCashPosition(string portfolioId)
		{
			var cashPosition = await _portfolioService.GetPortfolioCashPosition(portfolioId);
			return Ok(cashPosition);
		}
  }
}
