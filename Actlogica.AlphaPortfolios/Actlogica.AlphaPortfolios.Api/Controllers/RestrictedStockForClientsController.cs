﻿using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Actlogica.AlphaPortfolios.Api.Types;
using System;
using Actlogica.AlphaPortfolios.ApiContracts.RestrictedStockORGs;
using Microsoft.AspNetCore.Http;
using System.Linq;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [ApiController]
    [Route("[controller]")]
    [Authorize]
    public class RestrictedStockForClientsController : AlphaBaseController
    {
        private readonly IRestrictedStockForClientService _restrictedStockSvc;
        private readonly IClientService _clientService;
        public RestrictedStockForClientsController(IRestrictedStockForClientService restrictedStockService, IClientService clientService, IHttpContextAccessor contextAccessor) : base(contextAccessor)
        {
            _restrictedStockSvc = restrictedStockService;
            _clientService = clientService;
        }

        [Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
        [HttpGet("[action]/{clientId}")]
        public async Task<IActionResult> GetByClientId(string clientId)
        {
            if (role.Contains("AlphaAccountsSubscriber"))
            {
                var client = await _clientService.GetClientByUserId(base.userId);
                if (clientId != client.Id)
                {
                    return BadRequest(new { message = "Invalid Client Id" });
                }

            }
            var restrictedListOfClients = await _restrictedStockSvc.GetRestrictedClientForClient(clientId);
            return Ok(restrictedListOfClients);
        }

        [Authorize(Policy = "CommonRoleIncludingSubscriberPolicy")]
        [HttpGet("[action]")]
        public async Task<IActionResult> GetAll()
        {
            if (role.Contains("AlphaAccountsSubscriber"))
            {
                var client = await _clientService.GetClientByUserId(base.userId);
                var restrictedListOfClients = await _restrictedStockSvc.GetRestrictedClientForClient(client.Id);
                return Ok(restrictedListOfClients);
            }
            return Ok(await _restrictedStockSvc.GetRestrictedStockForClients());
        }

        [Authorize(Policy = "CommonRolePolicy")]
        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            if (string.IsNullOrEmpty(id))
                return BadRequest("Id parameter is required.");

            return Ok(await _restrictedStockSvc.GetRestrictedStockForClientById(id));
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,Operations,CIO,ResearchAnalyst,OperationManager,AlphaAccountsSubscriber")]
        [HttpPost("[action]")]
        public async Task<IActionResult> New(RestrictedStocksForClient newRestrictedStock)
        {
            if (role.Contains("AlphaAccountsSubscriber"))
            {
                var client = await _clientService.GetClientByUserId(base.userId);
                if (newRestrictedStock.ClientId! == client.Id)
                    return BadRequest(new { message = "Invalid Client Id", status = false });
            }
            newRestrictedStock.Status = RestrictedStockStatus.Active.ToString();
            try
            {
                var add = await _restrictedStockSvc.AddRestrictedStockForClient(newRestrictedStock);
                return Ok(add);
            }
            catch (Exception e)
            {
                return BadRequest(new { status = false, message = e.Message });
            }
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,Operations,CIO,ResearchAnalyst,OperationManager,AlphaAccountsSubscriber")]
        [HttpPut("[action]/{id}")]
        public async Task<IActionResult> Update([FromBody] UpdateRestrictedStocksForClient existingRestrictedStock, string id)
        {
            try
            {
                await _restrictedStockSvc.Edit(id, existingRestrictedStock.AlternativeSecurityIdentifier, existingRestrictedStock.Rationale, existingRestrictedStock.IsinAlternativeSecurity, existingRestrictedStock.ExchangeAlternativeSecurity);
                return Ok(new { status = true, message = "Updated Successfully" });
            }
            catch (Exception e)
            {
                return BadRequest(new { status = false, message = "Failed To Update" });
            }
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,PrincipleOfficer")]
        [HttpDelete("[action]/{restrictedStockId}")]
        public async Task<IActionResult> Delete(string restrictedStockId)
        {
            try
            {
                await _restrictedStockSvc.DeleteRestrictedStockForClient(restrictedStockId);
                return Ok(new { status = true, message = "Deleted Successfully" });
            }
            catch (Exception)
            {
                return BadRequest(new { status = false, message = "Delete" });
            }
        }

        [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,OperationManager,PrincipleOfficer")]
        [HttpPatch("[action]/{restrictedStockId}")]
        public async Task<IActionResult> ChangeStatus(string restrictedStockId, [FromBody] bool status)
        {
            try
            {
                await _restrictedStockSvc.ChangeStatus(restrictedStockId, status);
                return Ok();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }
    }
}
