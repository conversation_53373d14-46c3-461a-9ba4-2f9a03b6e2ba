﻿using Actlogica.AlphaPortfolios.Api.Middleware;
using Actlogica.AlphaPortfolios.ApiContracts.UserManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.UserManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.Api.Types.User;
using AutoMapper;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Microsoft.AspNetCore.Http;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class UserManagementController : AlphaBaseController
	{
		private readonly IUserService _userService;
		private readonly IActlogicaAppRoleService _roleService;
		private readonly UserClaimProvider _userClaims;
		private readonly IMapper _mapper;
		private readonly IClientService _clientService;

		public UserManagementController(IUserService userService, IActlogicaAppRoleService roleService, UserClaimProvider userClaims, IMapper mapper, IHttpContextAccessor contextAccessor,
			IClientService clientService
			) : base(contextAccessor)
		{
			_userService = userService;
			_roleService = roleService;
			_userClaims = userClaims;
			_mapper = mapper;
			_clientService = clientService;
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllUsersInTenant([FromQuery] int roleId)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				//Only Give RM to AlphaAccountsSubscriber
				roleId = 203;
			}

			var userList = (await _userService.GetUsersList(Int32.Parse(userId))).Where(u => u.UserRoles.Any(ur => ur.Role.AppId == (int)ActlogicaApp.AlphaP));
			if (roleId == 0)
			{
				return Ok(userList);
			}
			else
			{
				return Ok(userList.Where(user => user.UserRoles.Any(ur => ur.RoleId == roleId)));
			}
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllRoles()
		{
			var allAlphaRoles = await _roleService.GetRoleByAppId((int)ActlogicaApp.AlphaP);
			return Ok(allAlphaRoles);
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin")]
		[HttpPost("[action]")]
		public async Task<IActionResult> AddUser([FromBody] ActlogicaUser newUser)
		{
			var accessibleApps = new List<string>();
			accessibleApps.Add($"{ActlogicaApp.AlphaP}");
			newUser.AppName = accessibleApps;
			var orgIdClaim = User.Claims.FirstOrDefault(cl => cl.Type == "organisationId");
			var adminEmail = User.Claims.FirstOrDefault(cl => cl.Type == "email");
			var adminTenantName = User.Claims.FirstOrDefault(cl => cl.Type == "tenant");
			newUser.OrganisationId = Convert.ToInt16(orgIdClaim.Value);
			var createdUser = await _userService.AddNewUser(newUser, adminEmail.Value, adminTenantName.Value);
			return Ok(createdUser);
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin")]
		[HttpPatch("[action]/{userId}")]
		public async Task<IActionResult> EditUser([FromBody] EditActlogicaUser user, string userId)
		{
			var createdUser = await _userService.EditUser(userId, user.RoleId);
			return Ok(createdUser);
		}


		[HttpGet("[action]/{userName}")]
		public async Task<IActionResult> CheckUsernameAvailability(string userName)
		{
			try
			{
				var isAvailable = await _userService.CheckUsernameIsAvailable(userName);
				return Ok(new { message = isAvailable, status = true });
			}
			catch (Exception ex)
			{
				return BadRequest(new { message = ex.Message, status = false });
			}
		}


	}
}
