﻿using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	[Authorize]
	public class DirectEquityPricesController : Controller
	{
		private readonly ILogger<DirectEquityPricesController> _logger;
		private readonly IMapper _mapper;
		private readonly IDirectEquityPricesService _directEquityPriceSvc;

		public DirectEquityPricesController(IDirectEquityPricesService directEquityPricesSvc, IMapper mapper)
		{
			_directEquityPriceSvc = directEquityPricesSvc;
			_mapper = mapper;
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetPricesHistory(string symbol, string exchange)
		{
			var priceHistory = await _directEquityPriceSvc.GetPriceSinceInception(symbol, exchange);
			return Ok(priceHistory);
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetPriceOnDate(string symbol, string exchange, DateTime asOnDate)
		{
			var priceHistory = await _directEquityPriceSvc.GetPriceOnDate(symbol, exchange, asOnDate);
			return Ok(priceHistory);
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetPriceBetweenDates(string symbol, string exchange, DateTime fromDate, DateTime toDate)
		{
			var priceHistory = await _directEquityPriceSvc.GetPriceBetweenDates(symbol, exchange, fromDate, toDate);
			return Ok(priceHistory);
		}
	}
}
