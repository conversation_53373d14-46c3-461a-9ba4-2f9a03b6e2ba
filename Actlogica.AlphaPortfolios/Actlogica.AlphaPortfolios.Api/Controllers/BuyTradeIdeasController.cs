﻿using Actlogica.AlphaPortfolios.Api.Utilities;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.TradeIdeas;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.TradeIdeaManagement;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	[Authorize]
	public class BuyTradeIdeasController : AlphaBaseController
	{
		private readonly IBuyTradeIdeaService _buyTradeIdeaSvc;
		private readonly IFileStorageService _fileStorageService;
		private readonly IStrategyModelService _strategyModelService;
		private readonly IUriService _uriService;
		private readonly IMapper _mapper;
		private readonly IClientOrderManagementService _clientOrderSvc;

		public BuyTradeIdeasController(
						IBuyTradeIdeaService buyTradeIdeaSvc,
						IFileStorageService fileStorageService,
						IStrategyModelService strategyModelService,
						IUriService uriService,
						IClientOrderManagementService clientOrderSvc,
						IMapper mapper,
 						IHttpContextAccessor contextAccessor) : base(contextAccessor)
		{
			_buyTradeIdeaSvc = buyTradeIdeaSvc;
			_fileStorageService = fileStorageService;
			_strategyModelService = strategyModelService;
			_uriService = uriService;
			_clientOrderSvc = clientOrderSvc;
			_mapper = mapper;
			_buyTradeIdeaSvc.SetCreatedBy(base.userName);
			_clientOrderSvc.SetCreatedBy(base.userName);
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,CIO,PrincipleOfficer,FundManager,ResearchAnalyst")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAll()
		{
			var userRoles = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
			var userId = User.Claims.FirstOrDefault(c => c.Type == "sub").Value;
			return Ok(await _buyTradeIdeaSvc.GetAllBuyTradeIdeas(userRoles, userId));
		}



		[Authorize(Policy = "TradeIdeaAccessPolicy")]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllByStatus([FromQuery] PaginationFilter filter)
		{

			var route = Request.Path.Value;
			var validFilter = new PaginationFilter(filter.PageNumber, filter.PageSize, filter.OrderBy, filter.FilterBy, filter.FilterValue);
			var data = await _buyTradeIdeaSvc.GetAllBuyTradeIdeas(validFilter, role, userId);
			var pagedReponse = PaginationHelper.CreatePagedReponse(data.Items, validFilter, data.TotalItems, _uriService, route);
			return Ok(pagedReponse);
		}

		[HttpGet("[action]/{tradeIdeaId}")]
		public async Task<IActionResult> GetBuyTradeIdeaById(string tradeIdeaId)
		{
			var tradeIdea = await _buyTradeIdeaSvc.GetBuyTradeIdeaById(tradeIdeaId);
			return Ok(tradeIdea);
		}


		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,ResearchAnalyst")]
		[HttpPost("[action]")]
		public async Task<IActionResult> New([FromForm] IFormFile file, [FromForm] BuyTradeIdeaModel newTradeIdea)
		{
			
			try
			{
				if (newTradeIdea.Status.ToLower() != "draft" && newTradeIdea.Status.ToLower() != "senttofm")
				{

					return BadRequest(new JsonResult(new { status = false, message = "Status should be either draft or sent to fm" }));
				}

				if (file == null || file.Length == 0)
				{
					return BadRequest(new JsonResult(new { status = false, message = "Attatchment File is Required" }));
				}

				//File should be less or equal to 5MB
				if (file.Length > 5 * Math.Pow(10, 6))
				{
					return BadRequest(new JsonResult(new { status = false, message = "Attatchment Size should be less than 5MB" }));

				}

				if (string.IsNullOrEmpty(newTradeIdea.SecurityName))
					return BadRequest("Security Name parameter is required.");

				if (newTradeIdea.Change <= 0.00)
					return BadRequest("Invalid change % received. Change has to be greater than 0.");

				return Ok(await _buyTradeIdeaSvc.AddBuyTradeIdea(file, newTradeIdea, User.Identity.Name));
			}
			catch (Exception ex)
			{
				return BadRequest(new JsonResult(new { status = false, message = ex.Message }));

			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,FundManager")]
		[HttpPut("[action]/{buyTradeIdeaId}")]
		public async Task<IActionResult> ApproveIdea(string buyTradeIdeaId)
		{
			try
			{
				var idea = await _buyTradeIdeaSvc.ApproveIdea(buyTradeIdeaId);

				return Ok(idea);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,FundManager")]
		[HttpPut("[action]/{buyTradeIdeaId}")]
		public async Task<IActionResult> RejectIdea(string buyTradeIdeaId)
		{
			try
			{
				var idea = await _buyTradeIdeaSvc.RejectIdea(buyTradeIdeaId);

				return Ok(idea);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,FundManager")]
		[HttpPut("[action]/{buyTradeIdeaId}")]
		public async Task<IActionResult> AbandonIdea(string buyTradeIdeaId)
		{
			try
			{
				var idea = await _buyTradeIdeaSvc.AbandonIdea(buyTradeIdeaId);

				return Ok(idea);
			}
			catch (Exception ex)
			{
				var aa = ex.Message;
				return BadRequest("There was an unknown error processing your request. Please try again.");
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,DealerDeskTrader")]
		[HttpPut("[action]/{buyTradeIdeaId}")]
		public async Task<IActionResult> CreateOrdersForIdea(string buyTradeIdeaId)
		{
			try
			{
				var tradeIdea = await _buyTradeIdeaSvc.GetBuyTradeIdeaById(buyTradeIdeaId);
				var ordersForBuyTradeIdea = await _buyTradeIdeaSvc.PrepareOrdersForBuyIdea(tradeIdea);
				await _clientOrderSvc.CreateClientOrders(ordersForBuyTradeIdea.ToList());

				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,ResearchAnalyst")]
		[HttpPut("[action]/{id}")]
		public async Task<IActionResult> Update([FromForm] IFormFile file, [FromForm] BuyTradeIdeaModel existingTradeIdea, string id)
		{

			try
			{

				if (existingTradeIdea.Status.ToLower() != "draft" && existingTradeIdea.Status.ToLower() != "senttofm")
				{
					return BadRequest("Can Only update Trade Idea to draft and Send To Fm");
				}

				await _buyTradeIdeaSvc.UpdateBuyTradeIdea(file, existingTradeIdea, id, User.Identity.Name);
				return Ok(new JsonResult(new { status = true, message = "Successfully Updated" }));
			}
			catch (Exception e)
			{
				return StatusCode(400, new JsonResult(new { status = false, message = e.Message }));
			}
		}

		[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,ResearchAnalyst")]
		[HttpDelete("[action]/{TradeIdeaId}")]
		public async Task<IActionResult> Delete(string TradeIdeaId)
		{
			try
			{
				await _buyTradeIdeaSvc.DeleteBuyTradeIdea(TradeIdeaId);
				return Ok();
			}
			catch (Exception e)
			{
				return StatusCode(400, new JsonResult(new { status = false, message = e.Message }));
			}
		}

		[HttpGet("[action]/{modelId}")]
		public async Task<IActionResult> GetTradeIdeaModelSecuritybyModelId(string modelId)
		{
			var securitiesInModel = await _strategyModelService.GetBuyTradeIdeaModelSecurityId(modelId);
			return Ok(securitiesInModel.OrderByDescending(ord => ord.Weight));
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> CalculateTradeIdeaWeight(TradeIdeaCalculateWeight requestModel)
		{
			try
			{
				var securitiesInModel = await _buyTradeIdeaSvc.CalculateTradeIdeaWeight(requestModel);
				return Ok(securitiesInModel);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpGet("[action]/{fileName}")]
		public async Task<FileResult> DownloadFile(string fileName)
		{
			//Build the File Path.
			Stream streamData = await _fileStorageService.DownloadTradeFile(fileName);//Path.Combine(this.Environment.WebRootPath, "Files/") + fileName;

			//Read the File data into Byte Array.
			byte[] bytes = ReadFully(streamData);

			//Send the File to Download.
			return File(bytes, "application/octet-stream", fileName);
		}

		private byte[] ReadFully(Stream input)
		{
			byte[] buffer = new byte[16 * 1024];
			using (MemoryStream ms = new MemoryStream())
			{
				int read;
				while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
				{
					ms.Write(buffer, 0, read);
				}
				return ms.ToArray();
			}
		}
	}
}
