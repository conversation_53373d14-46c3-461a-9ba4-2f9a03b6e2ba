﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Documents.SystemFunctions;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize]
	public class StrategyController : AlphaBaseController
	{
		private readonly IStrategyService _strategySvc;
		private readonly IMasterDataService _masterDataService;

		private readonly IClientService _clientService;

		private readonly IPortfolioService _portfolioService;

		public StrategyController(IStrategyService strategySvc, IMasterDataService masterDataService, IClientService clientService, IPortfolioService portfolioService, IHttpContextAccessor contextAccessor
			) : base(contextAccessor)
		{
			_strategySvc = strategySvc;
			_masterDataService = masterDataService;
			_clientService = clientService;
			_portfolioService = portfolioService;
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetAll()
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);
				return Ok(await _strategySvc.GetStrategiesForClient(client.Id));

			}
			return Ok(await _strategySvc.GetAll());
		}

		[HttpGet("[action]/{clientId}")]
		public async Task<IActionResult> GetAllForClient([FromQuery] string custodianId, string clientId)
		{
			var userRole = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
			var userId = User.Claims.FirstOrDefault(c => c.Type == "sub");
			return Ok(await _strategySvc.GetAllAvailableForNewDeployment(clientId,custodianId, 1, 10, userRole, userId.Value));
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpGet("[action]/{id}")]
		public async Task<IActionResult> GetById(string id)
		{
			return Ok(await _strategySvc.GetStrategyById(id));
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpGet("[action]/{id}")]
		public async Task<IActionResult> GetByModelId(string id)
		{
			try
			{
				var strategy = await _strategySvc.GetStrategyByModelId(id);

				foreach (var strategyBroker in strategy.StrategyBrokers)
				{
					strategyBroker.Broker = await _masterDataService.GetBrokerById(strategyBroker.BrokerId);
				}

				//strategy.StrategyBrokers.ToList().ForEach(async sb => sb.Broker = await _masterDataService.GetBrokerById(sb.BrokerId));

				return Ok(strategy);
			}
			catch (InvalidOperationException invEx)
			{
				return NotFound(invEx.Message);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> New(Strategy newStrategy)
		{
			if (!ModelState.IsValid)
				return BadRequest(ModelState);

			object bank;
			if (!Enum.TryParse(typeof(BankAccountType), newStrategy.StrategyBank.BankAccountType.ToString(), out bank))
				return BadRequest("Invalid bank account type selected.");


			newStrategy.StrategyBrokers.ToList().ForEach(sb => sb.Broker = null);
			newStrategy.StrategyCustodians.ToList().ForEach(sb => sb.Custodian = null);

			try
			{
				var strategyCreated = await _strategySvc.Create(newStrategy);

				return Ok(strategyCreated);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPut("[action]/{id}")]
		public async Task<IActionResult> Update([FromBody] Strategy existingStrategy, string id)
		{
			if (string.IsNullOrEmpty(id))
				return BadRequest("Id parameter is required.");

			existingStrategy.Id = id;
			await _strategySvc.Edit(existingStrategy);
			return Ok();
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpDelete("[action]/{id}")]
		public async Task<IActionResult> Delete(string id)
		{
			if (string.IsNullOrEmpty(id))
				return BadRequest("Id parameter is required.");

			var thisStrategy = await _strategySvc.GetStrategyById(id);
			if (thisStrategy == null)
				return NotFound("Strategy does not exist in Alpha Portfolio.");

			await _strategySvc.Delete(id);
			return Ok();
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> GetAllStrategy()
		{
			var tenantRole = User.Claims.ToList().FindAll(c => c.Type == "role").Select(c => c.Value).ToArray();
			var tenantUserId = User.Claims.FirstOrDefault(c => c.Type == "sub");
			return Ok(await _strategySvc.GetStrategiesBasedOnRoles(tenantRole, tenantUserId.Value));
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPut("[action]/{id}")]
		public async Task<IActionResult> UpdateStrategy([FromBody] Strategy existingStrategy, string id)
		{
			if (string.IsNullOrEmpty(id))
				return BadRequest("Id parameter is required.");

			existingStrategy.Id = id;
			await _strategySvc.UpdateStrategy(existingStrategy);
			return Ok();
		}

		[HttpGet("[action]/{code}")]
		public async Task<IActionResult> HasStrategyByCodeExists(string code)
		{
			if (string.IsNullOrEmpty(code))
				return BadRequest("code parameter is required.");
			var hasStrategyByCodeExists = await _strategySvc.HasStrategyByCodeExists(code);
			return Ok(hasStrategyByCodeExists);
		}

		[HttpGet("[action]/{dpId}")]
		public async Task<IActionResult> HasGetDpIdByCodeExists(string dpId)
		{
			if (string.IsNullOrEmpty(dpId))
				return BadRequest("code parameter is required.");

			var hasGetDpIdByCodeExists = await _strategySvc.HasGetDpIdByCodeExists(dpId);
			return Ok(hasGetDpIdByCodeExists);
		}

		[Authorize(Policy = "TenantRolePolicy")]
		[HttpPost("[action]")]
		public async Task<IActionResult> GetStrategiesForPeakMargin([FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate, [FromBody] string[] status)
		{

			if (!fromDate.HasValue || !toDate.HasValue)
			{
				return BadRequest("Provide from and to date");
			}

			if (fromDate > toDate)
			{
				return BadRequest("toDate Should Be less than FromDate");
			}

			var actualFromDate = fromDate.Value;
			var actualToDate = toDate.Value;

			var strategies = await _strategySvc.GetStrategiesForPeakMargin(status, actualFromDate, actualToDate);
			return Ok(strategies);
		}
	}
}
