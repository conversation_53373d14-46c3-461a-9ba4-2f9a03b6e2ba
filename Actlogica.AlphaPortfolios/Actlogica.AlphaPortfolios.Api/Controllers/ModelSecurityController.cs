﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Strategy;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
  [Route("[controller]")]
  [ApiController]
  [Authorize(Policy = "TenantRolePolicy")]
  public class ModelSecurityController : ControllerBase
  {
    private readonly IStrategyModelService _strategyModelService;
    private readonly ISecurityMasterService _securityMasterService;
    private readonly IMapper _mapper;
    private readonly IDirectEquityPricesService _stockPricesSvc;
    private readonly IMutualFundPricesService _mfPricesSvc;

    public ModelSecurityController(IStrategyModelService strategyModelService, IMapper mapper,
      ISecurityMasterService securityMasterService, IDirectEquityPricesService stockPricesSvc, IMutualFundPricesService mfPricesSvc)
    {
      _mapper = mapper;
      _strategyModelService = strategyModelService;
      _securityMasterService = securityMasterService;
      _stockPricesSvc = stockPricesSvc;
      _mfPricesSvc = mfPricesSvc;
    }

    [HttpPost("[action]/{modelId}")]
    public async Task<IActionResult> AddSecuritesToModel([FromBody] IEnumerable<ModelSecurity> modelSecurities, string modelId)
    {
      await _strategyModelService.AddSecuritiesToModel(modelId, modelSecurities);

      return Ok();
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> SearchSecurity(string name, string assetType)
    {
      if (string.IsNullOrEmpty(name))
        return BadRequest("Search text required.");

      
      if (assetType == "Mutual Fund")
      {
        var mfSearchResultSummary = await _securityMasterService.GetMutualFundsByIsinAndName(name);
        return Ok(_mapper.Map<IEnumerable<SecuritySearchSummary>>(mfSearchResultSummary));
      }

      var equitySearchResultSummary = await _securityMasterService.SearchStocksByIsinAndSymbol(name);

      //removing the '-' from company name is important as the app uses '-' for splitting results
      var filterResult = equitySearchResultSummary.ToList()
        .Where(sr => (sr.Status != "NonEquity" && sr.Status != "Delisted" && sr.Status != "Amalgamation") && sr.Sublisting != "InActive").ToList();
      filterResult.ForEach(sr => sr.CompName = sr.CompName.Replace('-', ' '));
      return Ok(_mapper.Map<IEnumerable<SecuritySearchSummary>>(filterResult));
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> GetSecurityByIsin(string isin, string assetType, string exchange)
    {
      if (assetType == "Mutual Fund")
      {
        var mfSecurity = await _securityMasterService.GetMutualFundByIsin(isin);
        var mfLatestPrice = await _mfPricesSvc.GetLatestPrice(mfSecurity.AmfiCode);
        mfSecurity.LatestPrice = Convert.ToDouble(mfLatestPrice.Nav);
        var security = _mapper.Map<ModelSecurity>(mfSecurity);
        security.Symbol = mfSecurity.RTACode;
        security.Exchange = "DIR";
        security.IsMutualFund = true;
        return Ok(security);
      }

      var stockSecurity = await _securityMasterService.GetStockDetailsByIsin(isin, exchange);

      var identifier = exchange == "NSE" ? stockSecurity.Symbol : stockSecurity.Scripcode;
      var latestPrice = await _stockPricesSvc.GetLatestPrice(identifier, exchange);
      stockSecurity.LatestPrice = latestPrice == null ? 0 : latestPrice.Price;
      var modelSecurityResult = _mapper.Map<ModelSecurity>(stockSecurity);

      if (modelSecurityResult != null)
        modelSecurityResult.Exchange = exchange;

      return Ok(_mapper.Map<ModelSecurity>(modelSecurityResult));
    }

    [HttpGet("[action]/{modelId}")]
    public async Task<IActionResult> GetSecuritiesInModel(string modelId)
    {
      var securitiesInModel = await _strategyModelService.GetSecuritiesByModelId(modelId);
      return Ok(securitiesInModel);
    }
    [HttpGet("[action]/{modelId}")]
    public async Task<IActionResult> GetTradeIdeaModelSecuritybyModelId(string modelId)
    {
      var securitiesInModel = await _strategyModelService.GetBuyTradeIdeaModelSecurityId(modelId);
      return Ok(securitiesInModel.OrderByDescending(ord => ord.Weight));
    }
    [HttpDelete("[action]/{modelSecurityId}")]
    public async Task<IActionResult> Delete(string modelSecurityId)
    {
      await _strategyModelService.DeleteModelSecurity(modelSecurityId);
      return Ok();
    }
    [HttpGet("[action]")]
    public async Task<IActionResult> GetAllModelStrategy()
    {
      var modelSecuritys = await _strategyModelService.GetModelSecuritys();
      return Ok(modelSecuritys);
    }
  }
}
