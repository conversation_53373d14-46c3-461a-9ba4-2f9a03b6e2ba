﻿using System;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.ApiContracts.TransactionDrafts;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.ServiceIntegration.TransactionInput;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
    [Authorize]
    [Route("[controller]")]
    [ApiController]

    public class TransactionInputController : AlphaBaseController
    {
        private readonly ITransactionInputDraftService _transactionInputDraftService;
        public TransactionInputController(ITransactionInputDraftService transactionInputDraftService, IHttpContextAccessor contextAccessor) :base(contextAccessor) 
        {
            _transactionInputDraftService = transactionInputDraftService;
        }

        [HttpGet("[action]")]
        public async Task<IActionResult> GetAllTransactions()
        {
            try
            {
                return Ok(await _transactionInputDraftService.GetTransactionInputDraftsList());
            }
            catch (System.Exception ex)
            {
                return BadRequest(ex.Message);
            }

        }
        [HttpGet("[action]/{Id}")]
        public async Task<IActionResult> GetTransactionInputById(string Id)
        {
            try
            {
                var input = await _transactionInputDraftService.GetTransactionInputById(Id);
                if (input is null)
                {
                    return NotFound(Id);
                }

                return Ok(input);
            }
            catch (System.Exception ex)
            {
                return BadRequest($"Failed to Fetch {ex.Message}");
            }
        }


        [HttpPost("[action]")]
        public async Task<IActionResult> AddTransactionInput([FromBody] CapitalRegisterTransactions transactionInput)
        {
            try
            {
                return Ok(await _transactionInputDraftService.AddTransactionInput(transactionInput));
            }
            catch (System.Exception ex)
            {
                return BadRequest($"Failed to Add {ex.Message}");
            }
        }

        [HttpPut("[action]/{Id}")]
        public async Task<IActionResult> UpdateTransactionInput(string Id, [FromBody] CapitalRegisterTransactionUpdationDTO transactionDTO)
        {
            try
            {
                return Ok(await _transactionInputDraftService.UpdateTransactionInputStatus(Id, transactionDTO.Status, transactionDTO.SettlementDate));
            }
            catch (System.Exception ex)
            {
                return BadRequest($"Failed to Update {ex.Message}");
            }
        }


        [HttpGet("[action]/{Status}")]
        public async Task<IActionResult> GetTransactionsByStatus(string Status)
        {
            if (string.IsNullOrEmpty(Status) )
            {
                return BadRequest($"Status is Required");
            }
            if(!Enum.TryParse<Status>(Status,false,out var parsedstatus))
            {
                return BadRequest($"Invalid Status {Status}");
            }

            return Ok(await _transactionInputDraftService.GetTransactionsByStatus(Status));
        }
         
    }
}
