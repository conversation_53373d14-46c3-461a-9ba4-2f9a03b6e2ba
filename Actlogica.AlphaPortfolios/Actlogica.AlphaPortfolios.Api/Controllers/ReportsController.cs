﻿using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.ApiContracts.Reports.Payloads;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Azure;
using Microsoft.OData.Edm;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	[Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>,FundManager,OperationManager,Operations,AlphaRelationshipManager,AlphaAccountsSubscriber")]
	public class ReportsController : AlphaBaseController
	{
		private readonly IReportsService _service;
		private readonly IClientService _clientService;
		private readonly IPortfolioService _portfolioService;
		private readonly IReportCronConfigService _reportCronConfigService;
		public ReportsController(IReportsService service, IClientService clientService, IReportCronConfigService reportCronConfigService, IPortfolioService portfolioService, IHttpContextAccessor contextAccessor) : base(contextAccessor)
		{
			_service = service;
			_clientService = clientService;
			_reportCronConfigService = reportCronConfigService;
			_portfolioService = portfolioService;
			_reportCronConfigService.SetUserName(base.userName);
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetAll()
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{
				var client = await _clientService.GetClientByUserId(base.userId);
				var reportsOfClient = await _service.GetAllReportRequestsByClientId(client.Id);
				return Ok(reportsOfClient);

			}
			var reports = await _service.GetAllReportRequest();
			return Ok(reports);
		}

		[HttpGet("[action]/{id}")]
		public async Task<IActionResult> Get(string id)
		{
			var reports = await _service.GetReport(id);
			return Ok(reports);
		}
		[AllowAnonymous]
		[HttpGet("[action]")]
		public async Task<IActionResult> GetTypes()
		{
			var reportType = await _service.GetReportType();
			var ImplementationDone = new List<string>
						{
								"HoldingDetails",
								"CapitalGains"
						};
			var responseData = reportType.Select(r => new { Name = r.ToString(), IsNotImplemented = !ImplementationDone.Any(x => x.ToLowerInvariant() == r.ToString().ToLowerInvariant()) }).ToList();
			return Ok(responseData);
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> Initiate(ReportRequest request)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{   //Verify User Didn't Selected MIS
				var client = await _clientService.GetClientByUserId(base.userId);
				var requestPayload = JsonConvert.DeserializeObject<BaseRequestPayload>(request.RequestPayload);
				if (client.Id != requestPayload.ClientId)
				{
					return BadRequest(new { message = "Invalid Client Id", status = false });
				}

				//Check whether the portfolios belongs to this client
				var portfolio = await _portfolioService.CheckPortfolioIsInGivenClient(request.PortfolioId, request.ClientId);
				if (!portfolio)
				{
					return BadRequest(new { message = "Invalid Portfolio Id", status = false });
				}
			}
			var tenantClaim = User.Claims.First(c => c.Type == "tenant");
			var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
			request.TenantName = tenantValue;
			return Ok(await _service.InitiateReports(request));
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> OffsiteReports(ReportRequest request, DateTime fromDate, DateTime ToDate)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{   //Verify User Didn't Selected MIS
				var client = await _clientService.GetClientByUserId(base.userId);
				var requestPayload = JsonConvert.DeserializeObject<BaseRequestPayload>(request.RequestPayload);
				if (client.Id != requestPayload.ClientId)
				{
					return BadRequest(new { message = "Invalid Client Id", status = false });
				}

				//Check whether the portfolios belongs to this client
				var portfolio = await _portfolioService.CheckPortfolioIsInGivenClient(request.PortfolioId, request.ClientId);
				if (!portfolio)
				{
					return BadRequest(new { message = "Invalid Portfolio Id", status = false });
				}
			}
	
			return Ok(await _service.InitiateOffSiteReports(request, fromDate, ToDate));
		}

		[AllowAnonymous]
		[HttpGet("[action]/{reportRequestId}")]
		public async Task<FileResult> DownloadFile(string reportRequestId)
		{
			var reportRequest = await _service.GetReport(reportRequestId);
			if (reportRequest == null)
				throw new System.Exception("Report with request id not found");

			var requestPayload = JsonConvert.DeserializeObject<BaseRequestPayload>(reportRequest.RequestPayload);

			var client = await _clientService.GetClientById(requestPayload.ClientId);

			var extension = reportRequest.ReportPath.Split('.').Last();

			//Build the File Path.
			Stream streamData = await _service.DownloadFile(reportRequest.ReportPath);

			//Read the File data into Byte Array.
			byte[] bytes = ReadFully(streamData);

			string fileName;
			if (client != null)
				fileName = $"{client.FirstName} {client.LastName}-{client.ClientCode}-{reportRequest.ReportType}.{extension}";
			else
				fileName = $"{reportRequestId}-{reportRequest.ReportType}.{extension}";

			//Send the File to Download.
			return File(bytes, "application/octet-stream", fileName);
		}



		[HttpPost("[action]")]
		public async Task<IActionResult> ScheduleReport(ScheduleReportsCron reportSchedule)
		{
			if (reportSchedule.ReportType == ReportType.CapitalGains && reportSchedule.ClientId == null)
			{
				return BadRequest(new { message = "Client Id is required for Capital Gains Report", status = false });
			}

			if (reportSchedule.ReportType != ReportType.CapitalGains && reportSchedule.PortfolioId == null)
			{
				return BadRequest(new { message = "Portfolio Id is required for All other reports except Capital Gains", status = false });
			}

			if (role.Contains("AlphaAccountsSubscriber"))
			{
				// Check whether the PortfolioId and ClientId is of this User
				var tempClientID = "";
				if (reportSchedule.ClientId! == tempClientID)
				{
					return BadRequest(new { message = "Invalid Client Id ", status = false });
				}

			}

			if (!reportSchedule.ReportType.ToString().StartsWith("Mis", StringComparison.InvariantCultureIgnoreCase) && reportSchedule.Frequency == ReportFrequency.Daily)
			{
				return BadRequest(new { message = "Only MIS Report is Allowed to Have Frequency Daily", status = false });
			}

			try
			{
				var schedule = new ReportsCronConfig
				{
					Frequency = reportSchedule.Frequency,
					PortfolioId = reportSchedule.PortfolioId,
					ReportType = reportSchedule.ReportType,
					RequestDate = DateTime.Now,
					RequestedBy = userName,
					Time = reportSchedule.Time,
					ClientId = reportSchedule.ClientId,
					ReportFormat = reportSchedule.ReportFormat,
					Status = ScheduledReportStatus.Active
				};

				await _reportCronConfigService.ScheduleReport(schedule);
				return StatusCode(201, new { message = $"Successfully Scheduled the Report {reportSchedule.ReportType}", status = true });
			}
			catch (Exception e)
			{
				return BadRequest(new { message = $"Failed to Save. Reason: {e.Message}", status = false });
			}
		}

		[HttpPatch("[action]/{reportId}")]
		public async Task<IActionResult> EditScheduledReport(string reportId, [FromBody] EditReportSchedule editReportSchedule)
		{
			try
			{
				await _reportCronConfigService.EditScheduledReport(reportId, editReportSchedule.Time, editReportSchedule.Frequency);
				return StatusCode(201, new { message = "Edited Successfully", status = true });
			}
			catch (Exception e)
			{
				return BadRequest(new { message = $"Failed to Edit. Reason: {e.Message}", status = false });
			}

		}

		//status->true = enable
		//Status-false = disable
		[HttpPatch("[action]/{reportId}")]
		public async Task<IActionResult> ChangeScheduledReportStatus(string reportId, bool status)
		{

			try
			{
				if (status == true)
				{
					await _reportCronConfigService.EnableScheduledReport(reportId);
				}
				else
				{
					await _reportCronConfigService.DisableScheduledReport(reportId);
				}

				return Ok(new { message = "Succesfully Changed the Status of Report", status = true });
			}
			catch (Exception e)
			{
				return StatusCode(500, new { message = $"Failed to Change Status. Reason: {e.Message}", status = false });
			}
		}

		[HttpDelete("[action]/{reportId}")]
		public async Task<IActionResult> DeleteScheduledReportStatus(string reportId)
		{
			try
			{
				await _reportCronConfigService.DeleteScheduledReport(reportId);
				return Ok(new { message = "Succesfully Deleted the Schedule of the Report", status = true });
			}
			catch (Exception e)
			{
				return StatusCode(500, new { message = $"Failed to Delete. Reason: {e.Message}", status = false });
			}

		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetScheduledReport([FromQuery] ReportType? reportType, [FromQuery] ScheduledReportStatus? scheduledReportStatus)
		{
			try
			{
				if (reportType.HasValue)
				{
					var reports = await _reportCronConfigService.GetScheduledReports(reportType.Value, scheduledReportStatus);
					return Ok(new { data = reports, total = reports.Count() });
				}
				else
				{
					if (scheduledReportStatus.HasValue)
					{
						var reports = await _reportCronConfigService.GetScheduledReportsByStatus(scheduledReportStatus.Value);
						return Ok(new { data = reports, total = reports.Count() });
					}
					else
					{
						var reports = await _reportCronConfigService.GetAll();
						return Ok(new { data = reports, total = reports.Count() });
					}

				}
			}
			catch (Exception e)
			{
				return BadRequest(new { message = $"Failed to Fetch {e.Message}", status = false });
			}
		}

		[HttpGet("[action]/{id}")]
		public async Task<IActionResult> GetScheduledReportDetailsById(string id)
		{
			try
			{
				var scheduledReport = await _reportCronConfigService.GetScheduledReportDetailsById(id);
				return Ok(new { data = scheduledReport });


			}
			catch (Exception e)
			{
				return BadRequest(new { message = $"Failed to Fetch {e.Message}", status = false });

			}

		}
		private byte[] ReadFully(Stream input)
		{
			byte[] buffer = new byte[16 * 1024];
			using (MemoryStream ms = new MemoryStream())
			{
				int read;
				while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
				{
					ms.Write(buffer, 0, read);
				}
				return ms.ToArray();
			}
		}


		private static string Base64Decode(string base64EncodedData)
		{
			var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
			return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
		}

		[HttpPost("[action]")]
		public async Task<IActionResult> InitiateSebiMonthly(ReportRequest request, DateTime fromDate, DateTime ToDate)
		{
			if (role.Contains("AlphaAccountsSubscriber"))
			{   //Verify User Didn't Selected MIS
				var client = await _clientService.GetClientByUserId(base.userId);
				var requestPayload = JsonConvert.DeserializeObject<BaseRequestPayload>(request.RequestPayload);
				if (client.Id != requestPayload.ClientId)
				{
					return BadRequest(new { message = "Invalid Client Id", status = false });
				}

				//Check whether the portfolios belongs to this client
				var portfolio = await _portfolioService.CheckPortfolioIsInGivenClient(request.PortfolioId, request.ClientId);
				if (!portfolio)
				{
					return BadRequest(new { message = "Invalid Portfolio Id", status = false });
				}
			}
	
			return Ok(await _service.InitiateSebiMonthly(request, fromDate, ToDate));
		}
	}
}
