﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
  [Route("api/[controller]")]
  [ApiController]
  [Authorize]
  public class GeneralSettingController : ControllerBase
  {
    private readonly IGeneralSettingService _generalSettingService;
    public GeneralSettingController(IGeneralSettingService generalSettingService)
    {
      _generalSettingService = generalSettingService;
    }

    [Authorize(Roles = "TenantRolePolicy")]
    [HttpGet("[action]")]
    public async Task<IActionResult> GetAll()
    {
      var settings = await _generalSettingService.GetAll();
      return Ok(settings);
    }

    [Authorize(Roles = "TenantRolePolicy")]
    [HttpGet("[action]/{id}")]
    public async Task<IActionResult> GetById(string id)
    {
      var setting = await _generalSettingService.GetById(id);
      return Ok(setting);
    }

    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin")]
    [HttpPost("[action]")]
    public async Task<IActionResult> AddNewSetting([FromBody] GeneralSetting existingSetting)
    {
      var setting = await _generalSettingService.Create(existingSetting);
      return Ok(setting);
    }

    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin")]
    [HttpPut("[action]/{id}")]
    public async Task<IActionResult> UpdateSetting(string id, [FromBody] GeneralSetting existingSetting)
    {
      var setting = await _generalSettingService.Update(id, existingSetting);
      return Ok(setting);
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> TenantIsInFinflo()
    {
      try
      {
        var setting = await _generalSettingService.GetByKey("isInFinflo");
        if (setting != null)
        {
          return Ok(setting.Value);
        }
        else
        {
          return Ok(false);
        }
      }
      catch (Exception ex)
      {
        return BadRequest(new { message = ex.Message, status = false });
      }
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> IsComplianceRulesEnabled()
    {
      try
      {
        var setting = await _generalSettingService.GetByKey("isComplianceRuleEnabled");
        if (setting != null)
        {
          return Ok(setting.Value);
        }
        else
        {
          return Ok(false);
        }
      }
      catch (Exception ex)
      {
        return BadRequest(new { message = ex.Message, status = false });
      }
    }

    [Authorize(Roles = "AlphaAccountsAdmin,AlphaAdmin")]
    [HttpGet("[action]/{key}")]
    public async Task<IActionResult> GetByKey(string key)
    {
      var setting = await _generalSettingService.GetByKey(key);
      return Ok(setting);
    }


  }
}
