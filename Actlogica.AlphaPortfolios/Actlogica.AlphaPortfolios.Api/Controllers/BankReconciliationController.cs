﻿using Actlogica.AlphaPortfolios.ApiContracts.BankReconciliation;
using Actlogica.AlphaPortfolios.ServiceIntegration.BankReconciliation;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.OData.Edm;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{
	[Route("[controller]")]
	[ApiController]
	public class BankReconciliationController : ControllerBase
	{
		private readonly IBankReconciliationService _bankReconciliationService;
		private readonly IMapper _mapper;

		public BankReconciliationController(IMapper mapper, IBankReconciliationService bankReconciliationService)
		{
			_bankReconciliationService = bankReconciliationService;
			_mapper = mapper;

		}
		[HttpPost("[action]")]
		public async Task<IActionResult> UploadSettlementFile(List<IFormFile> files)
		{
			try
			{
				if (files == null || files.Count < 2) return BadRequest($"Invalid parameter received {nameof(files)}");

				var tradeOrderSettlementId = await _bankReconciliationService.ScheduleBankReconciliation(files);

				return Ok(tradeOrderSettlementId);
			}
			catch (InvalidDataException ex)
			{
				return Conflict(ex.Message);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpGet("[action]/{id}")]
		public async Task<IActionResult> GetReconResult(string id)
		{
			return Ok(await _bankReconciliationService.GetReconResult(id));
		}

		[HttpGet("[action]/{bankReconProcessId}/{clientId}")]
		public async Task<IActionResult> GetBankReconMatch(string bankReconProcessId, string clientId)
		{

			var tradeOrderSettlementId = await _bankReconciliationService.GetBankReconMatch(bankReconProcessId, clientId);

			return Ok(tradeOrderSettlementId);
		}

		[HttpGet("[action]/{bankReconProcessId}/{status}")]
		public async Task<FileResult> DownloadFile(string bankReconProcessId, string status)
		{
			var data = await _bankReconciliationService.DownloadFile(bankReconProcessId, status);
			if (data != null)
			{
				var downloadObj = _mapper.Map<List<BRDownloadFile>>(data);
				var csvString = downloadObj.ToCsv();
				return File(Encoding.ASCII.GetBytes(csvString), "text/csv", $"{status}.csv");
			}
			return null;
		}

		[HttpPost("[action]/{id}")]
		public async Task<IActionResult> AdjustmentFileUpload(string id, IFormFile file)
		{
			try
			{
				var tradeOrderSettlementId = await _bankReconciliationService.ProcessBRAdjustment(file, id);
				return Ok(tradeOrderSettlementId);
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetProcessedRecon()
		{

			var tradeOrderSettlementId = await _bankReconciliationService.GetProcessedRecon();
			return Ok(tradeOrderSettlementId.ToList().OrderByDescending(br => br.CreatedDate));
		}

		[HttpGet("{status}/UpdateStatus/{id}")]
		public async Task<IActionResult> UpdateStatus(string id, string status)
		{
			try
			{
				await _bankReconciliationService.UpdateStatus(id, status);
				return Ok();
			}
			catch (Exception ex)
			{
				return BadRequest(ex.Message);
			}
		}

	}
}
