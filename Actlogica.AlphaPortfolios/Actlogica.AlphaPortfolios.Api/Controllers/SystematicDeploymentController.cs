﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Orders;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api.Controllers
{

	[Route("api/[controller]")]
	[ApiController]
	[Authorize]
	public class SystematicDeploymentController : AlphaBaseController
	{
		private readonly ISystematicDeploymentService _systematicDeploymentService;
		public SystematicDeploymentController(ISystematicDeploymentService systematicDeploymentService, IHttpContextAccessor contextAccessor) : base(contextAccessor)
		{
			_systematicDeploymentService = systematicDeploymentService;
			_systematicDeploymentService.SetCreatedBy(base.userName);

		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetAllByStatus([FromQuery] string status)
		{

			try
			{
				if (status.ToLower() == "active")
				{
					return Ok(await _systematicDeploymentService.GetActive());
				}
				else if (status.ToLower() == "inactive")
				{
					return Ok(await _systematicDeploymentService.GetInActive());

				}
				else
				{

					return new JsonResult(new { message = "Please give a valid status,that is active or inactive" });
				}

			}

			catch (Exception e)
			{

				return new JsonResult(new { message = "Sorry there's some error in fetching" });
			}
		}
		[HttpPost("[action]")]
		public async Task<IActionResult> NewDeployment(SystematicDeployment deployment)
		{
			try
			{
				var userName = User.Identity.Name;

				deployment.CreatedBy = userName;
				await _systematicDeploymentService.NewDeployment(deployment);
				return new JsonResult(new { status = true, message = "Successfully Created New Deployment" });
			}
			catch (Exception e)
			{

				return StatusCode(400, new JsonResult(new { status = false, message = e.Message }));
			}
		}


		[HttpPut("[action]/{id}")]
		public async Task<IActionResult> EditActiveStatus(string id, [FromBody] bool isActive)
		{

			try
			{
				await _systematicDeploymentService.EditSystematicDeployment(id, isActive);

				return new JsonResult(new { status = true, message = "Edited Successfully" });
			}
			catch (Exception e)
			{
				return StatusCode(400, new JsonResult(new { status = false, message = e.Message }));
			}

		}


		[HttpGet("[action]/{deploymentSetupId}")]
		public async Task<IActionResult> GetDetails(string deploymentSetupId)
		{
			return Ok(await _systematicDeploymentService.GetDetails(deploymentSetupId));
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> TriggerRedemptionCron()
		{
			await _systematicDeploymentService.TriggerRedemptionsForToday();

			return Ok();
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> TriggerInstallmentCron()
		{
			await _systematicDeploymentService.TriggerInstallmentsForToday();

			return Ok();
		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetDeploymentTracker()
		{

			try
			{
				return Ok(await _systematicDeploymentService.GetDeploymentTracker());
			}
			catch (Exception e)
			{
				return StatusCode(400, new JsonResult(new { status = false, message = e.Message }));
			}
		}

		[HttpPost("[action]/{id}")]
		public async Task<IActionResult> TriggerRedemptionManually(string id)
		{

			try
			{
				await _systematicDeploymentService.TriggerRedemptionOrdersManually(id);

				return Ok();

			}
			catch (Exception e)
			{

				return StatusCode(400, new JsonResult(new { status = false }));
			}

		}

		[HttpPost("[action]/{id}")]
		public async Task<IActionResult> TriggerInstallmentManually(string id)
		{

			try
			{
				await _systematicDeploymentService.TriggerInstallmentOrdersManually(id);

				return Ok();

			}
			catch (Exception e)
			{

				return StatusCode(400, new JsonResult(new { status = false }));
			}

		}

		[HttpPost("[action]/{id}")]
		public async Task<IActionResult> AbandonTrackerOrder(string id)
		{

			try
			{
				await _systematicDeploymentService.AbandonTrackerOrder(id);
				return Ok();

			}
			catch (Exception e)
			{

				return StatusCode(400, new JsonResult(new { status = false }));
			}

		}

		[HttpPost("[action]/{id}")]
		public IActionResult FindNextNonHolidayDate([FromBody] DateTime date)
		{


			var nonHoliday = SystematicDeploymentService.FindNextWorkingDay(date);

			return Ok();

		}

		[HttpGet("[action]")]
		public async Task<IActionResult> GetSTPTrackerOrderDetails([FromQuery] string trackerId, [FromQuery] string stpType)
		{
			if (String.IsNullOrEmpty(trackerId))
			{
				return BadRequest(new { message = "Tracker Id is required", error = true });
			}

			if (stpType.ToLower() != "buy" && stpType.ToLower() != "sell")
			{
				return BadRequest(new { message = "stp type should be either buy or sell", error = true });
			}

			var type = "SYSTEMATICDEPLOYMENTREDEMPTION";
			if (stpType.ToLower() == "buy")
			{
				type = "SYSTEMATICDEPLOYMENTINSTALLMENT";
			}
			var orders = await _systematicDeploymentService.GetSTPTrackerOrders(trackerId, type);

			return Ok(orders);

		}



	}
}
