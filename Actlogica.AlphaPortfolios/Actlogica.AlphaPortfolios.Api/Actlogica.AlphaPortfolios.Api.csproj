<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Contracts\**" />
    <Content Remove="Contracts\**" />
    <EmbeddedResource Remove="Contracts\**" />
    <None Remove="Contracts\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.16.2" />
    <PackageReference Include="ClickHouse.Client" Version="7.8.2" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="IdentityServer4.AccessTokenValidation" Version="2.7.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="7.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Quartz" Version="3.8.1" />
    <PackageReference Include="Quartz.AspNetCore" Version="3.8.1" />
    <PackageReference Include="RestSharp" Version="106.15.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="Serilog.Formatting.Compact" Version="3.0.0" />
    <PackageReference Include="Serilog.Sinks.ClickHouse" Version="1.0.0" />
    <PackageReference Include="Serilog.Sinks.Http" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.MongoDB" Version="6.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="5.6.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Actlogica.AlphaPortfolios.ApiContracts\Actlogica.AlphaPortfolios.ApiContracts.csproj" />
    <ProjectReference Include="..\Actlogica.AlphaPortfolios.Data\Actlogica.AlphaPortfolios.Data.csproj" />
    <ProjectReference Include="..\Actlogica.AlphaPortfolios.ServiceIntegration\Actlogica.AlphaPortfolios.ServiceIntegration.csproj" />
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>

</Project>
