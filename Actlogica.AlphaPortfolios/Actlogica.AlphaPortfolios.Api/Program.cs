using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.Api
{
	public class Program
	{
		public static void Main(string[] args)
		{
			Log.Logger = new LoggerConfiguration()

				.MinimumLevel.Debug()
				.Enrich.FromLogContext()
				.WriteTo.Console()
				.MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
				// .WriteTo.MongoDB(
				// 	"mongodb+srv://<user_name:>:<password>>@<cluster>/<db>", 
				// 	collectionName: "test5")
				.CreateLogger();
			try
			{
				DotNetEnv.Env.Load("../");
				Log.Information("Logging Started");
				CreateHostBuilder(args).Build().Run();
			}
			catch (Exception e)
			{
				Log.Fatal(e, "Logging Failed");
			}
			finally
			{
				Log.CloseAndFlush();
			}
		}

		public static IHostBuilder CreateHostBuilder(string[] args) =>
				Host.CreateDefaultBuilder(args)
					.ConfigureAppConfiguration((hostingContext, config) =>
					{

						// Add JSON configuration first (optional, but common practice)
						config.SetBasePath(Directory.GetCurrentDirectory())
							  .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
							  .AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json",
										 optional: true,
										 reloadOnChange: true);


						// Add Environment Variables
						// This will load all environment variables and can override previous configs
						config.AddEnvironmentVariables();
						var builtConfig = config.Build();
						Console.WriteLine($"Built Config Connection String: {builtConfig.GetConnectionString("DefaultConnection")}");
						// // Optional: Add prefix filtering for environment variables
						// // This will only load environment variables that start with "MyApp_"
						// config.AddEnvironmentVariables(prefix: "MyApp_");

						// Optional: Add command line arguments (highest priority)
						// if (args != null)
						// {
						// 	config.AddCommandLine(args);
						// }
					})
					.UseSerilog()
						.ConfigureWebHostDefaults(webBuilder =>
						{
							webBuilder.UseStartup<Startup>();
						});
	}
}
