using Actlogica.AlphaPortfolios.Api.Middleware;
using Actlogica.AlphaPortfolios.Api.Utilities;
using Actlogica.AlphaPortfolios.ApiContracts;
using Actlogica.AlphaPortfolios.Data.Configs;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.HistoricalData;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.HoldingRecon;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces.HoldingRecon;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.OrderMgmt;
using Actlogica.AlphaPortfolios.Data.Repositories.MasterDataDb;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage;
using Actlogica.AlphaPortfolios.ServiceIntegration.BankReconciliation;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.Configs;
using Actlogica.AlphaPortfolios.ServiceIntegration.CorporateActions;
using Actlogica.AlphaPortfolios.ServiceIntegration.HoldingReconciliation;
using Actlogica.AlphaPortfolios.ServiceIntegration.OrderManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.Holding;
using Actlogica.AlphaPortfolios.ServiceIntegration.RestrictedStockORGs;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using Actlogica.AlphaPortfolios.ServiceIntegration.StrategyManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.TenantSettings;
using Actlogica.AlphaPortfolios.ServiceIntegration.TradeIdeaManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.UserManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioHoldingManagement;
using AutoMapper;
using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using CsvHelper;
using Actlogica.AlphaPortfolios.ServiceIntegration.Communications.Email;
using Actlogica.AlphaPortfolios.ServiceIntegration.Fees;
using Actlogica.AlphaPortfolios.ServiceIntegration.Investments;

using Quartz;
using Actlogica.AlphaPortfolios.Api.Cron;
using Actlogica.AlphaPortfolios.ServiceIntegration.Dashboards;
using Actlogica.AlphaPortfolios.ServiceIntegration.BankReconciliation.BankTransactionReconProcess;
using Actlogica.AlphaPortfolios.ServiceIntegration.ModelHoldingManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.PeakMargin;
using System.Text.Json.Serialization;
using Actlogica.AlphaPortfolios.Api.Types;
using Actlogica.AlphaPortfolios.ServiceIntegration.Transformer;
using Actlogica.AlphaPortfolios.ApiContracts.Clients;
using Newtonsoft.Json.Converters;
using Actlogica.AlphaPortfolios.ServiceIntegration.Communications;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage.PerformanceEngine;
using Microsoft.Azure.Cosmos.Table;
using Actlogica.AlphaPortfolios.ApiContracts.ApiBodyTypes.Clients;
using Microsoft.AspNetCore.Authorization;
using Actlogica.AlphaPortfolios.ServiceIntegration.Capital;
using Actlogica.AlphaPortfolios.ServiceIntegration.Income;
using Actlogica.AlphaPortfolios.ServiceIntegration.Imports;
using Actlogica.AlphaPortfolios.ServiceIntegration.RabbitMQ;
using Actlogica.AlphaPortfolios.ServiceIntegration.DataUpdate;
using Actlogica.AlphaPortfolios.ServiceIntegration.DistributionCenter;
using Actlogica.AlphaPortfolios.ServiceIntegration.TradeCompliance;
using Actlogica.AlphaPortfolios.ServiceIntegration.ReceivablePayables;
using Actlogica.AlphaPortfolios.ServiceIntegration.Ndpms;
using Actlogica.AlphaPortfolios.ServiceIntegration.TransactionInput;

namespace Actlogica.AlphaPortfolios.Api
{
	public class Startup
	{
		string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";
		public Startup(IConfiguration configuration)
		{
			Configuration = configuration;
		}

		public IConfiguration Configuration { get; }

		// This method gets called by the runtime. Use this method to add services to the container.
		public void ConfigureServices(IServiceCollection services)
		{

			services.AddQuartz(q =>
			{
				//STP Cron Job
				var jobKey = new JobKey("SystematicDeploymentJob");
				q.AddJob<SystematicDeploymentCron>(opts => opts.WithIdentity(jobKey));

				q.AddTrigger(opts => opts
					.ForJob(jobKey)
					.WithIdentity("SystematicDeployment")
					.WithCronSchedule("0 0 0 * * ?")

				);

				//Report Master Job
				var reportMasterJob = new JobKey("ReportMasterJob");
				q.AddJob<ReportsMasterCron>(opts => opts.WithIdentity(reportMasterJob));

				//Schedule Report Master Job at 12:00 am
				q.AddTrigger(opts => opts
				.ForJob(reportMasterJob)
				.WithIdentity("ReportMaster")
				.WithCronSchedule("0 0 0 * * ?"));


				//MIS Report Cron Job
				var misReportJobKey = new JobKey("MisReportJob");
				q.AddJob<MisReportJob>(opts => opts.WithIdentity(misReportJobKey).StoreDurably(true));

				//All Other reports
				var holdingDetailsReportJobKey = new JobKey("HoldingDetailsJob");
				q.AddJob<HoldingDetailsReportsJob>(opts => opts.WithIdentity(holdingDetailsReportJobKey).StoreDurably(true));

				var capitalGainsReportJobKey = new JobKey("CapitalGainsJob");
				q.AddJob<CapitalGainsReportsJob>(opts => opts.WithIdentity(capitalGainsReportJobKey).StoreDurably(true));

				var capitalRegisterReportJobKey = new JobKey("CapitalRegisterJob");
				q.AddJob<CapitalRegisterReportsJob>(opts => opts.WithIdentity(capitalRegisterReportJobKey).StoreDurably(true));

				var bankBookReportJobKey = new JobKey("BankBookJob");
				q.AddJob<BankBookReportsJob>(opts => opts.WithIdentity(bankBookReportJobKey).StoreDurably(true));

				var dividendsStatementJobKey = new JobKey("DividendStatementJob");
				q.AddJob<DividendStatementReportsJob>(opts => opts.WithIdentity(dividendsStatementJobKey).StoreDurably(true));

				var sebiQuarterlyJobKey = new JobKey("SebiQuarterlyJob");
				q.AddJob<SebiQuarterlyReportsJob>(opts => opts.WithIdentity(sebiQuarterlyJobKey).StoreDurably(true));
			});

			services.AddQuartzHostedService(q => q.WaitForJobsToComplete = true);

			services.AddCors(options =>
			 {
				 options.AddPolicy(name: MyAllowSpecificOrigins,
																policy =>
																{
																	//policy.WithOrigins(Configuration.GetValue<string>("AllowedCors"));
																	policy.AllowAnyOrigin();
																	policy.AllowAnyMethod();
																	policy.AllowAnyHeader();
																});
			 });
			services.AddControllers().AddNewtonsoftJson();
			services.AddControllers().AddNewtonsoftJson(options =>
			{
				options.SerializerSettings.Converters.Add(new StringEnumConverter());
			});

			services.AddControllers()
			.AddJsonOptions(option =>
			{
				option.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
				option.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
			});
			services.AddSwaggerGen((Action<SwaggerGenOptions>)(c =>
			{
				
				c.SwaggerDoc("v1", new OpenApiInfo()
				{
					Title = "Actlogica.AlphaPortfolios.Api",
					Version = "v1"
				});

				c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
				{
					Name = "Authorization",
					Type = SecuritySchemeType.ApiKey,
					Scheme = "Bearer",
					BearerFormat = "JWT",
					In = ParameterLocation.Header,
					Description = "JWT Authorization header using the Bearer Scheme"
				});

				c.AddSecurityRequirement(new OpenApiSecurityRequirement
				{
					{
						new OpenApiSecurityScheme
						{
							Reference = new OpenApiReference
							{
								Type = ReferenceType.SecurityScheme,
								Id = "Bearer"
							},
							Scheme = "oauth2",
							Name = "Bearer",
							In = ParameterLocation.Header,
						},
						new List<string>()
					}
				});

			}));
			var sendgrid = Configuration.GetSection("SendGrid");
			var sendGridApiUrl = sendgrid.GetValue<string>("SendGridApiUrl");
			var sendGridApiKey = sendgrid.GetValue<string>("SendGridApiKey");
			
			var idServerConfig = Configuration.GetSection("IdentityServerSettings");
			var idServerAuthorityUrl = idServerConfig.GetValue(typeof(string), "DiscoveryUrl");
			
			var v2PerfConfig = Configuration.GetSection("PerformaceEngineV2");
			var v2PerfApi = sendgrid.GetValue<string>("V2PerformanceEngineUrl");
			
			
			var ndpms = Configuration.GetSection("Ndpms");
			var ndpmsUrl = ndpms.GetValue<string>("NdpmsUrl");
			var aesKey = ndpms.GetValue<string>("NdpmsAesKey");
			var aesIV = ndpms.GetValue<string>("NdpmsAesIV");

			services.AddAuthentication("Bearer")
			.AddIdentityServerAuthentication("Bearer", options =>
			{
				options.Authority = idServerAuthorityUrl.ToString(); //url of IDentityServer4
				options.ApiName = "AlphaPortfoliosAPI"; //name of API Resource
			});
			services.AddAuthorization(options =>
			{
				options.AddPolicy("ResearchAnalystPolicy", policy => policy.RequireRole("ResearchAnalyst"));
				options.AddPolicy("FundManagerPolicy", policy => policy.RequireRole("FundManager"));
				options.AddPolicy("AlphaAccountsSubscriberPolicy", policy => policy.RequireRole("AlphaAccountsSubscriber"));
				options.AddPolicy("CommonRolePolicy", policy => policy
				.RequireRole("AlphaAccountsAdmin", "AlphaAdmin", "PrincipleOfficer", "CIO", "FundManager", "ResearchAnalyst", "Operations", "OperationManager", "AlphaRelationshipManager")
				);

				options.AddPolicy("TenantRolePolicy", policy => policy
				.RequireRole("AlphaAccountsAdmin", "AlphaAdmin", "PrincipleOfficer", "CIO", "FundManager", "ResearchAnalyst", "Operations", "OperationManager", "AlphaRelationshipManager", "DealerDeskTrader")
				);

				options.AddPolicy("CommonRoleIncludingSubscriberPolicy", policy => policy
				.RequireRole("AlphaAccountsAdmin", "AlphaAdmin", "PrincipleOfficer", "CIO", "FundManager", "ResearchAnalyst", "Operations", "OperationManager", "AlphaRelationshipManager", "AlphaAccountsSubscriber" ,"AlphaDistributorAdmin", "AlphaDistributorRelationshipManager")
				);

				options.AddPolicy("AlphaEssentialsPolicy", policy => policy.RequireClaim("subscription", "Alpha-Essential"));

				options.AddPolicy("TradeIdeaAccessPolicy", policy =>
	 							 policy.Requirements.Add(new QueryParameterRequirement("FilterBy", "TradeIdea")));

			});


			services.AddControllers(options =>
				{
					options.ModelBinderProviders.Insert(0, new JsonModelBinderProvider<IEnumerable<ApiContracts.ApiBodyTypes.Clients.ClientBankBody>>());
					options.ModelBinderProviders.Insert(1, new JsonModelBinderProvider<ApiContracts.ApiBodyTypes.Clients.ClientDetails>());
					options.ModelBinderProviders.Insert(2, new JsonModelBinderProvider<ClientPersonalDetailsBody>());

				});
			var coreDbConnectionString = Configuration.GetConnectionString("DefaultConnection");
			var options = new DbContextOptionsBuilder<AlphaPortfolioDbContext>();
			options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection"));
			services.AddDbContext<AlphaPortfolioDbContext>(options => options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection")));
			services.AddHttpContextAccessor();
			services.AddSingleton<IUriService>(o =>
			{
				var accessor = o.GetRequiredService<IHttpContextAccessor>();
				var request = accessor.HttpContext.Request;
				var uri = string.Concat(request.Scheme, "://", request.Host.ToUriComponent());
				return new UriService(uri);
			});
			//using(var context = new AlphaPortfolioDbContext(options.Options))
			//{
			// context.Database.Migrate();
			//}

			var identityServerSettings = Configuration.GetSection("IdentityServerSettings");
			services.Configure<IdentityServerApiSettings>(options => Configuration.GetSection("IdentityServerSettings").Bind(options));
			services.Configure<IntegrationProviders>(options => Configuration.GetSection("IntegrationProviders").Bind(options));
			services.Configure<DataUpdate>(Configuration.GetSection("DataUpdateConfigs"));
			var investaApiKey = "43fc59fb04234a26b7b166bc852b7672";
			var investaApiUrl = "https://investa.actlogica.com/";

			var mfMasterDataApi = Configuration.GetSection("IntegrationProviders").GetValue<string>("MfMasterDataApi");
			var mfMasterDataApiKey = Configuration.GetSection("IntegrationProviders").GetValue<string>("MfMasterDataApiKey");

			var transformerApiUrl = Configuration.GetSection("TransformerSettings").GetValue<string>("Url");

			var tenantStorageConnectionString = Configuration.GetConnectionString("TenantStorageConnectionString");
			var masterDataDbConnectionString = Configuration.GetConnectionString("MasterDataDbConnectionString");
			var tradeFileContainerName = Configuration.GetSection("Storage:TradeFileContainerName");
			var alphaTradeFileContainerName = Configuration.GetSection("Storage:AlphaTradeFileContainerName");
			var portfolioHoldingContainerName = Configuration.GetSection("Storage:PortfolioHoldingContainerName");
			var clientAgreementContainerName = Configuration.GetSection("Storage:ClientAgreementContainerName");
			var allocationFileContainerName = Configuration.GetSection("Storage:AllocationFileContainerName");
			var settlementFileContainerName = Configuration.GetSection("Storage:OrderSettlementFiles");
			var holdingReconFileContainerName = Configuration.GetSection("Storage:HoldingReconFileContainerName");
			var capitalRegisterFileContainerName = Configuration.GetSection("Storage:CapitalRegisterFileContainerName");
			var incomeExpenseFileContainerName = Configuration.GetSection("Storage:IncomeExpenseFileContainerName");
			var dataUpdateFileContainerName = Configuration.GetSection("Storage:DataUpdateFileContainerName");
			var distributionCenterFileContainerName = Configuration.GetSection("Storage:DistributionCenterFileContainerName");

			
			var storageContainerNames = new StorageContainerNames
			{
				AllocationFileContainerName = allocationFileContainerName.Value,
				AlphaTradeFileContainerName = alphaTradeFileContainerName.Value,
				PortfolioHoldingFileContainerName = portfolioHoldingContainerName.Value,
				TradeFileContainerName = tradeFileContainerName.Value,
				SettlementFileContainerName = settlementFileContainerName.Value,
				ClientAgreementContainerName = clientAgreementContainerName.Value,
				HoldingReconFileContainerName = holdingReconFileContainerName.Value,
				CapitalRegisterFileContainerName = capitalRegisterFileContainerName.Value,
				IncomeExpenseFileContainerName = incomeExpenseFileContainerName.Value,
				DataUpdateFileContainerName = dataUpdateFileContainerName.Value,
				DistributionCenterContainerName = distributionCenterFileContainerName.Value
			};

			var blobServiceClient = new BlobServiceClient(tenantStorageConnectionString);
			var svcBusConnStr = Configuration.GetConnectionString("AlphaPServiceBusConnectionString");
			var azureEmailConnectionString = Configuration.GetConnectionString("AzureEmailConnectionString");
			var emailService = new AzureEmailService(azureEmailConnectionString);
			var sendGridService = new SendGridService(sendGridApiKey,sendGridApiUrl);
			

			services.Configure<ConnectionStrings>((IConfiguration)this.Configuration.GetSection("ConnectionStrings"));
			services.Configure<PerformanceEngineConfigs>((IConfiguration)this.Configuration.GetSection("PerformanceEngineConfigs"));
			IMapper mapper = new MapperConfiguration((Action<IMapperConfigurationExpression>)(mc =>
			{
				mc.AddProfile((Profile)new DataMappingProfiles());
				mc.AddProfile((Profile)new ServiceMappingProfiles());
				mc.AddProfile((Profile)new ApiMappingProfiles());
			})).CreateMapper();
			services.AddSingleton<IMapper>(mapper);
			services.AddScoped<UserClaimProvider>();

			services.AddScoped(typeof(IDbRepository<>), typeof(DbRepositoryBase<>));
			services.AddScoped<IClientsRepository, ClientRepository>();
			services.AddScoped<IDirectEquityInvestmentRepository, DirectEquityInvestmentRepository>();
			services.AddScoped<IDirectEquityTransactionRepository, DirectEquityTransactionRepository>();
			services.AddScoped<IInvestmentRepository, InvestmentRepository>();
			services.AddScoped<IInvestmentTransactionRepository, InvestmentTransactionRepository>();
			services.AddScoped<IFAInvestmentMappingRepository, FAInvestmentMappingRepository>();
			services.AddScoped<IDirectEquityMasterDataRepository>(options => new DirectEquityMasterDataRepository(masterDataDbConnectionString));
			services.AddScoped<IMutualFundMasterDataRepository>(options => new MutualFundMasterDataRepository(masterDataDbConnectionString));
			services.AddScoped<IMutualFundInvestmentRepository, MutualFundInvestmentRepository>();
			services.AddScoped<IMutualFundTransactionRepository, MutualFundTransactionRepository>();
			services.AddScoped<IBondInvestmentRepository, BondInvestmentRepository>();
			services.AddScoped<IBondTransactionRepository, BondTransactionRepository>();
			services.AddScoped<IBondPriceRepository>(options => new BondPriceRepository(masterDataDbConnectionString));
			services.AddScoped<IBondMasterRepository>(options => new BondMasterRepository(masterDataDbConnectionString));
			services.AddScoped<IStrategyModelRepository, StrategyModelRepository>();
			services.AddScoped<IModelSecurityRepository, ModelSecurityRepository>();
			services.AddScoped<IPortfolioRepository, PortfolioRepository>();
			services.AddScoped<IModelPortfolioRepository, ModelPortfolioRepository>();
			services.AddScoped<IStrategyRepository, StrategyRepository>();
			services.AddScoped<IEquityCorporateActionRepository, EquityCorporateActionRepository>();
			services.AddScoped<IEquityHistoryRepository, EquityHistoryRepository>();
			services.AddScoped<IMFNavHistoryRepository, MFNavHistoryRepository>();
			services.AddScoped<IPoolCapitalRegisterRepository, PoolCapitalRegisterRepository>();
			services.AddScoped<IPoolCashLedgerRepository, PoolCashLedgerRepository>();
			services.AddScoped<IPortfolioCapitalRegisterRepository, PortfolioCapitalRegisterRepository>();
			services.AddScoped<IPortfolioCashLedgerRepository, PortfolioCashLedgerRepository>();
			services.AddScoped<IClientOrderEntryRepository, ClientOrderEntryRepository>();
			services.AddScoped<IBrokerRepository, BrokerRepository>();
			services.AddScoped<ICustodianRepository, CustodianRepository>();
			services.AddScoped<IGeneralSettingRepository, GeneralSettingRepository>();
			services.AddScoped<IRestrictedStockForClientsRepository, RestrictedStockForClientsRepository>();
			services.AddScoped<IRestrictedStockORGRepository, RestrictedStockORGRepository>();
			services.AddScoped<ITradeOrderRepository, TradeOrderRepository>();
			services.AddScoped<ITradeOrderSettlementFileRepository, TradeOrderSettlementFileRepository>();
			services.AddScoped<ITradeOrderSettlementFileEntryRepository, TradeOrderSettlementFileEntryRepository>();
			services.AddScoped<ITradeSettlementLogRepository, TradeSettlementLogRepository>();
			services.AddScoped<IOrderSettlementInClientAccountRepository, OrderSettlementInClientAccountRepository>();
			services.AddScoped<IBuyTradeIdeaRepository, BuyTradeIdeaRepository>();
			services.AddScoped<ISellTradeIdeaRepository, SellTradeIdeaRepository>();
			services.AddScoped<IBankReconciliationRepository, BankReconciliationRepository>();
			services.AddScoped<IBankReconProcessRepository, BankReconProcessRepository>();
			services.AddScoped<IBankReconProcessResultDataRepository, BankReconProcessResultDataRepository>();
			services.AddScoped<IBankReconBalanceProcessInputRepository, BankReconBalanceProcessInputRepository>();
			services.AddScoped<IDataUpdateRepository, DataUpdateRepository>();
			services.AddScoped<IDistributionMasterSharingConfigurationRepository, DistributionMasterSharingConfigurationRepository>();
			services.AddScoped<IDistributorMasterRepository, DistributorMasterRepository>();
			services.AddScoped<IDistributorMasterUsersRepository, DistributorMasterUsersRepository>();
			services.AddScoped<IPortfolioRMDetailsRepository, PortfolioRMDetailsRepository>();
			services.AddScoped<IDistributorMasterEmpanelmentRepository, DistributorMasterEmpanelmentRepository>();
			services.AddScoped<IDistributorMasterCertificationsRepository, DistributorMasterCertificationsRepository>();
			services.AddScoped<IDistributorMasterDocumentsRepository, DistributorMasterDocumentsRepository>();
			services.AddScoped<IDistributorMasterBranchesRepository, DistributorMasterBranchesRepository>();
			services.AddScoped<IDistributorMasterStatutoryInfoRepository, DistributorMasterStatutoryInfoRepository>();
			services.AddScoped<IDistributorMasterPayoutDetailsRepository, DistributorMasterPayoutDetailsRepository>();
			services.AddScoped<IDistributorMasterRegionRepository, DistributorMasterRegionRepository>();
			services.AddScoped<IDistributorAmcBranchesRepository, DistributorAmcBranchesRepository>();
			services.AddScoped<IDistributorAmcRegionsRepository, DistributorAmcRegionsRepository>();
			services.AddScoped<IPortfolioDistributorSharingRepository, PortfolioDistributorSharingRepository>();
			services.AddScoped<IPortfolioDistributorMappingRepository, PortfolioDistributorMappingRepository>();
			services.AddScoped<IDistributionMasterSharingConfigurationRepository, DistributionMasterSharingConfigurationRepository>();
			services.AddScoped<IDistributorMasterRepository, DistributorMasterRepository>();
			services.AddScoped<IDistributorMasterUsersRepository, DistributorMasterUsersRepository>();
			services.AddScoped<IPortfolioRMDetailsRepository, PortfolioRMDetailsRepository>();
			services.AddScoped<IDistributorMasterEmpanelmentRepository, DistributorMasterEmpanelmentRepository>();
			services.AddScoped<IDistributorMasterCertificationsRepository, DistributorMasterCertificationsRepository>();
			services.AddScoped<IDistributorMasterDocumentsRepository, DistributorMasterDocumentsRepository>();
			services.AddScoped<IDistributorMasterBranchesRepository, DistributorMasterBranchesRepository>();
			services.AddScoped<IDistributorMasterStatutoryInfoRepository, DistributorMasterStatutoryInfoRepository>();
			services.AddScoped<IDistributorMasterPayoutDetailsRepository, DistributorMasterPayoutDetailsRepository>();
			services.AddScoped<IDistributorMasterRegionRepository, DistributorMasterRegionRepository>();
			services.AddScoped<IDistributorAmcBranchesRepository, DistributorAmcBranchesRepository>();
			services.AddScoped<IDistributorAmcRegionsRepository, DistributorAmcRegionsRepository>();
			services.AddScoped<IPortfolioDistributorSharingRepository, PortfolioDistributorSharingRepository>();
			services.AddScoped<IPortfolioDistributorMappingRepository, PortfolioDistributorMappingRepository>();
			services.AddScoped<IPortfolioReceivableRepository, PortfolioReceivableRepository>();
			services.AddScoped<IStrategyCustodiansRepository, StrategyCustodiansRepository>();

			services.AddScoped<IHoldingCustReconRepository, HoldingCustReconRepository>();
			services.AddScoped<IHoldingReconProcessRepository, HoldingReconProcessRepository>();
			services.AddScoped<IHoldingReconProcessResultRepository, HoldingReconProcessResultRepository>();
			services.AddScoped<IHoldingReconRepository, HoldingReconRepository>();
			services.AddScoped<IHoldingReconRequestRepository, HoldingReconRequestRepository>();
			services.AddScoped<IPortfolioAnalyticsRepository, PortfolioAnalyticsRepository>();
			services.AddScoped<IPortfolioAumDailyRepository, PortfolioAumDailyRepository>();
			services.AddScoped<IPortfolioHoldingUpdateRepository, PortfolioHoldingUpdateRepository>();
			services.AddScoped<IPortfolioHoldingUpdateEntryRepository, PortfolioHoldingUpdateEntryRepository>();
			services.AddScoped<ISystematicDeploymentRepository, SystematicDeploymentRepository>();
			services.AddScoped<IDeploymentTrackerRepository, DeploymentTrackerRepository>();
			services.AddScoped<IDeploymentTrackerLogsRepository, DeploymentTrackerLogsRepository>();
			services.AddScoped<IFeeTemplateRepository, FeeTemplateRepository>();
			services.AddScoped<IStrategyModelFeeTemplateRepository, StrategyModelFeeTemplateRepository>();
			services.AddScoped<IPortfolioFeeTemplateRepository, PortfolioFeeTemplateRepository>();
			services.AddScoped<IMisReportsDataRepository, MisReportsDataRepository>();

			services.AddScoped<ITradeOrderUnsettledAmountsRepository, TradeOrderUnsettledAmountsRepository>();
			services.AddScoped<IBseSettlementCalendarRepository, BseSettlementCalendarRepository>();
			services.AddScoped<IFinFloSyncConfigurationRepository, FinFloSyncConfigurationRepository>();
			services.AddScoped<IReportsRepository, ReportsRepository>();
			services.AddScoped<IHistoricalModelPortfolioRecordRepository, HistoricalModelPortfolioRecordRepository>();
			services.AddScoped<IHistoricalModelPortfolioHoldingRepository, HistoricalModelPortfolioHoldingRepository>();
			services.AddScoped<IBankReconTransactionInputRepository, BankReconTransactionInputRepository>();
			services.AddScoped<ICustodyAllocationFileGenerator, CustodyAllocationFileGenerator>();
			services.AddScoped<IPortfolioFeeRepository, PortfolioFeeRepository>();
			services.AddScoped<IAlphaTransformerIntegrationRepository, AlphaTransformerIntegrationRepository>();
			services.AddScoped<ITransformerFileRequestRepository, TransformerFileRequestRepository>();
			services.AddScoped<IMarketIndexDataStorageRepository, MarketIndexDataStorageRepository>();
			services.AddScoped<IMasterLookupRepository, MasterLookupRepository>();
			services.AddScoped<IMasterLookupAMCRepository, MasterLookupAMCRepository>();

			services.AddScoped<IEquityCorporateActionService, EquityCorporateActionService>();
			services.AddScoped<IClientService, ClientService>();
			services.AddScoped<IStrategyModelService, StrategyModelService>();
			services.AddScoped<IStrategyService, StrategyService>();
			services.AddScoped<IPortfolioService, PortfolioService>();
			services.AddScoped<IModelPortfolioService, ModelPortfolioService>();
			services.AddScoped<ITaxSettingsService, TaxSettingsService>();
			services.AddScoped<IPoolOrderManagementService, PoolOrderManagementService>();
			services.AddScoped<IClientOrderManagementService, ClientOrderManagementService>();
			services.AddScoped<IMasterDataService, MasterDataService>();
			services.AddScoped<IGeneralSettingService, GeneralSettingService>();
			services.AddScoped<ISecurityMasterService>(
				options => new SecurityMasterService(
					investaApiUrl, investaApiKey, new BondMasterRepository(masterDataDbConnectionString),
					new DirectEquityMasterDataRepository(masterDataDbConnectionString),
					new MutualFundMasterDataRepository(masterDataDbConnectionString),
					new MarketIndexDataStorageRepository(),
			 		options.GetRequiredService<AlphaPortfolioDbContext>()
					));
			services.AddScoped<IDirectEquityPricesService>(options => new DirectEquityPricesService(investaApiUrl, investaApiKey));
			services.AddScoped<IMutualFundPricesService>(options => new MutualFundPricesService(mfMasterDataApi, mfMasterDataApiKey));
			services.AddScoped<IFileStorageService>(options => new FileStorageService(blobServiceClient, storageContainerNames));
			services.AddScoped<ITradeOrderService, TradeOrderService>();
			services.AddScoped<IRestrictedStockForClientService, RestrictedStockForClientService>();
			services.AddScoped<IRestrictedStockORGService, RestrictedStockORGService>();
			services.AddScoped<IBuyTradeIdeaService, BuyTradeIdeaService>();
			services.AddScoped<ISellTradeIdeaService, SellTradeIdeaService>();
			services.AddScoped<IBankReconciliationService, BankReconciliationService>();
			services.AddScoped<IHoldingReconService>(
				x => new HoldingReconService(
				x.GetRequiredService<IFileStorageService>(),
				x.GetRequiredService<IMapper>(),
			 	x.GetRequiredService<AlphaPortfolioDbContext>(),
			 	x.GetRequiredService<IHoldingReconRequestRepository>(),
				x.GetRequiredService<IRabbitMqService>(),
				tenantStorageConnectionString
			 ));
			services.AddScoped<IReportsService>(
				x => new ReportsService(
					x.GetRequiredService<IReportsRepository>(),
					x.GetRequiredService<IMapper>(),
					tenantStorageConnectionString,
					Configuration.GetValue<string>("ReportsConfigs:ReportBlobContainerName"),
					svcBusConnStr,
					Configuration.GetValue<string>("ReportsConfigs:ReportRequestQueue"),
					x.GetRequiredService<IRabbitMqReportsService>()
					));

			services.AddScoped<IDataUpdate>(
				x => new DataUpdate(
					x.GetRequiredService<IDataUpdateRepository>(),
					x.GetRequiredService<IMapper>(),
					tenantStorageConnectionString,
					Configuration.GetValue<string>("DataUpdateConfigs:DataUpdateFileContainerName"),
					svcBusConnStr,
					Configuration.GetValue<string>("DataUpdateConfigs:DataUpdateQueue"),
					x.GetRequiredService<IIncomeExpenseService>(),
					x.GetRequiredService<ICapitalRegisterService>()
					));

			services.AddScoped<IUserService, UserService>();
			services.AddScoped<IActlogicaAppRoleService, ActlogicaAppRoleService>();
			services.AddScoped<IPortfolioAnalyticsService, PortfolioAnalyticsService>();
			services.AddScoped<IPortfolioHoldingUpdateService, PortfolioHoldingUpdateService>();
			services.AddScoped<ICsvParserService, CsvParserService>();
			services.AddScoped<ISystematicDeploymentService, SystematicDeploymentService>();
			services.AddScoped<IEmailService>(x => new AzureEmailService(azureEmailConnectionString));
			services.AddScoped<ISendGridService>(x => new SendGridService(sendGridApiKey, sendGridApiUrl));
			services.AddScoped<IFeeTemplateService, FeeTemplateService>();
			services.AddScoped<IStrategyModelFeeTemplateService, StrategyModelFeeTemplateService>();
			services.AddScoped<IPortfolioFeeTemplateService, PortfolioFeeTemplateService>();
			services.AddScoped<IPortfolioFeeTriggeredLogsRepository, PortfolioFeeTriggeredLogsRepository>();
			services.AddScoped<IPortfolioFeeService, PortfolioFeeService>();
			services.AddScoped<IBankTransactionReconProcessService, BankTransactionReconProcessService>();
			services.AddScoped<IDashboardService, DashboardService>();
			services.AddScoped<IModelHoldingUpdateService, ModelHoldingUpdateService>();
			services.AddScoped<IPeakMarginReport, PeakMarginReport>();
			services.AddScoped<IPeakMarginLogRepository, PeakMarginLogRepository>();
			services.AddScoped<IMarketIndexDataStorageRepository, MarketIndexDataStorageRepository>();
			services.AddScoped<ITransformerIntegrationService>(
				options => new TransformerIntegrationService(options.GetService<IMapper>(), transformerApiUrl,
					options.GetRequiredService<IAlphaTransformerIntegrationRepository>()));

			var storageAccountKey = tenantStorageConnectionString.Split(";")[2].Split("=")[1];
			var tenantName = tenantStorageConnectionString.Split(";")[1].Split("=")[1].Replace("alphap", string.Empty);

			services.AddScoped<IPortfolioAnalyticsStorageRepository>(
				options => new PortfolioAnalyticsStorageRepository(options.GetService<IMapper>(), tenantName,
					storageAccountKey));
			services.AddScoped<IPortfolioPerformanceStorageRepository>(
				options => new PortfolioPerformanceStorageRepository(options.GetService<IMapper>(), tenantName,
					storageAccountKey));

			services.AddScoped<IStrategicDeploymentService, StrategicDeploymentService>();
			services.AddScoped<IReportsCronConfigRepository, ReportsCronConfigRepository>();
			services.AddScoped<IReportCronConfigService, ReportCronConfigService>();

			services.AddScoped<IAlphaNotificationService>(options => new AlphaNotificationService(emailService, blobServiceClient, sendGridService));

			services.AddSingleton<IAuthorizationHandler, QueryParameterHandler>();
			
			services.AddScoped<ICapitalRegisterService,CapitalRegisterService>();
			services.AddScoped<IDistributionCenterService,DistributionCenterService>();
			
			services.AddScoped<IComplianceRuleTypeRepository, ComplianceRuleTypeRepository>();
			services.AddScoped<IComplianceRuleSubTypeRepository, ComplianceRuleSubTypeRepository>();
			services.AddScoped<IComplianceRuleRepository, ComplianceRuleRepository>();
			services.AddScoped<ITradeComplianceService,TradeComplianceService>();
		
			services.AddScoped<IPortfolioReceivableService, PortfolioReceivableService>();
			
			services.AddScoped<IIncomeExpenseService,IncomeExpenseService>();
			
			services.AddSingleton<IRabbitMqService>(options => new RabbitMQService("alpha", "alpha", "alpharabbit.uat2.actlogica.com", tenantName, ""));
			services.AddSingleton<IRabbitMqReportsService>(options => new RabbitMQReportsService("alpha", "alpha", "alpharabbit.uat2.actlogica.com", tenantName + "/reports", ""));
			
			services.AddScoped<INdpmsService>(
				x => new NdpmsService(
					x.GetRequiredService<ISendGridService>(),
					x.GetRequiredService<IPortfolioRepository>(),
					x.GetRequiredService<IClientsRepository>(),
					x.GetRequiredService<IClientOrderEntryRepository>(),
					mapper,
					aesKey,
					aesIV,
					ndpmsUrl,
					tenantName
				));



			//services.AddScoped<ITradeOrderSettlementFileService, TradeOrderSettlementFileService>();
			//services.AddScoped<ITradeOrderSettlementFileEntryService, TradeOrderSettlementFileEntryService>();
			//services.AddScoped<ITradeSettlementLogService, TradeSettlementLogService>();
			services.AddScoped<ITransactionInputDraftService, TransactionInputDraftService>();
			services.AddScoped<ITransactionInputDraftRepository, TransactionInputDraftRepository>();
		}

		// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
		public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
		{
			//if (!env.IsDevelopment())
			//{
			app.UseDeveloperExceptionPage();
			app.UseSwagger();
			app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "Actlogica.AlphaPortfolios.Api v1"));
			//}
			app.UseDeveloperExceptionPage();
			app.UseHttpsRedirection();

			app.UseRouting();
			app.UseCors(MyAllowSpecificOrigins);
			app.UseAuthentication();
			app.UseAuthorization();
			app.UseEndpoints(endpoints =>
			{
				endpoints.MapControllers();
			});


		}
	}


}
