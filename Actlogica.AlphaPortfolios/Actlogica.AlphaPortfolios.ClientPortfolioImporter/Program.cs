﻿// See https://aka.ms/new-console-template for more information
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ClientPortfolioImporter;
using Actlogica.AlphaPortfolios.Data.Configs;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.OrderMgmt;
using Actlogica.AlphaPortfolios.Data.Repositories.MasterDataDb;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage;
using Actlogica.AlphaPortfolios.ServiceIntegration.Configs;
using Actlogica.AlphaPortfolios.ServiceIntegration.Imports.LegacyData;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using AutoMapper;

var mapper = new MapperConfiguration((Action<IMapperConfigurationExpression>)(mc =>
{
	mc.AddProfile((Profile)new DataMappingProfiles());
	mc.AddProfile((Profile)new ServiceMappingProfiles());
})).CreateMapper();
var masterDataDbConnStr = "Data Source=masterdatadbsvr-si.database.windows.net;Initial Catalog=marketdata-db-uat;Persist Security Info=False;User ID=productdbadmin;Password=***************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
var templateConnectionString = "Server=tcp:alphap-ci.database.windows.net,1433;Initial Catalog=<tenant>;Persist Security Info=False;User ID=alphap-db-user;Password=*******************$;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;";
var commonDataConnStr = "DefaultEndpointsProtocol=https;AccountName=finflocommondata;AccountKey=****************************************************************************************;";

Console.WriteLine("Hello! This console allows you to import an entire client PORTFOLIO data in bulk across multiple clients.");

Console.WriteLine("Start by selecting an environment. Please enter the name of the tenant. For Testing=testing, for UAT=sandbox for Production=rhpms:");
var tenantName = Console.ReadLine();

var dbConnectionString = templateConnectionString.Replace("<tenant>", tenantName);
var dbContext = new AlphaPortfolioDbContext(dbConnectionString);

var clientRepo = new ClientRepository(dbContext);
var strategyRepo = new StrategyRepository(dbContext);
var strategyModelRepo = new StrategyModelRepository(dbContext);
var portfolioCashLedgerRepo = new PortfolioCashLedgerRepository(dbContext);
var portfolioCapRegRepo = new PortfolioCapitalRegisterRepository(dbContext);
var portfolioRepo = new PortfolioRepository(dbContext);

var investmentRepo = new InvestmentRepository(dbContext);
var investmentTxnRepo = new InvestmentTransactionRepository(dbContext);
var mfInvestmentRepo = new MutualFundInvestmentRepository(dbContext);
var mfInvestmentTxn = new MutualFundTransactionRepository(dbContext);
var poolCapRegRepo = new PoolCapitalRegisterRepository(dbContext);
var poolCashRegRepo = new PoolCashLedgerRepository(dbContext);
var tradeOrderUnsettledAmountRepo = new TradeOrderUnsettledAmountsRepository(dbContext);

var modelPortfolioRepo = new ModelPortfolioRepository(dbContext);
var invstRepo = new InvestmentRepository(dbContext);
var invTxnRepo = new InvestmentTransactionRepository(dbContext);
var directEquityMasterDataRepo = new DirectEquityMasterDataRepository(masterDataDbConnStr);
var mfMasterRepo = new MutualFundMasterDataRepository(masterDataDbConnStr);
var bondMasterRepo = new BondMasterRepository(masterDataDbConnStr);
var tenantSecurityMasterRepo = new TenantSecurityMasterRepository(dbContext);
var portfolioSharingConfig = new PortfolioDistributorSharingRepository(dbContext);


var indexDataStorageSvc = new MarketIndexDataStorageRepository();
var securityMasterService = new SecurityMasterService("https://investa.actlogica.com/", "43fc59fb04234a26b7b166bc852b7672",
	bondMasterRepo, directEquityMasterDataRepo, mfMasterRepo, indexDataStorageSvc, dbContext);
var equityHistoryRepo = new EquityHistoryRepository();
var portfolioService = new PortfolioService(mapper, invstRepo, investmentTxnRepo,
	mfInvestmentRepo, mfInvestmentTxn, poolCapRegRepo, poolCashRegRepo, portfolioCapRegRepo,
	portfolioCashLedgerRepo, portfolioRepo, modelPortfolioRepo, securityMasterService, tradeOrderUnsettledAmountRepo, clientRepo, portfolioSharingConfig);
var generalSettingRepo = new GeneralSettingRepository(dbContext);
var portfolioReceivableRepo = new PortfolioReceivableRepository(dbContext);

var migrationSettings = new MigrationEngineSettings();

try
{
	//Console.WriteLine("Please select the migration job type\n");
	//Console.WriteLine("1. All Data = Capital Register, Income/Expenses, Security Transactions and Building of ledger.\n");
	//Console.WriteLine("2. No ledger = Capital Register, Income/Expenses, Security Transactions WITHOUT Building of ledger.\n");
	//Console.WriteLine("3. Bank book based = Capital Register, Security Transactions and Bank Book as ledger.\n");
	//migrationSettings.MigrationJobTypeSelected = Enum.Parse<MigrationJobType> (Console.ReadLine(), true);

	var isMigratingSecurityTxnsOnly = false;
	Console.WriteLine("Do you want to migrate just transactions? This is only available when migrating nothing but transactions. NO LEDGER CHANGES WILL BE MADE\n");
	Console.WriteLine("Please type the \"Yes\" to confirm that you want to migrate transactions only..\n");
	var migratingTxnsOnly = Console.ReadLine();
	if (migratingTxnsOnly.ToLower() == "yes")
	{
		Console.WriteLine("\nPlease type the password to continue");
		var piyushSpecificPassword = Console.ReadLine();
		if (piyushSpecificPassword != "T]EIaAgU&KG-I{S")
		{
			Console.WriteLine("\n\nInvalid manager password, please ask Piyush for a password.");
			Console.WriteLine("\nInvalid input. Process aboring...");
			Console.WriteLine("\nEnter any key to shut down...");
			Console.ReadLine();
			return;
		}
		else
		{
			isMigratingSecurityTxnsOnly = true;
		}
	}

	var runCorpActions = true;
	Console.WriteLine("\nAre you running this to migrate or BAU. Please select:");
	Console.WriteLine("\n1 - for historical migrations.");
	Console.WriteLine("\n2 - for post migration daily FA activity.");
	Console.WriteLine("\nNOTE: On option 2, Corporate actions are NOT run.");
	if(Console.ReadLine().ToLower() == "2")
	{
		runCorpActions = false;
	}

	Console.WriteLine("\nCorporate Actions Selection");
	var corpActionsEndDate = DateTime.Today;
	if(runCorpActions)
	{
		Console.WriteLine($"\nYou have selected option to run the engine for Historical Migrations, note that Corporate Actions will be executed: " +
			$"Hit enter to continue or Close the console to exit.");
		Console.ReadLine();


		Console.WriteLine($"Please enter the migration phase AsAt Date which is what the engine will limit the CorpActions run till.");
		Console.WriteLine($"\nPlease enter the Year - YYYY");
		var year = Console.ReadLine();
		
		Console.WriteLine($"\nPlease enter the month - MM");
		var month = Console.ReadLine();
		
		Console.WriteLine($"\nPlease enter the day - DD");
		var day = Console.ReadLine();
		corpActionsEndDate = new DateTime(int.Parse(year), int.Parse(month), int.Parse(day));
	}
	else
	{
		Console.WriteLine($"\nYou have selected option to run the engine for migrating daily FA activity, note that Corporate Actions WILL NOT BE executed: " +
			$"Hit enter to continue or Close the console to exit.");
		Console.ReadLine();
	}

	Console.WriteLine("\nPlease paste the path of the folder to import data from");
	Console.WriteLine("\nNote: Only XLSX files are accepted!");
	var rootDirectory = Console.ReadLine();
	if (string.IsNullOrEmpty(rootDirectory))
		return;

	var foldersInDirectory = Directory.GetDirectories(rootDirectory).Order();
	Console.WriteLine($"{foldersInDirectory.Count()} folders found.");
	foldersInDirectory.ToList().ForEach(folder => Console.WriteLine($"{folder}"));
	Console.WriteLine("Please type \"Yes\" and hit enter key to start the migration process.");
	var proceedYesOrNo = Console.ReadLine();

	foreach (var lotFolder in foldersInDirectory)
	{
		try
		{

			Console.WriteLine($"Reading files from directory - {lotFolder}.");
			var filesInDirectory = Directory.GetFiles(lotFolder);
			Console.WriteLine($"{filesInDirectory.Count()} files found.");
			filesInDirectory.ToList().ForEach(file => Console.WriteLine($"{file}"));


			if (string.IsNullOrWhiteSpace(proceedYesOrNo) || proceedYesOrNo.ToLower() != "yes")
			{
				Console.WriteLine("Invalid input. Process aborting...");
				Console.WriteLine("Enter any key to shut down...");
				Console.ReadLine();
				return;
			}

			Console.WriteLine("===========================================================================================================================");
			Console.WriteLine("Reading bulk capital register file.");

			var capitalRegisterFilePath = filesInDirectory.Where(file => file.Contains("CapitalRegisterImport-Input")).FirstOrDefault();
			if (capitalRegisterFilePath == null)
			{
				Console.WriteLine("Capital Register input template not found in directory. Process aborting...");
				Console.WriteLine("Enter any key to shut down...");
				Console.ReadLine();
				return;
			}

			var capitalRegisterImporter = new CapitalRegisterImporter(clientRepo, strategyRepo, portfolioCapRegRepo, portfolioCashLedgerRepo, strategyModelRepo, portfolioRepo, modelPortfolioRepo);
			var mappedCapRegTxns = await capitalRegisterImporter.LoadDataFromFile(capitalRegisterFilePath);

			Console.WriteLine($"Capital register file read successfully completed.");
			Console.WriteLine("-----------------------------------------------------------------------------------------------");

			Console.WriteLine("Reading bulk income and expenses file.");
			var incomeExpenseFilePath = filesInDirectory.Where(file => file.Contains("IncomeAndExpense") && file.Contains("Import-Input")).FirstOrDefault();
			if (incomeExpenseFilePath == null)
			{
				Console.WriteLine("Income & Expenses input template not found in directory. Process aborting...");
				Console.WriteLine("Enter any key to shut down...");
				Console.ReadLine();
				return;
			}
			var incomeExpenseImporter = new IncomeAndExpensesImporter(clientRepo, strategyRepo, portfolioCashLedgerRepo, strategyModelRepo, portfolioRepo, modelPortfolioRepo);
			var mappedLedgerTxns = await incomeExpenseImporter.LoadDataFromFile(incomeExpenseFilePath);

			Console.WriteLine($"Income expenses file read successfully completed.");
			Console.WriteLine("-----------------------------------------------------------------------------------------------");

			Console.WriteLine("Reading bulk transactions file.");

			var transactionsFilePath = filesInDirectory.Where(file => file.Contains("TransactionImport-Input")).FirstOrDefault();
			if (transactionsFilePath == null)
			{
				Console.WriteLine("Transactions input template not found in directory. Process aborting...");
				Console.WriteLine("Enter any key to shut down...");
				Console.ReadLine();
				return;
			}

			Console.WriteLine("Reading Direct Equity transactions from file.");
			//Direct equity transactions import
			var directEquityTxnImporter = new DirectEquityTransactionImporter(clientRepo, portfolioCashLedgerRepo, portfolioCapRegRepo, mapper, equityHistoryRepo,
				portfolioRepo, investmentRepo, investmentTxnRepo, strategyRepo, strategyModelRepo, modelPortfolioRepo, generalSettingRepo, portfolioSharingConfig);
			var mappedDirectEquityTxns = await directEquityTxnImporter.LoadDataFromFile(transactionsFilePath, securityMasterService, tenantSecurityMasterRepo);
			if (mappedDirectEquityTxns != null)
			{
				await directEquityTxnImporter.CheckDataIntegrity(mappedDirectEquityTxns, transactionsFilePath.Replace("-Input", "-Validated-DirectEquity-Output"));
			}

			Console.WriteLine($"Direct Equity file read successfully completed.");
			Console.WriteLine("-----------------------------------------------------------------------------------------------");

			Console.WriteLine("Reading Mutual Fund transactions from file.");
			//Mutual fund transactions import
			var mfTxnImporter = new MutualFundTransactionImporter(clientRepo, portfolioCashLedgerRepo, portfolioCapRegRepo,
				portfolioRepo, investmentRepo, investmentTxnRepo, strategyRepo, strategyModelRepo, modelPortfolioRepo);
			var mappedMutualFundTxns = await mfTxnImporter.LoadDataFromFile(transactionsFilePath, securityMasterService);
			await mfTxnImporter.CheckDataIntegrity(mappedMutualFundTxns, transactionsFilePath.Replace("-Input", "-Validated-MutualFunds-Output"));

			Console.WriteLine($"Mutual Fund file read successfully completed.");
			Console.WriteLine("-----------------------------------------------------------------------------------------------");

			Console.WriteLine("Reading Bond transactions from file.");
			//Bond transactions import
			var bondTxnImporter = new BondTransactionImporter(clientRepo, portfolioCashLedgerRepo, portfolioCapRegRepo,
				portfolioRepo, investmentRepo, investmentTxnRepo, strategyRepo, strategyModelRepo, modelPortfolioRepo);
			var mappedBondTxns = await bondTxnImporter.LoadDataFromFile(transactionsFilePath, securityMasterService, tenantSecurityMasterRepo);
			await bondTxnImporter.CheckDataIntegrity(mappedBondTxns, transactionsFilePath.Replace("-Input", "-Validated-Bonds-Output"));

			Console.WriteLine($"Bond file read successfully completed.");
			Console.WriteLine("-----------------------------------------------------------------------------------------------");

			var clientsToSkip = GetClientCodesToSkip();

			var apClientCodesInFile = mappedDirectEquityTxns.GroupBy(crTxn => crTxn.APClientCode).Select(client => client.Key).ToList();
			apClientCodesInFile.AddRange(mappedMutualFundTxns.GroupBy(crTxn => crTxn.APClientCode).Select(client => client.Key).ToList());
			apClientCodesInFile.AddRange(mappedBondTxns.GroupBy(crTxn => crTxn.APClientCode).Select(client => client.Key).ToList());
			apClientCodesInFile.AddRange(mappedCapRegTxns.GroupBy(crTxn => crTxn.APClientCode).Select(client => client.Key).ToList());
			apClientCodesInFile.AddRange(mappedLedgerTxns.GroupBy(crTxn => crTxn.APClientCode).Select(client => client.Key).ToList());
			
			var apClientCodes = apClientCodesInFile.Distinct();

			Console.WriteLine($"Beginning import data for {apClientCodes.Count()} clients at {DateTime.Now}.");
			var watch = new System.Diagnostics.Stopwatch();
			int importClientNumber = 0;
			foreach (var apClientCode in apClientCodes)
			{
				watch.Start();
				importClientNumber++;
				if (clientsToSkip.Contains(apClientCode))
				{
					Console.WriteLine($"*************************************************************************");
					Console.WriteLine($"Skipping client {apClientCode}");
					Console.WriteLine($"*************************************************************************");
					continue;
				}
				
				var allClientStrategyCodes = mappedCapRegTxns.Where(ap => ap.APClientCode == apClientCode).GroupBy(cg => cg.ClientCode).Select(g => g.Key).ToList();
				allClientStrategyCodes.AddRange(mappedLedgerTxns.Where(ap => ap.APClientCode == apClientCode).GroupBy(cg => cg.ClientCode).Select(g => g.Key));
				allClientStrategyCodes.AddRange(mappedDirectEquityTxns.Where(ap => ap.APClientCode == apClientCode).GroupBy(cg => cg.ClientCode).Select(g => g.Key));
				allClientStrategyCodes.AddRange(mappedMutualFundTxns.Where(ap => ap.APClientCode == apClientCode).GroupBy(cg => cg.ClientCode).Select(g => g.Key));
				allClientStrategyCodes.AddRange(mappedBondTxns.Where(ap => ap.APClientCode == apClientCode).GroupBy(cg => cg.ClientCode).Select(g => g.Key));

				var uniqueClientStrategyCodes = allClientStrategyCodes.Distinct();
				foreach (var clientStrategyCode in uniqueClientStrategyCodes)
				{
					Console.WriteLine($"Starting ClientStrategyCode: {clientStrategyCode} for {importClientNumber} out of {apClientCodes.Count()} clients at {DateTime.Now}.");
					using (var transaction = await dbContext.Database.BeginTransactionAsync())
					{
						try
						{
							var ledgerEntriesForMigration = new List<Actlogica.AlphaPortfolios.Data.Entity.Db.PortfolioCashLedger>();

							Console.WriteLine("=============================================");
							Console.WriteLine($"Beginning Capital Register transactions import for Client Strategy Code {clientStrategyCode}.");
							if (mappedCapRegTxns.Where(txn => txn.ClientCode == clientStrategyCode).Count() > 0)
							{
								ledgerEntriesForMigration.AddRange(
									await capitalRegisterImporter.ImportTransactions(mappedCapRegTxns.Where(txn => txn.ClientCode == clientStrategyCode)));
							}
							Console.WriteLine($"Capital register import complete. Imported {ledgerEntriesForMigration.Count} records.");
							Console.WriteLine("-----------------------------------------------");

							Console.WriteLine($"Beginning Income/Expenses transactions import for Client Strategy Code {clientStrategyCode}.");
							if (mappedLedgerTxns.Where(txn => txn.ClientCode == clientStrategyCode).Count() > 0 && !isMigratingSecurityTxnsOnly)
							{
								ledgerEntriesForMigration.AddRange(
									await incomeExpenseImporter.ImportTransactions(mappedLedgerTxns.Where(txn => txn.ClientCode == clientStrategyCode)));
							}
							Console.WriteLine($"Income & expenses import complete. Imported {ledgerEntriesForMigration.Count} records.");
							Console.WriteLine("-----------------------------------------------");

							Console.WriteLine($"Beginning DirectEquity transactions import for Client Strategy Code {clientStrategyCode}.");
							ledgerEntriesForMigration.AddRange(
								await directEquityTxnImporter.ImportTransactions(mappedDirectEquityTxns.Where(txn => txn.ClientCode == clientStrategyCode),
								commonDataConnStr, securityMasterService, isMigratingSecurityTxnsOnly, runCorpActions, corpActionsEndDate));

							//await directEquityTxnImporter.RunCorporateActionsForSpecificIsin("INE919P01029", "79edb34114ba4c3cb4549d61b0150e20", "0fe8dd0fee4b4ef69dd7b7340adda921", commonDataConnStr);
							Console.WriteLine($"Direct Equity import complete. Imported {mappedDirectEquityTxns.Where(txn => txn.ClientCode == clientStrategyCode).Count()} records (Excluding corporate actions).");
							Console.WriteLine("---------------------------------------------");

							Console.WriteLine($"Beginning MutualFunds transactions import for Client Strategy Code {clientStrategyCode}.");
							ledgerEntriesForMigration.AddRange(
								await mfTxnImporter.ImportTransactions(mappedMutualFundTxns.Where(txn => txn.ClientCode == clientStrategyCode), commonDataConnStr));
							Console.WriteLine($"Mutual Fund import complete. Imported {mappedMutualFundTxns.Where(txn => txn.ClientCode == clientStrategyCode).Count()} records.");
							Console.WriteLine("---------------------------------------------");

							Console.WriteLine($"Beginning Bonds transactions import for Client Strategy Code {clientStrategyCode}.");
							ledgerEntriesForMigration.AddRange(
								await bondTxnImporter.ImportTransactions(mappedBondTxns.Where(txn => txn.ClientCode == clientStrategyCode), commonDataConnStr));
							Console.WriteLine($"Bonds import complete. Imported {mappedBondTxns.Where(txn => txn.ClientCode == clientStrategyCode).Count()} records.");
							Console.WriteLine("---------------------------------------------");

							Console.WriteLine($"Beginning CashLedger transactions import for Client Strategy Code {clientStrategyCode}.");

							if (!ledgerEntriesForMigration.Any() && isMigratingSecurityTxnsOnly)
              {
                await transaction.CommitAsync();
                Console.WriteLine($"No ledger entries for Client Strategy Code {clientStrategyCode}. Skipping....");
								continue;
							}

              var portfolio = await portfolioRepo.GetByClientStrategyCode(clientStrategyCode);
              var saleReceivables = new List<PortfolioReceivable>();
              if (!runCorpActions && mappedDirectEquityTxns.Any(txn => txn.ClientCode == clientStrategyCode && txn.TransactionType == $"{TransactionType.Sell}"))
							{
								var txnBaseClass = new TransactionImporterBase();
								if (portfolio == null)
									throw new InvalidOperationException($"Portfolio with Client Strategy Code: {clientStrategyCode} not found!");
								foreach(var investmentIsin in mappedDirectEquityTxns
										.Where(txn => txn.ClientCode == clientStrategyCode && txn.TransactionType == $"{TransactionType.Sell}").GroupBy(txn => txn.Isin))
								{
									var investment = await investmentRepo.GetByIsinInPortfolio(investmentIsin.Key, portfolio.Id);

									if (investment == null)
										continue;

									var investmentTxnsFromDb = await investmentTxnRepo.GetTransactionsInInvestment(investment.Id);

									foreach (var deTxn in investmentIsin.ToList().Where(txn => txn.TransactionType == $"{TransactionType.Sell}"))
									{
										var thisInvestmentTransactionFromDb = investmentTxnsFromDb.FirstOrDefault(
											txn => txn.Type == deTxn.TransactionType && txn.SubType == deTxn.TransactionSubType
											&& txn.TransactionDate == Convert.ToDateTime(deTxn.TransactionDate) && txn.Quantity == deTxn.Quantity
											&& txn.SttAmount == deTxn.SttAmount);
										var sellReceivable = new PortfolioReceivable
										{
											ClientId = portfolio.ClientId,
											PortfolioId = portfolio.Id,
											InvestmentId = investment.Id,
											Isin = investment.Isin,
											Symbol = investment.Symbol,
											InvestmentName = investment.Name,
											Exchange = deTxn.Exchange,
											CorporateActionType = string.Empty,
											TransactionType = $"{TransactionType.Credit}",
											TransactionSubType = $"{TransactionSubType.Sell}",
											TransactionDate = Convert.ToDateTime(deTxn.TransactionDate),
											SettlementDate = new DateTime(1900, 01, 01),
											CgtDate = Convert.ToDateTime(deTxn.TransactionDate),
											Amount = deTxn.TransactionAmount + deTxn.SttAmount,
											Quantity = deTxn.Quantity,
											Price = txnBaseClass.GetTransactionPrice(deTxn),
											ReceivableStatus = $"{ReceivableStatusType.Hold}",
											Remarks = $"{deTxn.TransactionType}|{investment.Name}|{investment.Id}|{deTxn.Isin}"
										};

										var sttPayable = new PortfolioReceivable
										{
											ClientId = portfolio.ClientId,
											PortfolioId = portfolio.Id,
											InvestmentId = investment.Id,
											Isin = "CASH",
											Symbol = "CASH",
											InvestmentName = "CASH",
											Exchange = "DIR",
											CorporateActionType = string.Empty,
											TransactionType = $"{TransactionType.Debit}",
											TransactionSubType = $"{TransactionSubType.Stt}",
											TransactionDate = Convert.ToDateTime(deTxn.TransactionDate),
											SettlementDate = new DateTime(1900, 01, 01),
											CgtDate = Convert.ToDateTime(deTxn.TransactionDate),
											Amount = deTxn.SttAmount,
											Quantity = 1.0,
											Price = 1.0,
											ReceivableStatus = $"{ReceivableStatusType.Hold}",
											Remarks = $"Stt|{investment.Name}|{investment.Id}|{deTxn.Isin}"
										};

										saleReceivables.Add(sellReceivable);
										saleReceivables.Add(sttPayable);
									}
								}
							}

              if (ledgerEntriesForMigration.Any())
              {
                var incomeExpensePayables = ledgerEntriesForMigration.Select(a => new PortfolioReceivable
                {
                  ClientId = portfolio.ClientId,
                  PortfolioId = portfolio.Id,
                  InvestmentId = "CASH",
                  Isin = "CASH",
                  Symbol = "CASH",
                  InvestmentName = "CASH",
                  Exchange = "DIR",
                  CorporateActionType = string.Empty,
                  TransactionType = a.TransactionType,
                  TransactionSubType = a.TransactionSubType,
                  TransactionDate = Convert.ToDateTime(a.TransactionDate),
                  SettlementDate = (a.SettlementDate == DateTime.MinValue) ? new DateTime(1900,01,01) : a.SettlementDate,
                  CgtDate = Convert.ToDateTime(a.TransactionDate),
                  Amount = a.Amount,
                  Quantity = a.Amount,
                  Price = 1.0,
                  ReceivableStatus = (a.SettlementDate.Date == DateTime.MinValue || a.SettlementDate.Date ==new DateTime(1900,01,01)) 
									? $"{ReceivableStatusType.Hold}" :
                  ((a.TransactionType == TransactionType.Credit.ToString()) ? $"{ReceivableStatusType.Received}":$"{ReceivableStatusType.Paid}"),
                  Remarks = a.Description
                }).ToList();

                saleReceivables.AddRange(incomeExpensePayables);
              }

							if (saleReceivables.Any())
							{
								await portfolioReceivableRepo.BulkInsert(saleReceivables);
								await portfolioReceivableRepo.Commit();
							}

              if (!isMigratingSecurityTxnsOnly && ledgerEntriesForMigration.Any())
							{
								if(!runCorpActions)
								{
									ledgerEntriesForMigration.RemoveAll(ldg => ldg.TransactionSubType == $"{TransactionSubType.Sell}");
								}

                var ledgerEntries = ledgerEntriesForMigration.Where(a => DateTime.MinValue.Date != a.SettlementDate.Date && a.SettlementDate.Date != new DateTime(1900,01,01).Date).ToList();

                if (ledgerEntries.Any())
								{
									var ledgerBuilder = new PortfolioLedgerBuilder(ledgerEntries.FirstOrDefault().PortfolioId,
										portfolioCashLedgerRepo, portfolioService);
                  await ledgerBuilder.CalculateAndBuildRunningBalance(ledgerEntries);
								}
								Console.WriteLine($"Cash ledger import complete. Imported {ledgerEntriesForMigration.Count()} records.");
							}

							await transaction.CommitAsync();
							Console.WriteLine($"Import completed successfully for AP Client Code {clientStrategyCode}.");
							Console.WriteLine("=============================================");

						}
						catch (Exception ex)
						{
							Console.WriteLine($"Import for {clientStrategyCode} as errored. Rolled back this client data import. Exception: {ex.Message}");
							Console.WriteLine($"Stack Trace {ex.StackTrace}");
							Console.WriteLine("If you wish to continue, hit the enter button on your keyboard or close the console.");
							Console.ReadLine();
							await transaction.RollbackAsync();
						}
					}

				}

			}
			watch.Stop();
			Console.WriteLine($"Import completed successfully for {apClientCodes.Count()} clients in {watch.ElapsedMilliseconds / 1000}s. You may now close the console.");
			Console.WriteLine("===========================================================================================================================");

			Console.WriteLine("Please enter key to exit the program.");
			Console.ReadLine();
		}
		catch(Exception ex)
		{
			Console.WriteLine($"Unexpected error occured while running lot {lotFolder}. \n\nSee Exception message for more details: {ex.Message}");
			Console.WriteLine($"Processing of lot aborting... - Exception Stack Trace: {ex.StackTrace}");
			Console.WriteLine("If you wish to stop the process any further, please close the console.");

			Console.WriteLine("Enter any key to skip and continue... You will need to clear all data in this lot and rerun");
			Console.ReadLine();
		}
	}
}
catch (Exception ex)
{
	Console.WriteLine($"Unexpected error occured. See Exception message for more details: {ex.Message}");
	Console.WriteLine($"Process aborting... - Exception Stack Trace: {ex.StackTrace}");
	Console.WriteLine("Enter any key to shut down...");
	Console.ReadLine();
}


static List<string> GetClientCodesToSkip()
{
	var apClientCodesToSkip = new List<string>();
	apClientCodesToSkip.Add("");

	return apClientCodesToSkip;
}