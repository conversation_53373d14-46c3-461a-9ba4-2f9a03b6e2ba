using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage.PerformanceEngine;
using Actlogica.AlphaPortfolios.ServiceIntegration.PerformanceEngine.Portfolio;
using Actlogica.AlphaPortfolios.Utils.Double;
using AutoMapper;
using Newtonsoft.Json;
using RestSharp;


namespace Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.PortfolioSummaryReport
{
    public class PortfolioSummaryReport : BaseReportGenerationEngine, IPortfolioSummaryReport
    {
        private readonly IPortfolioRepository _portfolioRepository;
        private readonly IClientsRepository _clientRepository;
        private readonly IGeneralSettingRepository _generalSettingRepository;
        private readonly string _v2PerfApi;
        private readonly PortfolioPerformanceStorageRepository _portfolioPerformanceStorageRepository;
        private readonly PortfolioAnalyticsStorageRepository _portfolioAnalyticsStorageRepository;
        private readonly string _tenantName;
        private readonly IMapper _mapper;
        private readonly AlphaPortfolioDbContext _dbContext;
        private readonly PortfolioCashLedgerRepository _portfolioCashLedgerRepository;
        private readonly PortfolioReceivableRepository _portfolioReceivableRepository;


        public PortfolioSummaryReport(IPortfolioRepository portfolioRepo, IClientsRepository clientRepo, IGeneralSettingRepository generalSettingRepository,
        string v2PerfApi, PortfolioPerformanceStorageRepository portfolioPerformanceStorageRepository, PortfolioAnalyticsStorageRepository portfolioAnalyticsStorageRepository, IMapper mapper,
        AlphaPortfolioDbContext alphaPortfolioDbContext, PortfolioCashLedgerRepository portfolioCashLedgerRepository, PortfolioReceivableRepository portfolioReceivableRepository)
        {
            _portfolioRepository = portfolioRepo;
            _clientRepository = clientRepo;
            _generalSettingRepository = generalSettingRepository;
            _v2PerfApi = v2PerfApi;
            _dbContext = alphaPortfolioDbContext;
            _portfolioAnalyticsStorageRepository = portfolioAnalyticsStorageRepository;
            _mapper = mapper;
            var settings = Task.Run(() => _generalSettingRepository.GetAll()).Result ?? throw new InvalidOperationException("Storage key not configured for tenant");
            var storageAccountKey = settings.FirstOrDefault(s => s.Key == "StorageAccountKey") ?? throw new KeyNotFoundException("No Storage Account Key Found");
            var tenantClaim = settings.FirstOrDefault(s => s.Key == "TenantName") ?? throw new KeyNotFoundException("No Tenant Name Key Found");
            _tenantName = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
            _portfolioPerformanceStorageRepository = portfolioPerformanceStorageRepository;
            _portfolioReceivableRepository = portfolioReceivableRepository;
            _portfolioCashLedgerRepository = portfolioCashLedgerRepository;
        }

        public async Task<RptPortfolioSummaryObjects> GetRptPortfolioSummary(string portfolioId, DateTime fromDate, DateTime toDate)
        {
            var portfolio = await _portfolioRepository.GetById(portfolioId) ?? throw new InvalidOperationException("Portfolio not found.");
            var client = await _clientRepository.GetClientById(portfolio.ClientId);

            var pmsHouseNameSetting = await _generalSettingRepository.GetByKey("PmsHouseName");
            var pmsHouseLogoSetting = await _generalSettingRepository.GetByKey("Logo");

            var transactionsList = new RptPortfolioSummaryTransaction();


            var rptPortfolioSummary = new RptPortfolioSummaryObjects()
            {
                PortfolioStartDate = portfolio.StartDate.ToString(),
                ClientName = $"{client.FirstName} {client.LastName}",
                ClientStrategyCode = portfolio.ClientStrategyCode,
                AccountNumber = portfolio.FAAccountNo,
                ReportType = "Portfolio Performance Summary",
                PortfolioName = portfolio.Name,
                PortfolioCode = portfolio.ClientStrategyCode,
                FromDate = fromDate.ToString("dd MM yyyy"),
                ToDate = toDate.ToString("dd MM yyyy"),
                ReportGenerationDate = DateTime.Today.ToString("dd MM yyyy"),
                PmsHouseName = pmsHouseNameSetting?.Value ?? "PMS Private Limited",
                Logo = pmsHouseLogoSetting?.Value ?? String.Empty,
                ClientUniqueCode = portfolio.ClientStrategyCode
            };

            // var getPortfolioUrl = $"/portfolio/get_portfolio/{portfolioId}";
            // var benchmarkUrl = $"/portfolio/benchmark/{portfolioId}";
            // var analyticsUrl = $"/portfolio/analytics/{portfolioId}";

            // var restClientclient = new RestClient(_v2PerfApi);

            // var getPortfolioRequest = new RestRequest(getPortfolioUrl, Method.GET)
            //     .AddQueryParameter("fromDate", fromDate.ToString("yyyy-MM-dd"))
            //     .AddQueryParameter("toDate", toDate.ToString("yyyy-MM-dd"))
            //     .AddQueryParameter("benchmarkIndex", "Nifty50");

            // var benchmarkRequest = new RestRequest(benchmarkUrl, Method.GET)
            //     .AddQueryParameter("fromDate", fromDate.ToString("yyyy-MM-dd"))
            //     .AddQueryParameter("toDate", toDate.ToString("yyyy-MM-dd"))
            //     .AddQueryParameter("benchmarkIndex", "Nifty50");

            // var analyticsRequest = new RestRequest(analyticsUrl, Method.GET)
            //     .AddQueryParameter("date", toDate.ToString("yyyy-MM-dd"));

            // var benchmarkResponse = await restClientclient.ExecuteAsync(benchmarkRequest);
            // var analyticsResponse = await restClientclient.ExecuteAsync(analyticsRequest);
            // var getPortfolioResponse = await restClientclient.ExecuteAsync(getPortfolioRequest);

            // var benchmarkResult = JsonConvert.DeserializeObject<List<ApiContracts.Reports.BenchmarkData>>(benchmarkResponse.Content);
            // var analyticsResult = JsonConvert.DeserializeObject<ApiContracts.Reports.AnalyticsResponse>(analyticsResponse.Content);
            // var investmentsResuls = JsonConvert.DeserializeObject<List<InvestmentEntry>>(getPortfolioResponse.Content);

            await PortfolioPerformanceEngine.CalculatePerformance(_dbContext, _tenantName, _mapper, portfolioId, toDate);

            var portfolioCashLedgers = await _portfolioCashLedgerRepository.GetTransationsBetweenDates(portfolioId, fromDate, toDate);

            var analyticsFromDate= await _portfolioAnalyticsStorageRepository.GetAnalyticsForTheDay(portfolioId, fromDate);
            var analyticsToDate = await _portfolioAnalyticsStorageRepository.GetAnalyticsForTheDay(portfolioId, toDate);

            var portfolioAnalyticFromDate = analyticsFromDate.FirstOrDefault(a => a.AggregateType == "Portfolio") ?? new ApiContracts.Portfolios.PortfolioAnalytics();
            var portfolioAnalyticTodate = analyticsToDate.FirstOrDefault(a => a.AggregateType == "Portfolio") ?? new ApiContracts.Portfolios.PortfolioAnalytics();

            var portfolioPerformanceChartDataFromDate = await _portfolioPerformanceStorageRepository.GetPerformanceDataForTheDay(portfolioId, fromDate);
            var portfolioPerformanceChartDataToDate = await _portfolioPerformanceStorageRepository.GetPerformanceDataForTheDay(portfolioId, toDate);

            var incomeTxns = portfolioCashLedgers.Where(txn => txn.TransactionType == TransactionType.Credit.ToString() &&
             (txn.TransactionSubType == TransactionSubType.Interest.ToString() || txn.TransactionSubType == TransactionSubType.Dividend.ToString()
             || txn.TransactionSubType == TransactionSubType.Other.ToString())).Sum(txn => txn.Amount);

            var feeTxns = portfolioCashLedgers.Where(txn => txn.TransactionSubType == TransactionSubType.Fees.ToString() || txn.TransactionSubType == TransactionSubType.PerformanceFees.ToString() || txn.TransactionSubType == TransactionSubType.ExitLoad.ToString()).Sum(txn => txn.Amount);

            var portfolioReceivables = await _portfolioReceivableRepository.GetByPortfolioId(portfolioId);
            var accruedIncome = portfolioReceivables.Where(t => t.TransactionType == TransactionType.Credit.ToString() && t.SettlementDate > toDate
                                && t.TransactionDate <= toDate).Sum(x => x.Amount);

            var expenseTxns = portfolioCashLedgers.Where(txn => txn.TransactionType == TransactionType.Debit.ToString() &&
             (txn.TransactionSubType == TransactionSubType.Charges.ToString() || txn.TransactionSubType == TransactionSubType.Stt.ToString()
             || txn.TransactionSubType == TransactionSubType.Other.ToString())).Sum(txn => txn.Amount);

            var transactions = new RptPortfolioSummaryTransaction
            {
                MarketValueFromDate = portfolioAnalyticFromDate.MarketValue.GetRptValue("1", 2),
                CapitalInOut = (portfolioAnalyticTodate.InvestedCapital - portfolioAnalyticFromDate.InvestedCapital).GetRptValue("1", 2),
                RealizedGain = (portfolioAnalyticTodate.RealisedGainLoss - portfolioAnalyticFromDate.RealisedGainLoss).GetRptValue("1", 2),
                UnrealizedGain = (portfolioAnalyticTodate.UnRealisedGainLoss - portfolioAnalyticFromDate.UnRealisedGainLoss).GetRptValue("1", 2),
                GainPriorToTakeOver = "0",
                Income = (portfolioAnalyticTodate.DividendPaid - portfolioAnalyticFromDate.DividendPaid + incomeTxns).GetRptValue("1", 2),
                Fees = feeTxns.GetRptValue("1", 2),
                Expenses = expenseTxns.GetRptValue("1", 2),
                AccruedIncome = accruedIncome.GetRptValue("1", 2),
                MarketValueToDate = portfolioAnalyticTodate.MarketValue.GetRptValue("1", 2),
                AbsolutePortfolioRateOfReturn = analyticsToDate.FirstOrDefault().AbsoluteReturnPercentage.ToString(),
                AbsoluteBenchmarkOne = portfolioPerformanceChartDataToDate.FirstOrDefault().BenchmarkTwrr1Y.ToString(),
                AnnualizedPortfolioRateOfReturn = portfolioPerformanceChartDataToDate.FirstOrDefault().Xirr1Y.ToString(),
                AnnualizedBenchmarkOne = portfolioPerformanceChartDataToDate.FirstOrDefault().BenchmarkXirr1Y.ToString(),
            };

            rptPortfolioSummary.Transactions = transactions;


            return rptPortfolioSummary;

        }

    }

}