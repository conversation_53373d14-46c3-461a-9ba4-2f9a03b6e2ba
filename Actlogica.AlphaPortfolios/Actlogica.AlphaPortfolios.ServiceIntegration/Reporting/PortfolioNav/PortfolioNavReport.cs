using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.HistoricalData;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage.PerformanceEngine;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.BankBook;
using Actlogica.AlphaPortfolios.Utils.Double;
using AutoMapper;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.PortfolioNavReport
{
    public class PortfolioNavReport : BaseReportGenerationEngine, IPortfolioNavReport
    {
        private readonly IPortfolioRepository _portfolioRepo;
        private readonly IClientsRepository _clientRepo;
        private readonly IMapper _mapper;
        private readonly IGeneralSettingRepository _generalSettingRepository;

        private readonly Data.Repositories.Storage.PerformanceEngine.PortfolioAumStorageRepository _portfolioAumService;

        public PortfolioNavReport(
            IClientsRepository clientRepo,
            IMapper mapper,
            IGeneralSettingRepository generalSettingRepository,
            IPortfolioRepository portfolioRepository
            )
        {
            _portfolioRepo = portfolioRepository;
            _clientRepo = clientRepo;
            _mapper = mapper;
            _generalSettingRepository = generalSettingRepository;
            var settings = Task.Run(() => _generalSettingRepository.GetAll()).Result ?? throw new InvalidOperationException("Storage key not configured for tenant");
            var storageAccountKey = settings.FirstOrDefault(s => s.Key == "StorageAccountKey") ?? throw new KeyNotFoundException("No Storage Account Key Found");
            var tenantClaim = settings.FirstOrDefault(s => s.Key == "TenantName") ?? throw new KeyNotFoundException("No Tenant Name Key Found");
            var tenantValue = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
            _portfolioAumService = new PortfolioAumStorageRepository(mapper, tenantValue, storageAccountKey.Value);
        }


        public async Task<byte[]> ExportToExcel(RptPortfolioNav reportData)
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var excelPkg = new ExcelPackage();
            var sheet = excelPkg.Workbook.Worksheets.Add("Bank Book");
            sheet.TabColor = Color.Green;
            sheet.DefaultRowHeight = 12;
            sheet.Row(1).Height = 5;
            sheet.Column(1).Width = 1;
            sheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            sheet.Row(1).Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            sheet.Row(1).Style.Font.Bold = true;
            sheet.Cells[2, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            sheet.Cells[2, 6].Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            sheet.Cells[2, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            sheet.Cells[2, 10].Style.VerticalAlignment = ExcelVerticalAlignment.Center;

            # region Header
            sheet.Cells[2, 2].Value = "Client Name";
            sheet.Cells[3, 2].Value = "PAN";
            sheet.Cells[4, 2].Value = "Portfolio Name";
            sheet.Cells[5, 2].Value = "Portfolio Code";
            sheet.Cells[2, 6].Value = "Portfolio NAV Report";

            // Assuming reportData is in the appropriate format
            sheet.Cells[2, 3].Value = reportData.ClientName;
            sheet.Cells[3, 3].Value = reportData.ClientPan;
            sheet.Cells[4, 3].Value = reportData.PortfolioName;
            sheet.Cells[5, 3].Value = reportData.PortfolioCode;
            sheet.Cells[1, 6].Value = reportData.PmsHouseName;
            sheet.Cells[2, 10].Value = "Report As On " + reportData.ReportGenerationDate;

            sheet.Row(1).Height = 20;
            sheet.Row(2).Height = 20;
            sheet.Row(3).Height = 20;
            sheet.Row(4).Height = 20;
            sheet.Row(5).Height = 20;

            sheet.Cells[2, 2].Style.Font.Bold = true;
            sheet.Cells[3, 2].Style.Font.Bold = true;
            sheet.Cells[4, 2].Style.Font.Bold = true;
            sheet.Cells[5, 2].Style.Font.Bold = true;
            #endregion

            #region Content Table Header
            Color colFromHex = ColorTranslator.FromHtml("#00FFE3");
            var headerRow = 7;

            sheet.Cells[headerRow, 2].Style.Border.Left.Style = ExcelBorderStyle.Medium;
            sheet.Cells[headerRow, 7].Style.Border.Right.Style = ExcelBorderStyle.Medium;
            for (var i = 2; i <= 7; i++)
            {
                sheet.Cells[headerRow, i].Style.Border.Top.Style = ExcelBorderStyle.Medium;
                sheet.Cells[headerRow, i].Style.Border.Bottom.Style = ExcelBorderStyle.Medium;
            }

            sheet.Cells[headerRow, 2].Value = "Date";
            sheet.Cells[headerRow, 2].Style.Font.Color.SetColor(Color.Black);
            sheet.Cells[headerRow, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[headerRow, 2].Style.Fill.BackgroundColor.SetColor(colFromHex);

            sheet.Cells[headerRow, 3].Value = "Opening Balance";
            sheet.Cells[headerRow, 3].Style.Font.Color.SetColor(Color.Black);
            sheet.Cells[headerRow, 3].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[headerRow, 3].Style.Fill.BackgroundColor.SetColor(colFromHex);

            sheet.Cells[headerRow, 4].Value = "Closing Balance";
            sheet.Cells[headerRow, 4].Style.Font.Color.SetColor(Color.Black);
            sheet.Cells[headerRow, 4].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[headerRow, 4].Style.Fill.BackgroundColor.SetColor(colFromHex);

            sheet.Cells[headerRow, 5].Value = "Cash Flow";
            sheet.Cells[headerRow, 5].Style.Font.Color.SetColor(Color.Black);
            sheet.Cells[headerRow, 5].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[headerRow, 5].Style.Fill.BackgroundColor.SetColor(colFromHex);

            sheet.Cells[headerRow, 6].Value = "Rate of Return %";
            sheet.Cells[headerRow, 6].Style.Font.Color.SetColor(Color.Black);
            sheet.Cells[headerRow, 6].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[headerRow, 6].Style.Fill.BackgroundColor.SetColor(colFromHex);

            sheet.Cells[headerRow, 7].Value = "NAV";
            sheet.Cells[headerRow, 7].Style.Font.Color.SetColor(Color.Black);
            sheet.Cells[headerRow, 7].Style.Fill.PatternType = ExcelFillStyle.Solid;
            sheet.Cells[headerRow, 7].Style.Fill.BackgroundColor.SetColor(colFromHex);
            #endregion

            #region Content Table Data
            var dataRow = 8;
            double sumCashFlow = 0.0;
            double result;

            foreach (var trans in reportData.Transactions)
            {
                sheet.Cells[dataRow, 2].Style.Border.Left.Style = ExcelBorderStyle.Medium;
                sheet.Cells[dataRow, 7].Style.Border.Right.Style = ExcelBorderStyle.Medium;
                // Add the current row's Cash Flow to the total
                sumCashFlow += Convert.ToDouble(trans.CashFlowDouble);

                sheet.Cells[dataRow, 2].Value = trans.Date;
                sheet.Cells[dataRow, 3].Value = trans.OpeningBalanceDouble;
                sheet.Cells[dataRow, 4].Value = trans.ClosingBalanceDouble;
                sheet.Cells[dataRow, 5].Value = trans.CashFlow;
                sheet.Cells[dataRow, 6].Value = trans.RateOfReturn;
                sheet.Cells[dataRow, 7].Value = trans.NAV;
                dataRow++;
            }
            for (var i = 2; i <= 7; i++) sheet.Cells[dataRow, i].Style.Border.Top.Style = ExcelBorderStyle.Medium;

            // Total and sum
            sheet.Cells[dataRow, 2].Value = "Total";
            sheet.Cells[dataRow, 5].Value = "\u20B9" + sumCashFlow;
            sheet.Cells[dataRow, 7].Value = sheet.Cells[dataRow - 1, 7].Value;
            #endregion

            // Autofit all columns
            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();

            var fileBytes = await excelPkg.GetAsByteArrayAsync();
            //Close Excel package 
            excelPkg.Dispose();

#if DEBUG
            var folderPath = $@"{Path.GetTempPath()}Reports\SampleBBReports";
            if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

            var fileOutputPath = $"{folderPath}\\RPT-BNKBK-{reportData.ClientName}-{DateTime.Now.Ticks}.xlsx";
            if (File.Exists(fileOutputPath))
                File.Delete(fileOutputPath);
            // Create excel file on physical disk  
            FileStream objFileStrm = File.Create(fileOutputPath);
            objFileStrm.Close();
            // Write content to excel file  
            await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

            return fileBytes;
        }

        public async Task<RptPortfolioNav> GenerateRptPortfolioNavAsAt(string portfolioId, DateTime fromDate, DateTime toDate)
        {
            var portfolio = await _portfolioRepo.GetById(portfolioId);
            if (portfolio == null)
                throw new InvalidOperationException("Portfolio not found.");

            var client = await _clientRepo.GetClientById(portfolio.ClientId);


            var pmsHouseNameSetting = await _generalSettingRepository.GetByKey("PmsHouseName");
            var pmsHouseLogoSetting = await _generalSettingRepository.GetByKey("Logo");

            var portfolioNavData = await _portfolioAumService.GetAumFromSpecificDateRange(portfolioId, fromDate.AddDays(-1), toDate);
            var rptPortfolioNav = new RptPortfolioNav();

            var transactions = new List<RptPortfolioNavTransaction>();
            RptPortfolioNavTransaction previousTransaction = null;

            foreach (var portfolioNav in portfolioNavData.Where(x => x.AsAtDate >= fromDate && x.AsAtDate <= toDate))
            {
                var transaction = new RptPortfolioNavTransaction
                {
                    CashFlow = Math.Round(portfolioNav.NetCashFlow, 2).GetRptValue("1", 2),
                    CashFlowDouble = Math.Round(portfolioNav.NetCashFlow, 2),
                    ClosingBalance = Math.Round(portfolioNav.CashBalance + portfolioNav.MarketValue, 2).GetRptValue("1", 2),
                    ClosingBalanceDouble = Math.Round(portfolioNav.CashBalance + portfolioNav.MarketValue, 2),
                    Date = portfolioNav.AsAtDate.ToString("dd-MM-yyyy"),
                    NAV = Math.Round(portfolioNav.Nav, 2),
                    RateOfReturn = Math.Round(portfolioNav.Change, 2),
                    OpeningBalance = previousTransaction == null 
                        ? Math.Round(portfolioNav.CashBalance, 2).GetRptValue("1", 2) 
                        : Math.Round(previousTransaction.ClosingBalanceDouble, 2).GetRptValue("1", 2),
                    OpeningBalanceDouble = previousTransaction == null 
                        ? Math.Round(portfolioNav.CashBalance, 2)
                        : Math.Round(previousTransaction.ClosingBalanceDouble, 2)
                };

                transactions.Add(transaction);
                previousTransaction = transaction;
            }

            rptPortfolioNav.Transactions = transactions;

            rptPortfolioNav.Total = new RptPortfolioNavTotal
            {
                TotalCashDouble = rptPortfolioNav.Transactions.Sum(e => e.ClosingBalanceDouble),
                TotalCashFlow = rptPortfolioNav.Transactions.Sum(e => e.CashFlowDouble),
            };

            if (pmsHouseNameSetting == null)
                rptPortfolioNav.PmsHouseName = "PMS Private Limited";
            else
                rptPortfolioNav.PmsHouseName = pmsHouseNameSetting.Value;

            if (pmsHouseLogoSetting == null)
                rptPortfolioNav.Logo = string.Empty;
            else
                rptPortfolioNav.Logo = pmsHouseLogoSetting.Value;


            rptPortfolioNav.ReportGenerationDate = DateTime.Today.ToString("dd MMM yyyy");
            rptPortfolioNav.ReportingDate = fromDate.ToString("dd MMM yyyy") + " - " + toDate.ToString("dd MMM yyyy");
            rptPortfolioNav.ClientName = $"{client.FirstName} {client.LastName}";
            rptPortfolioNav.ClientUniqueCode = client.ClientCode;
            rptPortfolioNav.PortfolioName = portfolio.Name;
            rptPortfolioNav.ClientPan = client.Pan;
            rptPortfolioNav.PortfolioCode = portfolio.ClientStrategyCode;
            return rptPortfolioNav;

        }
    }

}