﻿using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage.PerformanceEngine;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Actlogica.AlphaPortfolios.ApiContracts.Portfolios;
using Actlogica.AlphaPortfolios.ServiceIntegration.Clients;
using Microsoft.Extensions.Azure;
using OfficeOpenXml.Style;
using OfficeOpenXml;
using System.IO;
using Actlogica.AlphaPortfolios.Utils.Dates;
using Actlogica.AlphaPortfolios.ServiceIntegration.PerformanceEngine.Portfolio;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using Actlogica.AlphaPortfolios.ApiContracts.Common;

namespace Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.Mis
{
	public class MisReports : BaseReportGenerationEngine, IMisReports
	{
		private readonly IMisReportsDataRepository _misDataRepo;
		private readonly PortfolioAnalyticsStorageRepository _portfolioAnalyticsStorage;
		private readonly IDirectEquityPricesService _directEquityPriceSvc;
		private readonly Data.Entity.Db.AlphaPortfolioDbContext _dbContext;
		private readonly string _tenantName;
		private readonly IPortfolioService _ptfSvc;
		private readonly IClientService _clientService;
		private readonly IInvestmentRepository _investmentRepository;

		private readonly IMutualFundPricesService _mutualFundPricesService;

		private readonly ISecurityMasterService _securityMasterService;

		private readonly IBondPricesService _bondPricesService;
		private readonly ITenantSecurityPriceRepository _tenantSecurityPriceRepo;

		public MisReports(IMisReportsDataRepository misDataRepo, PortfolioAnalyticsStorageRepository analyticsRepo,
			IPortfolioService ptfSvc, IClientService clientService, Data.Entity.Db.AlphaPortfolioDbContext dbContext, 
			IInvestmentRepository investmentRepository, IDirectEquityPricesService directEquityPricesSvc, 
			IMutualFundPricesService mutualFundPricesService, ISecurityMasterService securityMasterService, 
			IBondPricesService bondPricesService, string tenantName, ITenantSecurityPriceRepository tenantSecurityPriceRepo)
		{
			_misDataRepo = misDataRepo;
			_directEquityPriceSvc = directEquityPricesSvc;
			_portfolioAnalyticsStorage = analyticsRepo;
			_ptfSvc = ptfSvc;
			_clientService = clientService;
			_dbContext = dbContext;
			_investmentRepository = investmentRepository;
			_mutualFundPricesService = mutualFundPricesService;
			_securityMasterService = securityMasterService;
			_bondPricesService = bondPricesService;
			_tenantName = tenantName;
			_tenantSecurityPriceRepo = tenantSecurityPriceRepo;
		}

		public async Task<byte[]> ExportToExcel(List<RptHoldingLatestDetailsForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var holdingsSheet = excelPkg.Workbook.Worksheets.Add("Holdings");
			holdingsSheet.TabColor = System.Drawing.Color.Green;
			holdingsSheet.DefaultRowHeight = 15;
			holdingsSheet.Row(1).Height = 15;
			holdingsSheet.Row(1).Style.Font.Bold = true;

			var asAtDate = reportData.FirstOrDefault().AsAtDate;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			holdingsSheet.Cells[1, 1].Value = "Client Name";
			holdingsSheet.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 2].Value = "Strategy Name";
			holdingsSheet.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 3].Value = "Client Strategy Code";
			holdingsSheet.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 4].Value = "Custodian Portfolio Code";
			holdingsSheet.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 5].Value = "FA Account Number";
			holdingsSheet.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 6].Value = "Isin";
			holdingsSheet.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 7].Value = "Holding Name";
			holdingsSheet.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 8].Value = "Symbol";
			holdingsSheet.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 9].Value = "Holdings";
			holdingsSheet.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 10].Value = "Average Price";
			holdingsSheet.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 11].Value = "Cost";
			holdingsSheet.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 11].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 12].Value = "Current Price";
			holdingsSheet.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 12].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 13].Value = "Market Value";
			holdingsSheet.Cells[1, 13].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 13].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 13].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 14].Value = "Sector";
			holdingsSheet.Cells[1, 14].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 14].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 14].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 15].Value = "Market Cap";
			holdingsSheet.Cells[1, 15].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 15].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 15].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 16].Value = "PortfolioId";
			holdingsSheet.Cells[1, 16].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 16].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 17].Value = "As At";
			holdingsSheet.Cells[1, 17].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 17].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 17].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;

			int startRow = 2;
			foreach (var analytic in reportData)
			{
				holdingsSheet.Cells[startRow, 1].Value = analytic.ClientName;
				holdingsSheet.Cells[startRow, 2].Value = analytic.StrategyName;
				holdingsSheet.Cells[startRow, 3].Value = analytic.ClientStrategyCode;
				holdingsSheet.Cells[startRow, 4].Value = analytic.CustodianPortfolioCode;
				holdingsSheet.Cells[startRow, 5].Value = analytic.FAAccountNo;
				holdingsSheet.Cells[startRow, 6].Value = analytic.Isin;
				holdingsSheet.Cells[startRow, 7].Value = analytic.HoldingName;
				holdingsSheet.Cells[startRow, 8].Value = analytic.Symbol;
				holdingsSheet.Cells[startRow, 9].Value = analytic.Holdings;
				holdingsSheet.Cells[startRow, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 10].Value = analytic.AveragePrice;
				holdingsSheet.Cells[startRow, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 11].Value = analytic.Cost;
				holdingsSheet.Cells[startRow, 11].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 12].Value = analytic.CurrentPrice;
				holdingsSheet.Cells[startRow, 12].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 13].Value = analytic.MarketValue;
				holdingsSheet.Cells[startRow, 13].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 14].Value = analytic.Sector;
				holdingsSheet.Cells[startRow, 14].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 15].Value = analytic.MarketCap;
				holdingsSheet.Cells[startRow, 15].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 16].Value = analytic.PortfolioId;
				holdingsSheet.Cells[startRow, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 17].Value = analytic.AsAtDate;
				holdingsSheet.Cells[startRow, 17].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;

				startRow++;
			}

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-HLDG-{asAtDate}-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}

		public async Task<byte[]> ExportToExcel(List<RptHoldingDetailsForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var holdingsSheet = excelPkg.Workbook.Worksheets.Add("Holdings");
			holdingsSheet.TabColor = System.Drawing.Color.Green;
			holdingsSheet.DefaultRowHeight = 15;
			holdingsSheet.Row(1).Height = 15;
			holdingsSheet.Row(1).Style.Font.Bold = true;

			var asAtDate = reportData.FirstOrDefault().AsAtDate;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			holdingsSheet.Cells[1, 1].Value = "Client Name";
			holdingsSheet.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 2].Value = "Strategy Name";
			holdingsSheet.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 3].Value = "Client Strategy Code";
			holdingsSheet.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 4].Value = "Custodian Portfolio Code";
			holdingsSheet.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 5].Value = "FA Account Number";
			holdingsSheet.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 6].Value = "Isin";
			holdingsSheet.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 7].Value = "Symbol";
			holdingsSheet.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 8].Value = "Security Name";
			holdingsSheet.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			holdingsSheet.Cells[1, 9].Value = "Holdings";
			holdingsSheet.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 10].Value = "Unit Cost";
			holdingsSheet.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 11].Value = "Total Cost";
			holdingsSheet.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 11].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 12].Value = "Unit Price";
			holdingsSheet.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 12].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 13].Value = "Market Value";
			holdingsSheet.Cells[1, 13].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 13].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 13].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 14].Value = "Accrued Income";
			holdingsSheet.Cells[1, 14].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 14].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 14].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 15].Value = "Receivable";
			holdingsSheet.Cells[1, 15].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 15].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 15].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 16].Value = "Payable";
			holdingsSheet.Cells[1, 16].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 16].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 17].Value = "As At";
			holdingsSheet.Cells[1, 17].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 17].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 17].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			holdingsSheet.Cells[1, 18].Value = "Asset Class";
			holdingsSheet.Cells[1, 18].Style.Fill.SetBackground(colFromHex);
			holdingsSheet.Cells[1, 18].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			holdingsSheet.Cells[1, 18].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;

			int startRow = 2;
			foreach (var analytic in reportData)
			{
				holdingsSheet.Cells[startRow, 1].Value = analytic.ClientName;
				holdingsSheet.Cells[startRow, 2].Value = analytic.StrategyName;
				holdingsSheet.Cells[startRow, 3].Value = analytic.ClientStrategyCode;
				holdingsSheet.Cells[startRow, 4].Value = analytic.CustodianPortfolioCode;
				holdingsSheet.Cells[startRow, 5].Value = analytic.FAAccountNo;
				holdingsSheet.Cells[startRow, 6].Value = analytic.Isin;
				holdingsSheet.Cells[startRow, 7].Value = analytic.Symbol;
				holdingsSheet.Cells[startRow, 8].Value = analytic.HoldingName;
				holdingsSheet.Cells[startRow, 9].Value = analytic.UnrealisedQty;
				holdingsSheet.Cells[startRow, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 10].Value = analytic.AveragePrice;
				holdingsSheet.Cells[startRow, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 11].Value = analytic.TotalCost;
				holdingsSheet.Cells[startRow, 11].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 12].Value = analytic.Price;
				holdingsSheet.Cells[startRow, 12].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 13].Value = analytic.MarketValue;
				holdingsSheet.Cells[startRow, 13].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 14].Value = analytic.AccruedIncome;
				holdingsSheet.Cells[startRow, 14].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 15].Value = analytic.Receivable;
				holdingsSheet.Cells[startRow, 15].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 16].Value = analytic.Payable;
				holdingsSheet.Cells[startRow, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 17].Value = analytic.AsAtDate;
				holdingsSheet.Cells[startRow, 17].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				holdingsSheet.Cells[startRow, 18].Value = analytic.AssetClass;
				holdingsSheet.Cells[startRow, 18].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;

				startRow++;
			}

			//holdingsSheet.Column(1).AutoFit();
			//holdingsSheet.Column(2).AutoFit();
			//holdingsSheet.Column(3).AutoFit();
			//holdingsSheet.Column(4).AutoFit();
			//holdingsSheet.Column(5).AutoFit();
			//holdingsSheet.Column(6).AutoFit();
			//holdingsSheet.Column(7).AutoFit();
			//holdingsSheet.Column(8).AutoFit();
			//holdingsSheet.Column(9).AutoFit();
			//holdingsSheet.Column(10).AutoFit();
			//holdingsSheet.Column(11).AutoFit();
			//holdingsSheet.Column(12).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-HLDG-{asAtDate}-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}

		public async Task<byte[]> ExportToExcel(List<RptDividendDetailsForMis> reportData)
		{

			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var dividendsSheet = excelPkg.Workbook.Worksheets.Add("Dividends");
			dividendsSheet.TabColor = System.Drawing.Color.Green;
			dividendsSheet.DefaultRowHeight = 15;
			dividendsSheet.Row(1).Height = 15;
			dividendsSheet.Row(1).Style.Font.Bold = true;

			var asAtDate = reportData.FirstOrDefault().AsAtDate;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			dividendsSheet.Cells[1, 1].Value = "Client Name";
			dividendsSheet.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 2].Value = "Strategy Name";
			dividendsSheet.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 3].Value = "Client Strategy Code";
			dividendsSheet.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			dividendsSheet.Cells[1, 4].Value = "Custodian Portfolio Code";
			dividendsSheet.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			dividendsSheet.Cells[1, 5].Value = "FA Account Number";
			dividendsSheet.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			dividendsSheet.Cells[1, 6].Value = "Transaction Date";
			dividendsSheet.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			dividendsSheet.Cells[1, 7].Value = "Transaction Type";
			dividendsSheet.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			dividendsSheet.Cells[1, 8].Value = "Transaction Sub Type";
			dividendsSheet.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			dividendsSheet.Cells[1, 9].Value = "Amount";
			dividendsSheet.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			dividendsSheet.Cells[1, 10].Value = "As At Date";
			dividendsSheet.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			dividendsSheet.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			dividendsSheet.Cells[1, 11].Value = "Description";
			dividendsSheet.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			dividendsSheet.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);

			int startRow = 2;
			foreach (var analytic in reportData)
			{
				dividendsSheet.Cells[startRow, 1].Value = analytic.ClientName;
				dividendsSheet.Cells[startRow, 2].Value = analytic.StrategyName;
				dividendsSheet.Cells[startRow, 3].Value = analytic.ClientStrategyCode;
				dividendsSheet.Cells[startRow, 4].Value = analytic.CustodianPortfolioCode;
				dividendsSheet.Cells[startRow, 5].Value = analytic.FAAccountNo;
				dividendsSheet.Cells[startRow, 6].Value = analytic.TransactionDate;
				dividendsSheet.Cells[startRow, 7].Value = analytic.TransactionType;
				dividendsSheet.Cells[startRow, 8].Value = analytic.TransactionSubType;
				dividendsSheet.Cells[startRow, 9].Value = analytic.Amount;
				dividendsSheet.Cells[startRow, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				dividendsSheet.Cells[startRow, 10].Value = analytic.AsAtDate;
				dividendsSheet.Cells[startRow, 11].Value = analytic.Description;

				startRow++;
			}

			//dividendsSheet.Column(1).AutoFit();
			//dividendsSheet.Column(2).AutoFit();
			//dividendsSheet.Column(3).AutoFit();
			//dividendsSheet.Column(4).AutoFit();
			//dividendsSheet.Column(5).AutoFit();
			//dividendsSheet.Column(6).AutoFit();
			//dividendsSheet.Column(7).AutoFit();
			//dividendsSheet.Column(8).AutoFit();
			//dividendsSheet.Column(9).AutoFit();
			//dividendsSheet.Column(10).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-DVND-{asAtDate}-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}

		public async Task<byte[]> ExportToExcel(List<RptIncomeExpenseDetailsForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var incomeExpenses = excelPkg.Workbook.Worksheets.Add("Income-Expenses");
			incomeExpenses.TabColor = System.Drawing.Color.Green;
			incomeExpenses.DefaultRowHeight = 15;
			incomeExpenses.Row(1).Height = 15;
			incomeExpenses.Row(1).Style.Font.Bold = true;

			var asAtDate = reportData.FirstOrDefault().AsAtDate;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			incomeExpenses.Cells[1, 1].Value = "Client Name";
			incomeExpenses.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 2].Value = "Strategy Name";
			incomeExpenses.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 3].Value = "Client Strategy Code";
			incomeExpenses.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			incomeExpenses.Cells[1, 4].Value = "Custodian Portfolio Code";
			incomeExpenses.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			incomeExpenses.Cells[1, 5].Value = "FA Account Number";
			incomeExpenses.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			incomeExpenses.Cells[1, 6].Value = "Transaction Date";
			incomeExpenses.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			incomeExpenses.Cells[1, 7].Value = "Transaction Type";
			incomeExpenses.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			incomeExpenses.Cells[1, 8].Value = "Transaction Sub Type";
			incomeExpenses.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			incomeExpenses.Cells[1, 9].Value = "Amount";
			incomeExpenses.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			incomeExpenses.Cells[1, 10].Value = "As At Date";
			incomeExpenses.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			incomeExpenses.Cells[1, 11].Value = "Description";
			incomeExpenses.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			incomeExpenses.Cells[1, 12].Value = "Portfolio Cash Ledger Id";
			incomeExpenses.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
			incomeExpenses.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);

			int startRow = 2;
			foreach (var analytic in reportData)
			{
				incomeExpenses.Cells[startRow, 1].Value = analytic.ClientName;
				incomeExpenses.Cells[startRow, 2].Value = analytic.StrategyName;
				incomeExpenses.Cells[startRow, 3].Value = analytic.ClientStrategyCode;
				incomeExpenses.Cells[startRow, 4].Value = analytic.CustodianPortfolioCode;
				incomeExpenses.Cells[startRow, 5].Value = analytic.FAAccountNo;
				incomeExpenses.Cells[startRow, 6].Value = analytic.TransactionDate;
				incomeExpenses.Cells[startRow, 7].Value = analytic.TransactionType;
				incomeExpenses.Cells[startRow, 8].Value = analytic.TransactionSubType;
				incomeExpenses.Cells[startRow, 9].Value = analytic.Amount;
				incomeExpenses.Cells[startRow, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				incomeExpenses.Cells[startRow, 10].Value = analytic.AsAtDate;
				incomeExpenses.Cells[startRow, 11].Value = analytic.Description;
				incomeExpenses.Cells[startRow, 12].Value = analytic.PortfolioCashLedgerId;

				startRow++;
			}

			//incomeExpenses.Column(1).AutoFit();
			//incomeExpenses.Column(2).AutoFit();
			//incomeExpenses.Column(3).AutoFit();
			//incomeExpenses.Column(4).AutoFit();
			//incomeExpenses.Column(5).AutoFit();
			//incomeExpenses.Column(6).AutoFit();
			//incomeExpenses.Column(7).AutoFit();
			//incomeExpenses.Column(8).AutoFit();
			//incomeExpenses.Column(9).AutoFit();
			//incomeExpenses.Column(10).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-DVND-{asAtDate}-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}

		public async Task<byte[]> ExportToExcel(List<RptCapitalRegisterDetailsForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var capitalRegister = excelPkg.Workbook.Worksheets.Add("Capital-Register");
			capitalRegister.TabColor = System.Drawing.Color.Green;
			capitalRegister.DefaultRowHeight = 15;
			capitalRegister.Row(1).Height = 15;
			capitalRegister.Row(1).Style.Font.Bold = true;

			var asAtDate = reportData.FirstOrDefault().AsAtDate;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			capitalRegister.Cells[1, 1].Value = "Client Name";
			capitalRegister.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 2].Value = "Strategy Name";
			capitalRegister.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 3].Value = "Client Strategy Code";
			capitalRegister.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			capitalRegister.Cells[1, 4].Value = "Custodian Portfolio Code";
			capitalRegister.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			capitalRegister.Cells[1, 5].Value = "FA Account No";
			capitalRegister.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			capitalRegister.Cells[1, 6].Value = "Transaction Date";
			capitalRegister.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			capitalRegister.Cells[1, 7].Value = "Transaction Type";
			capitalRegister.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			capitalRegister.Cells[1, 8].Value = "Transaction Sub Type";
			capitalRegister.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			capitalRegister.Cells[1, 9].Value = "Amount";
			capitalRegister.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			capitalRegister.Cells[1, 10].Value = "As At Date";
			capitalRegister.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			capitalRegister.Cells[1, 11].Value = "Description";
			capitalRegister.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			capitalRegister.Cells[1, 12].Value = "Portfolio Cash Ledger Id";
			capitalRegister.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
			capitalRegister.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);

			int startRow = 2;
			foreach (var analytic in reportData)
			{
				capitalRegister.Cells[startRow, 1].Value = analytic.ClientName;
				capitalRegister.Cells[startRow, 2].Value = analytic.StrategyName;
				capitalRegister.Cells[startRow, 3].Value = analytic.ClientStrategyCode;
				capitalRegister.Cells[startRow, 4].Value = analytic.CustodianPortfolioCode;
				capitalRegister.Cells[startRow, 5].Value = analytic.FAAccountNo;
				capitalRegister.Cells[startRow, 6].Value = analytic.TransactionDate;
				capitalRegister.Cells[startRow, 7].Value = analytic.TransactionType;
				capitalRegister.Cells[startRow, 8].Value = analytic.TransactionSubType;
				capitalRegister.Cells[startRow, 9].Value = analytic.Amount;
				capitalRegister.Cells[startRow, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				capitalRegister.Cells[startRow, 10].Value = analytic.AsAtDate;
				capitalRegister.Cells[startRow, 11].Value = analytic.Description;
				capitalRegister.Cells[startRow, 12].Value = analytic.PortfolioCashLedgerId;
				startRow++;
			}

			//incomeExpenses.Column(1).AutoFit();
			//incomeExpenses.Column(2).AutoFit();
			//incomeExpenses.Column(3).AutoFit();
			//incomeExpenses.Column(4).AutoFit();
			//incomeExpenses.Column(5).AutoFit();
			//incomeExpenses.Column(6).AutoFit();
			//incomeExpenses.Column(7).AutoFit();
			//incomeExpenses.Column(8).AutoFit();
			//incomeExpenses.Column(9).AutoFit();
			//incomeExpenses.Column(10).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-CAPREG-{asAtDate}-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}
		public async Task<byte[]> ExportToExcel(List<RptModelPortfolioTransactionsDetailsForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var modelPortfolioTransactions = excelPkg.Workbook.Worksheets.Add("Model-Portfolio-Transactions");
			modelPortfolioTransactions.TabColor = System.Drawing.Color.Green;
			modelPortfolioTransactions.DefaultRowHeight = 15;
			modelPortfolioTransactions.Row(1).Height = 15;
			modelPortfolioTransactions.Row(1).Style.Font.Bold = true;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			modelPortfolioTransactions.Cells[1, 1].Value = "Id";
			modelPortfolioTransactions.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 2].Value = "Model Name";
			modelPortfolioTransactions.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 3].Value = "Symbol";
			modelPortfolioTransactions.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 4].Value = "Name";
			modelPortfolioTransactions.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			modelPortfolioTransactions.Cells[1, 5].Value = "Type";
			modelPortfolioTransactions.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			modelPortfolioTransactions.Cells[1, 6].Value = "Sub Type";
			modelPortfolioTransactions.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			modelPortfolioTransactions.Cells[1, 7].Value = "Transaction Date";
			modelPortfolioTransactions.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			modelPortfolioTransactions.Cells[1, 8].Value = "Settlement Date";
			modelPortfolioTransactions.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			modelPortfolioTransactions.Cells[1, 9].Value = "Quantity";
			modelPortfolioTransactions.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			modelPortfolioTransactions.Cells[1, 10].Value = "Current Holding";
			modelPortfolioTransactions.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			modelPortfolioTransactions.Cells[1, 11].Value = "Unrealised Holding";
			modelPortfolioTransactions.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 12].Value = "Price";
			modelPortfolioTransactions.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 13].Value = "Amount";
			modelPortfolioTransactions.Cells[1, 13].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 13].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 14].Value = "Brokerage";
			modelPortfolioTransactions.Cells[1, 14].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 14].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 15].Value = "Service Tax";
			modelPortfolioTransactions.Cells[1, 15].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 15].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 16].Value = "Stt Amount";
			modelPortfolioTransactions.Cells[1, 16].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 16].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 17].Value = "Turn Tax";
			modelPortfolioTransactions.Cells[1, 17].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 17].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 18].Value = "Other tax";
			modelPortfolioTransactions.Cells[1, 18].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 18].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 19].Value = "Exchange";
			modelPortfolioTransactions.Cells[1, 19].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 19].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 20].Value = "Isin";
			modelPortfolioTransactions.Cells[1, 20].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 20].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioTransactions.Cells[1, 21].Value = "Market Price";
			modelPortfolioTransactions.Cells[1, 21].Style.Fill.SetBackground(colFromHex);
			modelPortfolioTransactions.Cells[1, 21].Style.Font.Color.SetColor(System.Drawing.Color.Black);


			int startRow = 2;
			foreach (var analytic in reportData)
			{
				modelPortfolioTransactions.Cells[startRow, 1].Value = analytic.Id;
				modelPortfolioTransactions.Cells[startRow, 2].Value = analytic.ModelName;
				modelPortfolioTransactions.Cells[startRow, 3].Value = analytic.Symbol;
				modelPortfolioTransactions.Cells[startRow, 4].Value = analytic.Name;
				modelPortfolioTransactions.Cells[startRow, 5].Value = analytic.Type;
				modelPortfolioTransactions.Cells[startRow, 6].Value = analytic.SubType;
				modelPortfolioTransactions.Cells[startRow, 7].Value = analytic.TransactionDate;
				modelPortfolioTransactions.Cells[startRow, 8].Value = analytic.SettlementDate;
				modelPortfolioTransactions.Cells[startRow, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				modelPortfolioTransactions.Cells[startRow, 9].Value = Math.Round(analytic.Quantity, 2);
				modelPortfolioTransactions.Cells[startRow, 10].Value = Math.Round(analytic.CurrentHolding, 2);
				modelPortfolioTransactions.Cells[startRow, 11].Value = Math.Round(analytic.UnrealisedHolding, 2);
				modelPortfolioTransactions.Cells[startRow, 12].Value = Math.Round(analytic.Price, 2);
				modelPortfolioTransactions.Cells[startRow, 13].Value = Math.Round(analytic.Amount, 2);
				modelPortfolioTransactions.Cells[startRow, 14].Value = Math.Round(analytic.Brokerage, 2);
				modelPortfolioTransactions.Cells[startRow, 15].Value = Math.Round(analytic.ServiceTax, 2);
				modelPortfolioTransactions.Cells[startRow, 16].Value = Math.Round(analytic.SttAmount, 2);
				modelPortfolioTransactions.Cells[startRow, 17].Value = Math.Round(analytic.TurnTax, 2);
				modelPortfolioTransactions.Cells[startRow, 18].Value = Math.Round(analytic.OtherTax, 2);
				modelPortfolioTransactions.Cells[startRow, 19].Value = analytic.Exchange;
				modelPortfolioTransactions.Cells[startRow, 20].Value = analytic.Isin;
				modelPortfolioTransactions.Cells[startRow, 21].Value = Math.Round(analytic.MarketRate, 2);
				startRow++;
			}

			//incomeExpenses.Column(1).AutoFit();
			//incomeExpenses.Column(2).AutoFit();
			//incomeExpenses.Column(3).AutoFit();
			//incomeExpenses.Column(4).AutoFit();
			//incomeExpenses.Column(5).AutoFit();
			//incomeExpenses.Column(6).AutoFit();
			//incomeExpenses.Column(7).AutoFit();
			//incomeExpenses.Column(8).AutoFit();
			//incomeExpenses.Column(9).AutoFit();
			//incomeExpenses.Column(10).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-ModelPortfolio-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}
		public async Task<byte[]> ExportToExcel(List<RptModelPortfolioHoldingsLatestDetailsForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var modelPortfolioHoldings = excelPkg.Workbook.Worksheets.Add("Model-Portfolio-Holdings");
			modelPortfolioHoldings.TabColor = System.Drawing.Color.Green;
			modelPortfolioHoldings.DefaultRowHeight = 15;
			modelPortfolioHoldings.Row(1).Height = 15;
			modelPortfolioHoldings.Row(1).Style.Font.Bold = true;

			// var asAtDate = reportData.FirstOrDefault().TransactionDate;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			modelPortfolioHoldings.Cells[1, 1].Value = "Model Name";
			modelPortfolioHoldings.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 1].Style.Fill.SetBackground(colFromHex);

			modelPortfolioHoldings.Cells[1, 2].Value = "Isin";
			modelPortfolioHoldings.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

			modelPortfolioHoldings.Cells[1, 3].Value = "Investment Name";
			modelPortfolioHoldings.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

			modelPortfolioHoldings.Cells[1, 4].Value = "Holding";
			modelPortfolioHoldings.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

			modelPortfolioHoldings.Cells[1, 5].Value = "Average Price";
			modelPortfolioHoldings.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

			modelPortfolioHoldings.Cells[1, 6].Value = "Cost";
			modelPortfolioHoldings.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

			modelPortfolioHoldings.Cells[1, 7].Value = "Rate";
			modelPortfolioHoldings.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;

			modelPortfolioHoldings.Cells[1, 8].Value = "Investment Market Value";
			modelPortfolioHoldings.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			modelPortfolioHoldings.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;

			modelPortfolioHoldings.Cells[1, 9].Value = "Model Cash";
			modelPortfolioHoldings.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);

			modelPortfolioHoldings.Cells[1, 10].Value = "AsAt Date";
			modelPortfolioHoldings.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			modelPortfolioHoldings.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);



			int startRow = 2;
			foreach (var analytic in reportData)
			{
				modelPortfolioHoldings.Cells[startRow, 1].Value = analytic.ModelName;
				modelPortfolioHoldings.Cells[startRow, 2].Value = analytic.Isin;
				modelPortfolioHoldings.Cells[startRow, 3].Value = analytic.InvestmentName;
				modelPortfolioHoldings.Cells[startRow, 4].Value = Math.Round(analytic.Holding, 2);
				modelPortfolioHoldings.Cells[startRow, 5].Value = Math.Round(analytic.AvgPrice, 2);
				modelPortfolioHoldings.Cells[startRow, 6].Value = Math.Round(analytic.Cost, 2);
				modelPortfolioHoldings.Cells[startRow, 7].Value = Math.Round(analytic.Rate, 2);
				modelPortfolioHoldings.Cells[startRow, 8].Value = Math.Round(analytic.InvestmentMv, 2);
				modelPortfolioHoldings.Cells[startRow, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				modelPortfolioHoldings.Cells[startRow, 9].Value = Math.Round(analytic.ModelCash, 2);
				modelPortfolioHoldings.Cells[startRow, 10].Value = analytic.AsAtDate;

				startRow++;

			}

			//incomeExpenses.Column(1).AutoFit();
			//incomeExpenses.Column(2).AutoFit();
			//incomeExpenses.Column(3).AutoFit();
			//incomeExpenses.Column(4).AutoFit();
			//incomeExpenses.Column(5).AutoFit();
			//incomeExpenses.Column(6).AutoFit();
			//incomeExpenses.Column(7).AutoFit();
			//incomeExpenses.Column(8).AutoFit();
			//incomeExpenses.Column(9).AutoFit();
			//incomeExpenses.Column(10).AutoFit();
			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-ModelPortfolioHoldings-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}

		public async Task<byte[]> ExportToExcel(List<RptClientMasterDetailsForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var clientMaster = excelPkg.Workbook.Worksheets.Add("Client-Master");
			clientMaster.TabColor = System.Drawing.Color.Green;
			clientMaster.DefaultRowHeight = 15;
			clientMaster.Row(1).Height = 15;
			clientMaster.Row(1).Style.Font.Bold = true;

			var asAtDate = reportData.FirstOrDefault().AsAtDate;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			clientMaster.Cells[1, 1].Value = "Client Name";
			clientMaster.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 2].Value = "Date Of Birth";
			clientMaster.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 3].Value = "PAN";
			clientMaster.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 4].Value = "Email";
			clientMaster.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 5].Value = "Phone";
			clientMaster.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 6].Value = "BseStarUcc";
			clientMaster.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 7].Value = "Strategy Name";
			clientMaster.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 8].Value = "Client Strategy Code";
			clientMaster.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 9].Value = "Custodian Portfolio Code";
			clientMaster.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 10].Value = "FA Account No";
			clientMaster.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			clientMaster.Cells[1, 11].Value = "Client Type";
			clientMaster.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 12].Value = "Domicile";
			clientMaster.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 13].Value = "Account Status";
			clientMaster.Cells[1, 13].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 13].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			clientMaster.Cells[1, 14].Value = "Client Code";
			clientMaster.Cells[1, 14].Style.Fill.SetBackground(colFromHex);
			clientMaster.Cells[1, 14].Style.Font.Color.SetColor(System.Drawing.Color.Black);

			int startRow = 2;
			foreach (var analytic in reportData)
			{
				clientMaster.Cells[startRow, 1].Value = analytic.ClientName;
				clientMaster.Cells[startRow, 2].Value = analytic.DateOfBirth;
				clientMaster.Cells[startRow, 3].Value = analytic.Pan;
				clientMaster.Cells[startRow, 4].Value = analytic.Email;
				clientMaster.Cells[startRow, 5].Value = analytic.Phone;
				clientMaster.Cells[startRow, 6].Value = analytic.BseStarUcc;
				clientMaster.Cells[startRow, 7].Value = analytic.StrategyName;
				clientMaster.Cells[startRow, 8].Value = analytic.ClientStrategyCode;
				clientMaster.Cells[startRow, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
				clientMaster.Cells[startRow, 9].Value = analytic.CustodianPortfolioCode;
				clientMaster.Cells[startRow, 10].Value = analytic.FAAccountNo;
				clientMaster.Cells[startRow, 11].Value = analytic.ClientType;
				clientMaster.Cells[startRow, 12].Value = analytic.Domicile;
				clientMaster.Cells[startRow, 13].Value = analytic.AccountStatus;
				clientMaster.Cells[startRow, 14].Value = analytic.ClientCode;

				startRow++;
			}

			//incomeExpenses.Column(1).AutoFit();
			//incomeExpenses.Column(2).AutoFit();
			//incomeExpenses.Column(3).AutoFit();
			//incomeExpenses.Column(4).AutoFit();
			//incomeExpenses.Column(5).AutoFit();
			//incomeExpenses.Column(6).AutoFit();
			//incomeExpenses.Column(7).AutoFit();
			//incomeExpenses.Column(8).AutoFit();
			//incomeExpenses.Column(9).AutoFit();
			//incomeExpenses.Column(10).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-CLMst-{asAtDate}-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}

		public async Task<byte[]> ExportToExcel(List<RptTradesBetweenDates> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var tradesGroupedByStatus = reportData.GroupBy(ord => ord.OrderStatus);

			foreach (var tradesByStatus in tradesGroupedByStatus)
			{
				var statusSpecificTradesSheet = excelPkg.Workbook.Worksheets.Add(tradesByStatus.Key);
				statusSpecificTradesSheet.TabColor = System.Drawing.Color.Green;
				statusSpecificTradesSheet.DefaultRowHeight = 15;
				statusSpecificTradesSheet.Row(1).Height = 15;
				statusSpecificTradesSheet.Row(1).Style.Font.Bold = true;


				System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
				statusSpecificTradesSheet.Cells[1, 1].Value = "Client Name";
				statusSpecificTradesSheet.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 2].Value = "Client Code";
				statusSpecificTradesSheet.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 3].Value = "Client Strategy Code";
				statusSpecificTradesSheet.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 4].Value = "Strategy Code";
				statusSpecificTradesSheet.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 5].Value = "Custodian Portfolio Code";
				statusSpecificTradesSheet.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 6].Value = "FA Account No";
				statusSpecificTradesSheet.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 7].Value = "Strategy Name";
				statusSpecificTradesSheet.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 8].Value = "Identifier";
				statusSpecificTradesSheet.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 9].Value = "ISIN";
				statusSpecificTradesSheet.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 10].Value = "Exchange";
				statusSpecificTradesSheet.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
				statusSpecificTradesSheet.Cells[1, 11].Value = "Scrip Name";
				statusSpecificTradesSheet.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 12].Value = "Order Date";
				statusSpecificTradesSheet.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 13].Value = "Order Type";
				statusSpecificTradesSheet.Cells[1, 13].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 13].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 14].Value = "Transaction Type";
				statusSpecificTradesSheet.Cells[1, 14].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 14].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 15].Value = "Price";
				statusSpecificTradesSheet.Cells[1, 15].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 15].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 16].Value = "Quantity";
				statusSpecificTradesSheet.Cells[1, 16].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 16].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 17].Value = "Transaction Amount";
				statusSpecificTradesSheet.Cells[1, 17].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 17].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 18].Value = "Order Status";
				statusSpecificTradesSheet.Cells[1, 18].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 18].Style.Font.Color.SetColor(System.Drawing.Color.Black);
				statusSpecificTradesSheet.Cells[1, 19].Value = "Source Type";
				statusSpecificTradesSheet.Cells[1, 19].Style.Fill.SetBackground(colFromHex);
				statusSpecificTradesSheet.Cells[1, 19].Style.Font.Color.SetColor(System.Drawing.Color.Black);

				int startRow = 2;
				foreach (var trade in tradesByStatus.ToList())
				{
					statusSpecificTradesSheet.Cells[startRow, 1].Value = trade.ClientName;
					statusSpecificTradesSheet.Cells[startRow, 2].Value = trade.ClientCode;
					statusSpecificTradesSheet.Cells[startRow, 3].Value = trade.ClientStrategyCode;
					statusSpecificTradesSheet.Cells[startRow, 4].Value = trade.StrategyCode;
					statusSpecificTradesSheet.Cells[startRow, 5].Value = trade.CustodianPortfolioCode;
					statusSpecificTradesSheet.Cells[startRow, 6].Value = trade.FAAccountNo;
					statusSpecificTradesSheet.Cells[startRow, 7].Value = trade.StrategyName;
					statusSpecificTradesSheet.Cells[startRow, 8].Value = trade.Identifier;
					statusSpecificTradesSheet.Cells[startRow, 9].Value = trade.Isin;
					statusSpecificTradesSheet.Cells[startRow, 10].Value = trade.Exchange;
					statusSpecificTradesSheet.Cells[startRow, 11].Value = trade.ScripName;
					statusSpecificTradesSheet.Cells[startRow, 12].Value = trade.OrderDate;
					statusSpecificTradesSheet.Cells[startRow, 13].Value = trade.OrderType;
					statusSpecificTradesSheet.Cells[startRow, 14].Value = trade.TransactionType;
					statusSpecificTradesSheet.Cells[startRow, 15].Value = trade.Price;
					statusSpecificTradesSheet.Cells[startRow, 15].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
					statusSpecificTradesSheet.Cells[startRow, 16].Value = trade.Quantity;
					statusSpecificTradesSheet.Cells[startRow, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
					statusSpecificTradesSheet.Cells[startRow, 17].Value = trade.TransactionAmount;
					statusSpecificTradesSheet.Cells[startRow, 17].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
					statusSpecificTradesSheet.Cells[startRow, 18].Value = trade.OrderStatus;
					statusSpecificTradesSheet.Cells[startRow, 19].Value = trade.SourceType;

					startRow++;
				}

			}


			//incomeExpenses.Column(1).AutoFit();
			//incomeExpenses.Column(2).AutoFit();
			//incomeExpenses.Column(3).AutoFit();
			//incomeExpenses.Column(4).AutoFit();
			//incomeExpenses.Column(5).AutoFit();
			//incomeExpenses.Column(6).AutoFit();
			//incomeExpenses.Column(7).AutoFit();
			//incomeExpenses.Column(8).AutoFit();
			//incomeExpenses.Column(9).AutoFit();
			//incomeExpenses.Column(10).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-TRD-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}


		public async Task<byte[]> ExportToExcel(List<RptTransactionalForMis> reportData)
		{
			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();
			var transactional = excelPkg.Workbook.Worksheets.Add("Transactional");
			transactional.TabColor = System.Drawing.Color.Green;
			transactional.DefaultRowHeight = 15;
			transactional.Row(1).Height = 15;
			transactional.Row(1).Style.Font.Bold = true;

			System.Drawing.Color colFromHex = System.Drawing.ColorTranslator.FromHtml("#B7DEE8");
			transactional.Cells[1, 1].Value = "Client Name";
			transactional.Cells[1, 1].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 1].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 2].Value = "Client Code";
			transactional.Cells[1, 2].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 2].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 3].Value = "Model Name";
			transactional.Cells[1, 3].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 3].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 4].Value = "Client Strategy Code";
			transactional.Cells[1, 4].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 4].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 5].Value = "Custodian Portfolio Code";
			transactional.Cells[1, 5].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 5].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 6].Value = "FA Account No";
			transactional.Cells[1, 6].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 6].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 7].Value = "Security Name";
			transactional.Cells[1, 7].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 7].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 8].Value = "ISIN";
			transactional.Cells[1, 8].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 8].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 9].Value = "Exchange";
			transactional.Cells[1, 9].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 9].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 10].Value = "Symbol";
			transactional.Cells[1, 10].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 10].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 10].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 11].Value = "Transaction Date";
			transactional.Cells[1, 11].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 11].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 11].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 12].Value = "Transaction Type";
			transactional.Cells[1, 12].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 12].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 12].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 13].Value = "Transaction Sub Type";
			transactional.Cells[1, 13].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 13].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 13].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
			transactional.Cells[1, 14].Value = "Amount";
			transactional.Cells[1, 14].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 14].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 14].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 15].Value = "Quantity";
			transactional.Cells[1, 15].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 15].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 15].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 16].Value = "Current Holding";
			transactional.Cells[1, 16].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 16].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 17].Value = "UnRealised Holding";
			transactional.Cells[1, 17].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 17].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 17].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 18].Value = "Price";
			transactional.Cells[1, 18].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 18].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 18].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 19].Value = "MarketRate";
			transactional.Cells[1, 19].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 19].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 19].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 20].Value = "Brokerage";
			transactional.Cells[1, 20].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 20].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 20].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 21].Value = "ServiceTax";
			transactional.Cells[1, 21].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 21].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 21].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 22].Value = "SttAmount";
			transactional.Cells[1, 22].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 22].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 22].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 23].Value = "TurnTax";
			transactional.Cells[1, 23].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 23].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 23].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 24].Value = "OtherTax";
			transactional.Cells[1, 24].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 24].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 24].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 25].Value = "Settlement Date";
			transactional.Cells[1, 25].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 25].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 25].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
			transactional.Cells[1, 26].Value = "Transaction Id";
			transactional.Cells[1, 26].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 26].Style.Font.Color.SetColor(System.Drawing.Color.Black);
			transactional.Cells[1, 26].Value = "Report Generation Date";
			transactional.Cells[1, 26].Style.Fill.SetBackground(colFromHex);
			transactional.Cells[1, 26].Style.Font.Color.SetColor(System.Drawing.Color.Black);


			int startRow = 2;
			foreach (var analytic in reportData)
			{
				transactional.Cells[startRow, 1].Value = analytic.InvestorName;
				transactional.Cells[startRow, 2].Value = analytic.ClientCode;
				transactional.Cells[startRow, 3].Value = analytic.ModelName;
				transactional.Cells[startRow, 4].Value = analytic.ClientStrategyCode;
				transactional.Cells[startRow, 5].Value = analytic.CustodianPortfolioCode;
				transactional.Cells[startRow, 6].Value = analytic.FAAccountNo;
				transactional.Cells[startRow, 7].Value = analytic.Name;
				transactional.Cells[startRow, 8].Value = analytic.Isin;
				transactional.Cells[startRow, 9].Value = analytic.Exchange;
				transactional.Cells[startRow, 10].Value = analytic.Symbol;
				transactional.Cells[startRow, 11].Value = analytic.TransactionDate;
				transactional.Cells[startRow, 12].Value = analytic.TransactionType;
				transactional.Cells[startRow, 13].Value = analytic.TransactionSubType;
				transactional.Cells[startRow, 14].Value = analytic.Amount;
				transactional.Cells[startRow, 15].Value = analytic.Quantity;
				transactional.Cells[startRow, 16].Value = analytic.CurrentHolding;
				transactional.Cells[startRow, 17].Value = analytic.UnrealisedHolding;
				transactional.Cells[startRow, 18].Value = analytic.Price;
				transactional.Cells[startRow, 19].Value = analytic.MarketRate;
				transactional.Cells[startRow, 10].Value = analytic.Brokerage;
				transactional.Cells[startRow, 21].Value = analytic.ServiceTax;
				transactional.Cells[startRow, 22].Value = analytic.SttAmount;
				transactional.Cells[startRow, 23].Value = analytic.TurnTax;
				transactional.Cells[startRow, 24].Value = analytic.OtherTax;
				transactional.Cells[startRow, 25].Value = analytic.SettlementDate;
				transactional.Cells[startRow, 26].Value = analytic.TransactionId;
				transactional.Cells[startRow, 26].Value = DateTime.Today.ToString("dd-MM-yyyy");

				startRow++;
			}

			//incomeExpenses.Column(1).AutoFit();
			//incomeExpenses.Column(2).AutoFit();
			//incomeExpenses.Column(3).AutoFit();
			//incomeExpenses.Column(4).AutoFit();
			//incomeExpenses.Column(5).AutoFit();
			//incomeExpenses.Column(6).AutoFit();
			//incomeExpenses.Column(7).AutoFit();
			//incomeExpenses.Column(8).AutoFit();
			//incomeExpenses.Column(9).AutoFit();
			//incomeExpenses.Column(10).AutoFit();

			var fileBytes = await excelPkg.GetAsByteArrayAsync();
			//Close Excel package 
			excelPkg.Dispose();

#if DEBUG
			var folderPath = $@"{Path.GetTempPath()}\Reports\SampleCGReports";
			if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

			var fileOutputPath = $"{folderPath}\\RPT-MIS-DVND-{DateTime.Now.Ticks}.xlsx";
			if (File.Exists(fileOutputPath))
				File.Delete(fileOutputPath);
			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(fileOutputPath);
			objFileStrm.Close();
			// Write content to excel file  
			await File.WriteAllBytesAsync(fileOutputPath, fileBytes);
#endif

			return fileBytes;
		}

		public async Task<List<RptDividendDetailsForMis>> GenerateRptDividendReportMisAsAt(DateTime asAt)
		{
			var dividendsDataForThisReport = await _misDataRepo.GetDividendDetailsForMis(asAt);
			return dividendsDataForThisReport;
		}


		public async Task<List<RptHoldingLatestDetailsForMis>> GenerateRptHoldingsLatestReportMis()
		{
			var holdingsLatest = await _misDataRepo.GetHoldingsLatestDetailsForMis();
			return holdingsLatest;
		}

		public async Task<List<RptHoldingDetailsForMis>> GenerateRptHoldingsReportMisAsAt(DateTime asAt, bool withAcquistionRate)
		{
			var portfolios = (await _ptfSvc.GetAllPortfolios()).ToList();
			var portfolioHoldingsResult = new ConcurrentBag<PortfolioAnalyticResult>();
			var portfolioHoldings = new List<PortfolioAnalytics>();
			var totalNumberOfPortfolios = portfolios.Count();
			var startIterator = 0;
			var endIterator = totalNumberOfPortfolios;
			var tasks = new List<Task>();

			var misHoldings = await _misDataRepo.GetHoldingsMisAsAt(asAt, withAcquistionRate);
			var misReceivablesPayables = await _misDataRepo.GetTenantReceivablePayableMisAsAt(asAt);
			var tenantSecurityPrices = await _tenantSecurityPriceRepo.GetAll();
			var holdingResponse = new List<RptHoldingDetailsForMis>();

      var priceCache = new ConcurrentDictionary<string, double>();

			var parallelOptions = new ParallelOptions
			{
				MaxDegreeOfParallelism = 10
			};

      await Parallel.ForEachAsync(misHoldings, parallelOptions, async (holdings, cancellationToken) =>
			{
				try
				{
					holdings.AveragePrice = Math.Round(holdings.TotalCost / holdings.UnrealisedQty, 4).ToString();

					if (holdings.Symbol.ToLower() == "cash")
					{
						var cashReceivable = misReceivablesPayables
							.FirstOrDefault(rec => rec.TransactionType == $"{TransactionType.Credit}"
								&& rec.PortfolioId == holdings.PortfolioId && rec.InvestmentId.ToLower() == "cash");
						var cashAmountReceivable = cashReceivable == null ? 0 : cashReceivable.Amount;

						var cashPayable = misReceivablesPayables
							.FirstOrDefault(rec => rec.TransactionType == $"{TransactionType.Debit}"
								&& rec.PortfolioId == holdings.PortfolioId && rec.InvestmentId.ToLower() == "cash");
						var cashAmountPayable = cashPayable == null ? 0 : cashPayable.Amount;

						holdings.Price = "1";
						holdings.AveragePrice = "1";
						holdings.MarketValue = (holdings.UnrealisedQty * Convert.ToDouble(holdings.Price)) + (cashAmountReceivable - cashAmountPayable);
						holdings.AsAtDate = asAt.ToString("dd-MM-yyyy");
						holdings.CustodianPortfolioCode = holdings.CustodianPortfolioCode;
						holdings.FAAccountNo = holdings.FAAccountNo;
						holdings.AccruedIncome = 0;
						holdings.Receivable = cashAmountReceivable;
						holdings.Payable = cashAmountPayable;
            holdingResponse.Add(holdings);
            return;
					}

					double? priceValue = 0.0;

					// Check if price already exists in cache
					if (priceCache.TryGetValue(holdings.Symbol, out double cachedPrice))
					{
						priceValue = cachedPrice;

					}
					else
					{

						if (holdings.SecurityType == ApiContracts.Common.SecurityType.ETF || holdings.SecurityType == ApiContracts.Common.SecurityType.Stocks)
						{
							if(holdings.SecuritySubType != SecuritySubType.Unlisted)
							{

								var companyMasterData = await _securityMasterService.GetCompanyMaster(holdings.Isin);
								// Fetch prices only if not in cache

								if (companyMasterData.Nse_sublisting == "Active" && companyMasterData.Bse_sublisting == "Active")
								{
									var nseTask = _directEquityPriceSvc.GetPriceOnDate(companyMasterData.Symbol, "NSE", asAt);
									var bseTask = _directEquityPriceSvc.GetPriceOnDate(companyMasterData.Scripcode, "BSE", asAt);
									await Task.WhenAll(nseTask, bseTask);

									var nsePrice = await nseTask;
									var bsePrice = await bseTask;
									var symbol = "";

									if (nsePrice.Date >= bsePrice.Date)
									{
										priceValue = nsePrice.Price;
										symbol = companyMasterData.Symbol;
									}
									else
									{
										priceValue = bsePrice.Price;
										symbol = companyMasterData.Scripcode;
									}
									// Add to cache if not already present
									priceCache.TryAdd(symbol, priceValue.Value);

								}

								else if (companyMasterData.Nse_sublisting == "Active")
								{
									var nsePrice = await _directEquityPriceSvc.GetPriceOnDate(companyMasterData.Symbol, "NSE", asAt);
									priceValue = nsePrice?.Price ?? 0;

									// Add to cache if not already present
									priceCache.TryAdd(companyMasterData.Symbol, priceValue.Value);
								}

								else
								{
									var bsePrice = await _directEquityPriceSvc.GetPriceOnDate(companyMasterData.Scripcode, "BSE", asAt);
									priceValue = bsePrice?.Price ?? 0;

									// Add to cache if not already present
									priceCache.TryAdd(companyMasterData.Scripcode, priceValue.Value);
								}
							}
							else if(holdings.SecuritySubType == SecuritySubType.Unlisted)
							{
								var unlistedSecurityPrice = tenantSecurityPrices.Where(sm => sm.Isin == holdings.Isin).OrderByDescending(sm => sm.PriceDate).FirstOrDefault();
								priceValue = unlistedSecurityPrice?.Price ?? 0;
							}
						}
						else if (holdings.SecurityType == ApiContracts.Common.SecurityType.MutualFund)
						{

							var mfSchemeMaster = await _securityMasterService.GetMutualFundByIsin(holdings.Isin);
							var mf = await _mutualFundPricesService.GetPriceOnSpecificDate(mfSchemeMaster.AmfiCode, asAt);
							priceValue = mf?.Price ?? 0;
						}
						else
						{
							//Consider as Bond
							var bondPrice = tenantSecurityPrices.Where(sm => sm.Isin == holdings.Isin).OrderByDescending(sm => sm.PriceDate).FirstOrDefault();
							priceValue = bondPrice?.Price ?? 0;
						}

					}

					// Update holdings with the calculated values

					var securityAccruable = misReceivablesPayables
							.FirstOrDefault(rec => rec.TransactionType == $"{TransactionType.Credit}"
								&& rec.PortfolioId == holdings.PortfolioId && rec.InvestmentId.ToLower() == holdings.InvestmentId);
          var securityPayable = misReceivablesPayables
              .FirstOrDefault(rec => rec.TransactionType == $"{TransactionType.Debit}"
                && rec.PortfolioId == holdings.PortfolioId && rec.InvestmentId.ToLower() == holdings.InvestmentId);
          var accruedAmountReceivable = securityAccruable == null ? 0 : securityAccruable.Amount;
          var accruedAmountPayable = securityPayable == null ? 0 : securityPayable.Amount;

          holdings.MarketValue = (holdings.UnrealisedQty * priceValue.Value) + (accruedAmountReceivable-accruedAmountPayable);
          holdings.AccruedIncome = accruedAmountReceivable;
          holdings.Price = priceValue.Value.ToString();
					holdings.AsAtDate = asAt.ToString("dd-MM-yyyy");
					holdings.Receivable = 0;
					holdings.Payable = accruedAmountPayable;
					if(holdings.UnrealisedQty <= 0)
					{
            holdings.Price = "0";
            holdings.AveragePrice = "0";
          }
					if (holdings.MarketValue <= 0 && holdings.UnrealisedQty <= 0)
						return;
				}
				catch (Exception ex)
				{
					holdings.MarketValue = 0;
					holdings.AsAtDate = asAt.ToString("dd-MM-yyyy");
					// Consider logging: _logger.LogError(ex, $"Error processing holdings for symbol {holdings.Symbol}");
				}
				holdingResponse.Add(holdings);
      });

      return holdingResponse;

		}

		private async Task<PortfolioAnalyticResult> BuildAnalyticResult(string portfolioId, DateTime asAtDate)
		{
			var holdings = await _portfolioAnalyticsStorage.GetPortfolioDataForADate(portfolioId, asAtDate);
			return new PortfolioAnalyticResult { PortfolioId = portfolioId, PortfolioAnalytics = holdings.Where(h => h.AggregateType == "Holding").ToList() };
		}

		public async Task<List<RptIncomeExpenseDetailsForMis>> GenerateRptIncomeExpenseReportMisAsAt(DateTime asAt)
		{
			var incomeExpenses = await _misDataRepo.GetIncomeExpenseDetailsForMis(asAt);
			return incomeExpenses;
		}

		public async Task<List<RptCapitalRegisterDetailsForMis>> GenerateRptMisCapitalRegisterBetweenDates(DateTime fromDate, DateTime toDate)
		{
			var capitalRegister = await _misDataRepo.GetCapitalRegisterDetailsForMis(fromDate, toDate);
			return capitalRegister;
		}
		public async Task<List<RptModelPortfolioTransactionsDetailsForMis>> GenerateRptModelPortfolioTransactionsMis()
		{
			var modelPortfolioTxns = await _misDataRepo.GetModelPortfolioTransactionsDetailsForMis();
			return modelPortfolioTxns;
		}
		public async Task<List<RptModelPortfolioHoldingsLatestDetailsForMis>> GenerateRptModelPortfolioHoldingsLatestMis()
		{
			var modelPortfolioHoldings = await _misDataRepo.GetModelPortfolioHoldingsLatestDetailsForMis();
			return modelPortfolioHoldings;
		}

		public async Task<List<RptTransactionalForMis>> GenerateRptTransactionalReportMisAsAt(DateTime fromDate, DateTime toDate)
		{
			var capitalRegister = await _misDataRepo.GetTransactionalDetailsForMis(fromDate, toDate);
			return capitalRegister;
		}

		public async Task<List<RptTradesBetweenDates>> GenerateRptTradesReportMisForDates(DateTime fromDate, DateTime toDate)
		{
			var capitalRegister = await _misDataRepo.GetTradesBetweenDatesForMis(fromDate, toDate);
			return capitalRegister;
		}

		public async Task<List<RptClientMasterDetailsForMis>> GenerateRptClientMasterReportMisAsAt(DateTime asAt)
		{
			var clientMaster = await _misDataRepo.GetClientMasterDetailsForMis(asAt);
			return clientMaster;
		}

	}

	public class PortfolioAnalyticResult
	{
		public string PortfolioId { get; set; }
		public List<PortfolioAnalytics> PortfolioAnalytics { get; set; }
	}
}
