using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Actlogica.AlphaPortfolios.ApiContracts.Reports;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage.PerformanceEngine;
using Actlogica.AlphaPortfolios.ServiceIntegration.PerformanceEngine.Portfolio;
using Actlogica.AlphaPortfolios.Utils.Double;
using AutoMapper;
using Newtonsoft.Json;
using RestSharp;


namespace Actlogica.AlphaPortfolios.ServiceIntegration.Reporting.PortfolioPerformanceHistoryReport
{
    public class PortfolioPerformanceHistoryReport : BaseReportGenerationEngine, IPortfolioPerformanceHistoryReport
    {
        private readonly IPortfolioRepository _portfolioRepository;
        private readonly IClientsRepository _clientRepository;
        private readonly IGeneralSettingRepository _generalSettingRepository;
        private readonly AlphaPortfolioDbContext _dbContext;
        private readonly string _tenantName;
        private readonly string _v2PerfApi;
        private readonly IMapper _mapper;
        private readonly PortfolioPerformanceStorageRepository _portfolioPerformanceStorageRepository;
        private readonly PortfolioAnalyticsStorageRepository _portfolioAnalyticsStorageRepository;


        public PortfolioPerformanceHistoryReport(IPortfolioRepository portfolioRepo, IClientsRepository clientRepo, IGeneralSettingRepository generalSettingRepository, AlphaPortfolioDbContext alphaPortfolioDbContext,
        IMapper mapper, PortfolioPerformanceStorageRepository portfolioPerformanceStorageRepository, PortfolioAnalyticsStorageRepository portfolioAnalyticsStorageRepository, string v2PerfApi)
        {
            _portfolioRepository = portfolioRepo;
            _clientRepository = clientRepo;
            _generalSettingRepository = generalSettingRepository;
            _dbContext = alphaPortfolioDbContext;
            _mapper = mapper;
            _portfolioPerformanceStorageRepository = portfolioPerformanceStorageRepository;
            _portfolioAnalyticsStorageRepository = portfolioAnalyticsStorageRepository;
            var settings = Task.Run(() => _generalSettingRepository.GetAll()).Result ?? throw new InvalidOperationException("Storage key not configured for tenant");
            var storageAccountKey = settings.FirstOrDefault(s => s.Key == "StorageAccountKey") ?? throw new KeyNotFoundException("No Storage Account Key Found");
            var tenantClaim = settings.FirstOrDefault(s => s.Key == "TenantName") ?? throw new KeyNotFoundException("No Tenant Name Key Found");
            _tenantName = tenantClaim.Value == "qa" ? "testing" : tenantClaim.Value == "uat" ? "uat" : tenantClaim.Value;
            var _portfolioAumService = new PortfolioAumStorageRepository(_mapper, _tenantName, storageAccountKey.Value);
            _v2PerfApi = v2PerfApi;
        }

        public async Task<RptPerformanceHistory> GetRptPortfolioPerformanceHistory(string portfolioId, DateTime asAtDate)
        {
            var portfolio = await _portfolioRepository.GetById(portfolioId) ?? throw new InvalidOperationException("Portfolio not found.");
            var client = await _clientRepository.GetClientById(portfolio.ClientId);

            var pmsHouseNameSetting = await _generalSettingRepository.GetByKey("PmsHouseName");
            var pmsHouseLogoSetting = await _generalSettingRepository.GetByKey("Logo");

            var performanceHistory = new RptPerformanceHistory
            {
                PortfolioStartDate = portfolio.StartDate.ToString(),
                ClientName = $"{client.FirstName} {client.LastName}",
                ClientStrategyCode = portfolio.ClientStrategyCode,
                AccountNumber = portfolio.FAAccountNo,
                ReportType = "Portfolio Performance Summary",
                PortfolioName = portfolio.Name,
                PortfolioCode = portfolio.ClientStrategyCode,
                ReportGenerationDate = DateTime.Today.ToString("dd MM yyyy"),
                ReportingDate = asAtDate.ToString("dd MM yyyy"),
                ClientPan = client.Pan,
                ClientUniqueCode = portfolio.ClientStrategyCode,
                PmsHouseName = pmsHouseNameSetting?.Value ?? "PMS Private Limited",
                Logo = pmsHouseLogoSetting?.Value ?? string.Empty
            };

            // var benchmarkUrl = $"/portfolio/benchmark/{portfolioId}";
            // var analyticsUrl = $"/portfolio/analytics/{portfolioId}";

            // var restClientclient = new RestClient(_v2PerfApi);
            // var benchmarkRequest = new RestRequest(benchmarkUrl, Method.GET)
            //     .AddQueryParameter("fromDate", asAtDate.ToString("yyyy-MM-dd"))
            //     .AddQueryParameter("toDate", asAtDate.ToString("yyyy-MM-dd"))
            //     .AddQueryParameter("benchmarkIndex", "Nifty50");

            // var analyticsRequest = new RestRequest(analyticsUrl, Method.GET)
            //     .AddQueryParameter("date", asAtDate.ToString("yyyy-MM-dd"));

            // var benchmarkResponse = await restClientclient.ExecuteAsync(benchmarkRequest);
            // var analyticsResponse = await restClientclient.ExecuteAsync(analyticsRequest);

            // var benchmarkList = JsonConvert.DeserializeObject<List<ApiContracts.Reports.BenchmarkData>>(benchmarkResponse.Content);
            // var analytics = JsonConvert.DeserializeObject<ApiContracts.Reports.AnalyticsResponse>(analyticsResponse.Content);

            // var xirr = benchmarkList[0].Xirr;

            // var transaction = new RptPerformanceHistoryTransactions
            // {
            //     InceptionDate = benchmarkList?[0]?.Date.ToString("dd-MM-yyyy") ?? string.Empty,
            //     Assets = analytics?.Aum?[0]?.MarketValue ?? "0",
            //     OneMonth = xirr?.OneMonth != null ? Math.Round(decimal.Parse(xirr.OneMonth), 2).ToString() : "0",
            //     ThreeMonth = xirr?.ThreeMonth != null ? Math.Round(decimal.Parse(xirr.ThreeMonth), 2).ToString() : "0",
            //     SixMonth = xirr?.SixMonth != null ? Math.Round(decimal.Parse(xirr.SixMonth), 2).ToString() : "0",
            //     OneYear = xirr?.OneYear != null ? Math.Round(decimal.Parse(xirr.OneYear), 2).ToString() : "0",
            //     TwoYear = xirr?.TwoYear != null ? Math.Round(decimal.Parse(xirr.TwoYear), 2).ToString() : "0",
            //     ThreeYear = xirr?.ThreeYear != null ? Math.Round(decimal.Parse(xirr.ThreeYear), 2).ToString() : "0",
            //     SevenYear = xirr?.SevenYear != null ? Math.Round(decimal.Parse(xirr.SevenYear), 2).ToString() : "0",
            //     TenYear = xirr?.TenYear != null ? Math.Round(decimal.Parse(xirr.TenYear), 2).ToString() : "0",
            //     Ytd = xirr?.Ytd != null ? Math.Round(decimal.Parse(xirr.Ytd), 2).ToString() : "0",
            //     Fytd = xirr?.Fytd != null ? Math.Round(decimal.Parse(xirr.Fytd), 2).ToString() : "0",
            //     SinceInception = xirr?.SinceInception != null ? Math.Round(decimal.Parse(xirr.SinceInception), 2).ToString() : "0",
            //     Account = portfolio?.Name ?? string.Empty,
            //     AssetsPct = "100",
            // };

            // var totals = new RptPerformanceHistoryTotals
            // {
            //     AssetsTotal = analytics?.Aum?[0]?.MarketValue ?? "0",
            //     AssetsPctTotal = "100"
            // };

            var transactions = new List<RptPerformanceHistoryTransactions>();

            await PortfolioPerformanceEngine.CalculatePerformance(_dbContext, _tenantName, _mapper, portfolioId, asAtDate);

            var analytics = await _portfolioAnalyticsStorageRepository.GetAnalyticsForTheDay(portfolioId, asAtDate);
            var portfolioAnalytic = analytics.FirstOrDefault(a => a.AggregateType == "Portfolio") ?? new ApiContracts.Portfolios.PortfolioAnalytics();

            var portfolioPerformanceChartData = await _portfolioPerformanceStorageRepository.GetPerformanceDataForTheDay(portfolioId, asAtDate);

            var benchmarkPerformance = new RptPerformanceHistoryTransactions
            {
                InceptionDate = portfolio.StartDate.ToString("dd MMM yyyy"),
                Assets = "-",
                AssetsPct = "100",
                OneMonth = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().BenchmarkTwrr1M, 2).ToString() : "0",
                ThreeMonth = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().BenchmarkTwrr3M, 2).ToString() : "0",
                SixMonth = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().BenchmarkTwrr6M, 2).ToString() : "0",
                OneYear = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().BenchmarkTwrr1Y, 2).ToString() : "0",
                ThreeYear = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().BenchmarkTwrr3Y, 2).ToString() : "0",
                SinceInception = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().BenchmarkTwrrSi, 2).ToString() : "0",
                Account = portfolioPerformanceChartData?.FirstOrDefault().BenchmarkName ?? String.Empty,
            };

            var portfolioPerformance = new RptPerformanceHistoryTransactions
            {
                InceptionDate = portfolio.StartDate.ToString("dd MMM yyyy"),
                Assets = portfolioAnalytic.MarketValue.GetRptValue("1", 2),
                AssetsPct = "100",
                OneMonth = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().Twrr1M, 2).ToString() : "0",
                ThreeMonth = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().Twrr3M, 2).ToString() : "0",
                SixMonth = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().Twrr6M, 2).ToString() : "0",
                OneYear = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().Twrr1Y, 2).ToString() : "0",
                ThreeYear = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().Twrr3Y, 2).ToString() : "0",
                SinceInception = portfolioPerformanceChartData.FirstOrDefault() != null ? Math.Round(portfolioPerformanceChartData.FirstOrDefault().TwrrSi, 2).ToString() : "0",
                Account = portfolio?.Name ?? string.Empty,
            };

            transactions.Add(portfolioPerformance);
            transactions.Add(benchmarkPerformance);

            var totals = new RptPerformanceHistoryTotals
            {
                AssetsTotal = portfolioPerformance.Assets ?? "0",
                AssetsPctTotal = "100"
            };

            performanceHistory.Transactions = transactions;
            performanceHistory.Total = totals;

            return performanceHistory;
            
        }

    }

}