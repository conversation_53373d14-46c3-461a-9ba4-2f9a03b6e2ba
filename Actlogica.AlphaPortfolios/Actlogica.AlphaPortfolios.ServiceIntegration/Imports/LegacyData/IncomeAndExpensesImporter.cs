﻿using Actlogica.AlphaPortfolios.ApiContracts.Imports.LegacyData;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Actlogica.AlphaPortfolios.ServiceIntegration.Imports.LegacyData
{
  public class IncomeAndExpensesImporter : IIncomeAndExpensesImporter
  {
    private readonly IClientsRepository _clientRepo;
    private readonly IStrategyRepository _strategyRepo;
    private readonly IStrategyModelRepository _strategyModelRepo;
    private readonly IPortfolioRepository _portfolioRepository;
    private readonly IModelPortfolioRepository _modelPortfolioRepository;
    private readonly IPortfolioCashLedgerRepository _portfolioLedgerRepository;

    public IncomeAndExpensesImporter(IClientsRepository clientRepo, IStrategyRepository strategyRepo, IPortfolioCashLedgerRepository portfolioCashLedgerRepository,
        IStrategyModelRepository strategyModelRepo, IPortfolioRepository portfolioRepository, IModelPortfolioRepository modelPortfolioRepository)
    {
      _clientRepo = clientRepo;
      _strategyRepo = strategyRepo;
      _strategyModelRepo = strategyModelRepo;
      _portfolioRepository = portfolioRepository;
      _modelPortfolioRepository = modelPortfolioRepository;
      _portfolioLedgerRepository = portfolioCashLedgerRepository;
    }

    public async Task<IEnumerable<IncomeExpenseTransactionImport>> CheckDataIntegrity(IEnumerable<IncomeExpenseTransactionImport> ledgerTxnsToImport)
    {
      throw new NotImplementedException();
    }

    public async Task<List<Data.Entity.Db.PortfolioCashLedger>> ImportTransactions(IEnumerable<IncomeExpenseTransactionImport> ledgerTxnsToImport)
    {
      var ledgerTransactions = new List<Data.Entity.Db.PortfolioCashLedger>();

      if (!ledgerTxnsToImport.Any())
        return ledgerTransactions;

      var client = await _clientRepo.GetByClientCode(ledgerTxnsToImport.FirstOrDefault().APClientCode);

      if (client == null)
        throw new InvalidOperationException($"Client not found with AP code: {ledgerTxnsToImport.FirstOrDefault().APClientCode}");

      var portfolio = await _portfolioRepository.GetByClientStrategyCode(ledgerTxnsToImport.FirstOrDefault().ClientCode);

      if (portfolio == null)
      {
        Console.WriteLine($"Portfolio {ledgerTxnsToImport.FirstOrDefault().ClientCode} Not Found , Skipping");
        return ledgerTransactions;
      }

      foreach (var cashLedgerTxnToImport in ledgerTxnsToImport.Where(txn => txn.TransactionSubType.ToLower() != "dividend"))
      {
        var portfolioCashLedgerTxn = new Data.Entity.Db.PortfolioCashLedger
        {
          PortfolioId = portfolio.Id,
          IsModelPortfolio = false,
          Amount = cashLedgerTxnToImport.Amount,
          Description = cashLedgerTxnToImport.Remarks,
          TransactionDate = Convert.ToDateTime(cashLedgerTxnToImport.TransactionDate),
          SettlementDate = Convert.ToDateTime(cashLedgerTxnToImport.SettlementDate),
          TransactionType =
          cashLedgerTxnToImport.TransactionType.ToLower() == "receipt" ?
          ApiContracts.Common.TransactionType.Credit.ToString() : ApiContracts.Common.TransactionType.Debit.ToString(),
          TransactionSubType =
          ((ApiContracts.Common.TransactionSubType)Enum.Parse(typeof(ApiContracts.Common.TransactionSubType), cashLedgerTxnToImport.TransactionSubType)).ToString(),
          RunningBalance = 0.0,
          TxnRefId = null
        };

        ledgerTransactions.Add(portfolioCashLedgerTxn);
      }

      return ledgerTransactions;
    }

    public async Task<List<Data.Entity.Db.PortfolioCashLedger>> ImportBankBookTransactions(IEnumerable<IncomeExpenseTransactionImport> ledgerTxnsToImport)
    {
      var ledgerTransactions = new List<Data.Entity.Db.PortfolioCashLedger>();

      if (!ledgerTxnsToImport.Any())
        return ledgerTransactions;

      var client = await _clientRepo.GetByClientCode(ledgerTxnsToImport.FirstOrDefault().APClientCode);

      if (client == null)
        throw new InvalidOperationException($"Client not found with AP code: {ledgerTxnsToImport.FirstOrDefault().APClientCode}");

      var strategy = await _strategyRepo.GetStrategyByStrategyCode(ledgerTxnsToImport.FirstOrDefault().StrategyCode);
      var strategyModels = await _strategyModelRepo.GetModelsInStrategy(strategy.Id);
      var thisModel = strategyModels.FirstOrDefault(sm => sm.Name == ledgerTxnsToImport.FirstOrDefault().ModelName);
      var portfolio = await _portfolioRepository.GetByClientStrategyCode(ledgerTxnsToImport.FirstOrDefault().ClientCode);

      if (portfolio == null)
      {
        portfolio = new Data.Entity.Db.Portfolio
        {
          Name = $"{strategy.Name} - {thisModel.Name}",
          ClientId = client.Id,
          ClientStrategyCode = ledgerTxnsToImport.FirstOrDefault().ClientCode,
          ModelId = thisModel.Id
        };

        await _portfolioRepository.Insert(portfolio);
        await _portfolioRepository.Commit();
      }

      foreach (var cashLedgerTxnToImport in ledgerTxnsToImport)
      {
        if (string.IsNullOrEmpty(cashLedgerTxnToImport.TransactionSubType))
          continue;
        
        try
        {
          var portfolioCashLedgerTxn = new Data.Entity.Db.PortfolioCashLedger
          {
            PortfolioId = portfolio.Id,
            IsModelPortfolio = false,
            Amount = cashLedgerTxnToImport.Amount,
            Description = cashLedgerTxnToImport.Remarks,
            TransactionDate = Convert.ToDateTime(cashLedgerTxnToImport.TransactionDate),
            SettlementDate = Convert.ToDateTime(cashLedgerTxnToImport.SettlementDate),
            TransactionType =
            cashLedgerTxnToImport.TransactionType.ToLower() == "credit" ?
            ApiContracts.Common.TransactionType.Credit.ToString() : ApiContracts.Common.TransactionType.Debit.ToString(),
            TransactionSubType =
            ((ApiContracts.Common.TransactionSubType)Enum.Parse(typeof(ApiContracts.Common.TransactionSubType), cashLedgerTxnToImport.TransactionSubType)).ToString(),
            RunningBalance = cashLedgerTxnToImport.Balance,
            TxnRefId = null
          };

          ledgerTransactions.Add(portfolioCashLedgerTxn);
        }
        catch(Exception ex)
        {

        }

      }

      return ledgerTransactions;
    }

    public async Task<List<Data.Entity.Db.PortfolioCashLedger>> ImportModelPortfolioTransactions(IEnumerable<IncomeExpenseTransactionImport> ledgerTxnsToImport)
    {
      var ledgerTransactions = new List<Data.Entity.Db.PortfolioCashLedger>();

      //no ledger transactions to import, return the empty collection
      if (!ledgerTxnsToImport.Any())
        return ledgerTransactions;

      var client = await _clientRepo.GetByClientCode(ledgerTxnsToImport.FirstOrDefault().ClientCode);
      var strategy = await _strategyRepo.GetStrategyByStrategyCode(ledgerTxnsToImport.FirstOrDefault().StrategyCode);
      var strategyModels = await _strategyModelRepo.GetModelsInStrategy(strategy.Id);
      var thisModel = strategyModels.FirstOrDefault(sm => sm.Name == ledgerTxnsToImport.FirstOrDefault().ModelName);
      var modelPortfolio = await _modelPortfolioRepository.GetByModelId(thisModel.Id);

      if (modelPortfolio == null)
      {
        modelPortfolio = new Data.Entity.Db.ModelPortfolio
        {
          Name = $"{strategy.Name} - {thisModel.Name}",
          ModelId = thisModel.Id
        };

        await _modelPortfolioRepository.Insert(modelPortfolio);
        await _modelPortfolioRepository.Commit();
      }

      foreach (var cashLedgerTxnToImport in ledgerTxnsToImport.Where(txn => txn.TransactionSubType.ToLower() != "dividend"))
      {
        var portfolioCashLedgerTxn = new Data.Entity.Db.PortfolioCashLedger
        {
          ModelportfolioId = modelPortfolio.Id,
          IsModelPortfolio = true,
          Amount = cashLedgerTxnToImport.Amount,
          Description = cashLedgerTxnToImport.Remarks,
          TransactionDate = Convert.ToDateTime(cashLedgerTxnToImport.TransactionDate),
          SettlementDate = Convert.ToDateTime(cashLedgerTxnToImport.TransactionDate),
          TransactionType =
          cashLedgerTxnToImport.TransactionType.ToLower() == "receipt" ?
          ApiContracts.Common.TransactionType.Credit.ToString() : ApiContracts.Common.TransactionType.Debit.ToString(),
          TransactionSubType =
          ((ApiContracts.Common.TransactionSubType)Enum.Parse(typeof(ApiContracts.Common.TransactionSubType), cashLedgerTxnToImport.TransactionSubType)).ToString(),
          RunningBalance = 0.0,
          TxnRefId = null
        };

        ledgerTransactions.Add(portfolioCashLedgerTxn);
      }

      return ledgerTransactions;
    }


    public List<ReRunCashLedgerImport> LoadDataFromReRunCashLedgerFile(string path)
    {
      var reRunCashLedger = new List<ReRunCashLedgerImport>();
      ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
      using (var stream = File.OpenRead(path))
      using (var package = new ExcelPackage(stream))
      {
        var worksheet = package.Workbook.Worksheets.FirstOrDefault(sheet => sheet.Name == "ImportData") ?? throw new InvalidOperationException("Expecting a sheet with name \"ImportData\" not found");
        int colCount = worksheet.Dimension.End.Column;
        int rowCount = worksheet.Dimension.End.Row;


        for (int row = 2; row <= rowCount; row++)
        {
          if (worksheet.Cells[row, 1].Value == null)
          {
            continue;
          }

          var reRunCashLedgerRow = new ReRunCashLedgerImport();
          reRunCashLedgerRow.ClientStrategyCode = worksheet.Cells[row, 1].Value == null ? String.Empty : worksheet.Cells[row, 1].Value.ToString().Trim();
          reRunCashLedgerRow.ClientName = worksheet.Cells[row, 2].Value == null ? String.Empty : worksheet.Cells[row, 2].Value.ToString().Trim();
          Console.WriteLine(worksheet.Cells[row, 3].Value);
          reRunCashLedgerRow.AsAtDate = Convert.ToDateTime(worksheet.Cells[row, 3].GetValue<DateTime>().ToShortDateString());
          reRunCashLedger.Add(reRunCashLedgerRow);
        }
      }

      return reRunCashLedger;
    }

    public List<DeleteCashLedgerTransactionImport> LoadDataFromDeleteCashLedgerTransactionFile(string path)
    {
      var deleteCashLedgerTxn = new List<DeleteCashLedgerTransactionImport>();
      ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
      using (var stream = File.OpenRead(path))
      using (var package = new ExcelPackage(stream))
      {
        var worksheet = package.Workbook.Worksheets.FirstOrDefault(sheet => sheet.Name == "ImportData") ?? throw new InvalidOperationException("Expecting a sheet with name \"ImportData\" not found");
        int colCount = worksheet.Dimension.End.Column;
        int rowCount = worksheet.Dimension.End.Row;


        for (int row = 2; row <= rowCount; row++)
        {
          if (worksheet.Cells[row, 1].Value == null)
          {
            continue;
          }

          var deleteCashLedgerTxnRow = new DeleteCashLedgerTransactionImport();
          deleteCashLedgerTxnRow.ClientStrategyCode = worksheet.Cells[row, 1].Value == null ? String.Empty : worksheet.Cells[row, 1].Value.ToString().Trim();
          deleteCashLedgerTxnRow.ClientName = worksheet.Cells[row, 2].Value == null ? String.Empty : worksheet.Cells[row, 2].Value.ToString().Trim();
          Console.WriteLine(worksheet.Cells[row, 3].Value);
          deleteCashLedgerTxnRow.AsAtDate = Convert.ToDateTime(worksheet.Cells[row, 3].GetValue<DateTime>().ToShortDateString());

          deleteCashLedgerTxn.Add(deleteCashLedgerTxnRow);
        }
      }

      return deleteCashLedgerTxn;
    }
    public async Task<IEnumerable<IncomeExpenseTransactionImport>> LoadBankBookDataFromFile(string path)
    {
      var incomeExpenseImportData = new List<IncomeExpenseTransactionImport>();
      ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
      using (var stream = File.OpenRead(path))
      using (var package = new ExcelPackage(stream))
      {
        var worksheet = package.Workbook.Worksheets.FirstOrDefault(sheet => sheet.Name == "ImportData");

        if (worksheet == null)
          throw new InvalidOperationException("Expecting a sheet with name \"ImportData\" not found");

        int colCount = worksheet.Dimension.End.Column;
        int rowCount = worksheet.Dimension.End.Row;

        //if (colCount != 10)
        //  throw new InvalidOperationException("Please check the file. You may have not uploaded the wrong file.");

        //if (string.IsNullOrEmpty(worksheet.Cells[1, 1].Value.ToString()))
        //  throw new InvalidOperationException("Please check the file. You may have not uploaded the wrong file.");

        for (int row = 1; row <= rowCount; row++)
        {
          if (worksheet.Cells[row, 1].Value == null)
          {
            continue;
          }

          if (worksheet.Cells[row, 1].Value.ToString().ToLower().Trim() == "clientcode")
          {
            continue;
          }

          var incomeExpenseImportRow = new IncomeExpenseTransactionImport();
          incomeExpenseImportRow.ClientCode = worksheet.Cells[row, 1].Value == null ? String.Empty : worksheet.Cells[row, 1].Value.ToString().Trim();
          incomeExpenseImportRow.ClientName = worksheet.Cells[row, 2].Value == null ? String.Empty : worksheet.Cells[row, 2].Value.ToString().Trim();
          incomeExpenseImportRow.StrategyCode = worksheet.Cells[row, 3].Value == null ? String.Empty : worksheet.Cells[row, 3].Value.ToString().Trim();
          incomeExpenseImportRow.StrategyName = worksheet.Cells[row, 4].Value == null ? String.Empty : worksheet.Cells[row, 4].Value.ToString().Trim();
          incomeExpenseImportRow.ModelName = worksheet.Cells[row, 5].Value == null ? String.Empty : worksheet.Cells[row, 5].Value.ToString().Trim();
          incomeExpenseImportRow.TransactionDate = worksheet.Cells[row, 6].GetValue<DateTime>().ToShortDateString(); //.Value == null ? String.Empty : worksheet.Cells[row, 6].Value.ToString().Trim();
          incomeExpenseImportRow.SettlementDate = worksheet.Cells[row, 7].GetValue<DateTime>().ToShortDateString(); //.Value == null ? String.Empty : worksheet.Cells[row, 6].Value.ToString().Trim();
          incomeExpenseImportRow.TransactionType = worksheet.Cells[row, 8].Value == null ? String.Empty : worksheet.Cells[row, 8].Value.ToString().Trim();
          incomeExpenseImportRow.TransactionSubType = worksheet.Cells[row, 9].Value == null ? String.Empty : worksheet.Cells[row, 9].Value.ToString().Trim();
          incomeExpenseImportRow.Amount = worksheet.Cells[row, 10].Value == null ? 0.0 : Math.Abs(Convert.ToDouble(worksheet.Cells[row, 10].Value.ToString().Trim()));
          incomeExpenseImportRow.Balance = worksheet.Cells[row, 11].Value == null ? 0.0 : Convert.ToDouble(worksheet.Cells[row, 11].Value.ToString().Trim());
          incomeExpenseImportRow.Remarks = worksheet.Cells[row, 12].Value == null ? String.Empty : worksheet.Cells[row, 12].Value.ToString().Trim();
          incomeExpenseImportRow.APClientCode = worksheet.Cells[row, 13].Value == null ? String.Empty : worksheet.Cells[row, 13].Value.ToString().Trim();

          incomeExpenseImportData.Add(incomeExpenseImportRow);
        }
      }

      return incomeExpenseImportData;
    }
    public async Task<IEnumerable<IncomeExpenseTransactionImport>> LoadDataFromFile(string path)
    {
      var incomeExpenseImportData = new List<IncomeExpenseTransactionImport>();
      ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
      using (var stream = File.OpenRead(path))
      using (var package = new ExcelPackage(stream))
      {
        var worksheet = package.Workbook.Worksheets.FirstOrDefault(sheet => sheet.Name == "ImportData");

        if (worksheet == null)
          throw new InvalidOperationException("Expecting a sheet with name \"ImportData\" not found");

        int colCount = worksheet.Dimension.End.Column;
        int rowCount = worksheet.Dimension.End.Row;

        //if (colCount != 10)
        //  throw new InvalidOperationException("Please check the file. You may have not uploaded the wrong file.");

        //if (string.IsNullOrEmpty(worksheet.Cells[1, 1].Value.ToString()))
        //  throw new InvalidOperationException("Please check the file. You may have not uploaded the wrong file.");

        for (int row = 1; row <= rowCount; row++)
        {
          if (worksheet.Cells[row, 1].Value == null)
          {
            continue;
          }

          if (row == 1)
          {
            continue;
          }

          var incomeExpenseImportRow = new IncomeExpenseTransactionImport();
          incomeExpenseImportRow.ClientCode = worksheet.Cells[row, 1].Value == null ? String.Empty : worksheet.Cells[row, 1].Value.ToString().Trim();
          incomeExpenseImportRow.ClientName = worksheet.Cells[row, 2].Value == null ? String.Empty : worksheet.Cells[row, 2].Value.ToString().Trim();
          incomeExpenseImportRow.StrategyCode = worksheet.Cells[row, 3].Value == null ? String.Empty : worksheet.Cells[row, 3].Value.ToString().Trim();
          incomeExpenseImportRow.StrategyName = worksheet.Cells[row, 4].Value == null ? String.Empty : worksheet.Cells[row, 4].Value.ToString().Trim();
          incomeExpenseImportRow.ModelName = worksheet.Cells[row, 5].Value == null ? String.Empty : worksheet.Cells[row, 5].Value.ToString().Trim();
          incomeExpenseImportRow.TransactionDate = worksheet.Cells[row, 6].GetValue<DateTime>().ToShortDateString(); //.Value == null ? String.Empty : worksheet.Cells[row, 6].Value.ToString().Trim();
          incomeExpenseImportRow.SettlementDate = worksheet.Cells[row, 7].GetValue<DateTime>().ToShortDateString(); //.Value == null ? String.Empty : worksheet.Cells[row, 6].Value.ToString().Trim();
          incomeExpenseImportRow.TransactionType = worksheet.Cells[row, 8].Value == null ? String.Empty : worksheet.Cells[row, 8].Value.ToString().Trim();
          incomeExpenseImportRow.TransactionSubType = worksheet.Cells[row, 9].Value == null ? String.Empty : worksheet.Cells[row, 9].Value.ToString().Trim();
          incomeExpenseImportRow.Amount = worksheet.Cells[row, 10].Value == null ? 0.0 : Math.Abs(Convert.ToDouble(worksheet.Cells[row, 10].Value.ToString().Trim()));
          incomeExpenseImportRow.Remarks = worksheet.Cells[row, 11].Value == null ? String.Empty : worksheet.Cells[row, 11].Value.ToString().Trim();
          incomeExpenseImportRow.APClientCode = worksheet.Cells[row, 12].Value == null ? String.Empty : worksheet.Cells[row, 12].Value.ToString().Trim();

          incomeExpenseImportData.Add(incomeExpenseImportRow);
        }
      }

      return incomeExpenseImportData;
    }
  }
}
