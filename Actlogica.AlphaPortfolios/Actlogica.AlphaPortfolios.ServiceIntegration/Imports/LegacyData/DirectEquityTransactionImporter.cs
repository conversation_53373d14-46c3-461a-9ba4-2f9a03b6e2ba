﻿using Actlogica.AlphaPortfolios.ApiContracts.Common;
using Actlogica.AlphaPortfolios.ApiContracts.Imports.LegacyData;
using Actlogica.AlphaPortfolios.ApiContracts.Imports.Validation;
using Actlogica.AlphaPortfolios.Data.Entity.Db;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Accounting;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Common;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.Interfaces;
using Actlogica.AlphaPortfolios.Data.Repositories.Db.OrderMgmt;
using Actlogica.AlphaPortfolios.Data.Repositories.Storage;
using Actlogica.AlphaPortfolios.ServiceIntegration.Common;
using Actlogica.AlphaPortfolios.ServiceIntegration.CorporateActions;
using Actlogica.AlphaPortfolios.ServiceIntegration.PortfolioManagement;
using Actlogica.AlphaPortfolios.ServiceIntegration.SecuritiesMaster;
using Actlogica.AlphaPortfolios.Utils.Dates;
using AutoMapper;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using SecurityType = Actlogica.AlphaPortfolios.ApiContracts.Common.SecurityType;

namespace Actlogica.AlphaPortfolios.ServiceIntegration.Imports.LegacyData
{
  public class DirectEquityTransactionImporter : TransactionImporterBase, IDirectEquityTransactionImporter
  {
    private readonly IClientsRepository _clientRepo;
    private readonly IPortfolioRepository _portfolioRepository;
    private readonly IPortfolioCashLedgerRepository _portfolioLedgerRepository;
    private readonly IPortfolioCapitalRegisterRepository _portfolioCapRegRepo;
    private readonly IInvestmentRepository _investmentRepo;
    private readonly IInvestmentTransactionRepository _investmentTxnRepo;
    private readonly IStrategyRepository _strategyRepo;
    private readonly IStrategyModelRepository _strategyModelRepo;
    private readonly IMapper _mapper;
    private readonly IEquityHistoryRepository _equityHistoryRepo;
    private readonly IModelPortfolioRepository _modelPortfolioRepository;
    private readonly IGeneralSettingRepository _generalSettingRepository;
    private readonly IPortfolioDistributorSharingRepository _portfolioDistributorSharingRepository;
    private List<EquityCompanyMaster> ListOfSecuritiesInThisFile;
    public DirectEquityTransactionImporter(IClientsRepository clientRepo, IPortfolioCashLedgerRepository portfolioCashLedgerRepository,
      IPortfolioCapitalRegisterRepository portfolioCapRegRepository, IMapper mapper, IEquityHistoryRepository equityHistoryRepository,
      IPortfolioRepository portfolioRepository, IInvestmentRepository investmentRepo, IInvestmentTransactionRepository investmentTxnRepo
      , IStrategyRepository strategyRepository, IStrategyModelRepository strategyModelRepository, IModelPortfolioRepository modelPortfolioRepository
      , IGeneralSettingRepository generalSettingRepo, IPortfolioDistributorSharingRepository portfolioDistributorSharingRepository)
    {
      _clientRepo = clientRepo;
      _portfolioRepository = portfolioRepository;
      _modelPortfolioRepository = modelPortfolioRepository;
      _portfolioLedgerRepository = portfolioCashLedgerRepository;
      _portfolioCapRegRepo = portfolioCapRegRepository;
      _investmentRepo = investmentRepo;
      _investmentTxnRepo = investmentTxnRepo;
      _strategyRepo = strategyRepository;
      _strategyModelRepo = strategyModelRepository;
      _equityHistoryRepo = equityHistoryRepository;
      _generalSettingRepository = generalSettingRepo;
      _mapper = mapper;
      ListOfSecuritiesInThisFile = new List<EquityCompanyMaster>();
      _portfolioDistributorSharingRepository = portfolioDistributorSharingRepository;
    }

    public async Task CheckDataIntegrity(IEnumerable<DirectEquityTransactionImport> directEquityTxnsToImport, string destinationFile)
    {
      ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
      var excelPkg = new ExcelPackage();
      var goodDataSheet = excelPkg.Workbook.Worksheets.Add("TxnsWithNoIssues");
      goodDataSheet.TabColor = System.Drawing.Color.Green;
      goodDataSheet.DefaultRowHeight = 12;
      goodDataSheet.Row(1).Height = 20;
      goodDataSheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
      goodDataSheet.Row(1).Style.Font.Bold = true;

      goodDataSheet.Cells[1, 1].Value = "ClientCode";
      goodDataSheet.Cells[1, 2].Value = "ClientName";
      goodDataSheet.Cells[1, 3].Value = "StrategyCode";
      goodDataSheet.Cells[1, 4].Value = "StrategyName";
      goodDataSheet.Cells[1, 5].Value = "ModelName";
      goodDataSheet.Cells[1, 6].Value = "TransactionDate";
      goodDataSheet.Cells[1, 7].Value = "TransactionType";
      goodDataSheet.Cells[1, 8].Value = "MarketRate";
      goodDataSheet.Cells[1, 9].Value = "Quantity";
      goodDataSheet.Cells[1, 10].Value = "TransactionAmount";
      goodDataSheet.Cells[1, 11].Value = "BrokeragePerUnit";
      goodDataSheet.Cells[1, 12].Value = "ServiceTax";
      goodDataSheet.Cells[1, 13].Value = "SttAmount";
      goodDataSheet.Cells[1, 14].Value = "TurnTax";
      goodDataSheet.Cells[1, 15].Value = "OtherTax";
      goodDataSheet.Cells[1, 16].Value = "Symbol";
      goodDataSheet.Cells[1, 17].Value = "SecurityName";
      goodDataSheet.Cells[1, 18].Value = "Isin";
      goodDataSheet.Cells[1, 19].Value = "Exchange";
      int goodDataSheetRecordIndex = 2;

      var txnsWithGoodData = directEquityTxnsToImport.Where(txn => !string.IsNullOrEmpty(txn.Symbol)).ToList();
      foreach (var goodTxnData in txnsWithGoodData)
      {
        goodDataSheet.Cells[goodDataSheetRecordIndex, 1].Value = goodTxnData.ClientCode;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 2].Value = goodTxnData.ClientName;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 3].Value = goodTxnData.StrategyCode;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 4].Value = goodTxnData.StrategyName;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 5].Value = goodTxnData.ModelName;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 6].Value = goodTxnData.TransactionDate;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 7].Value = goodTxnData.TransactionType;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 8].Value = goodTxnData.MarketRate;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 9].Value = goodTxnData.Quantity;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 10].Value = goodTxnData.TransactionAmount;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 11].Value = goodTxnData.BrokeragePerUnit;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 12].Value = goodTxnData.ServiceTax;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 13].Value = goodTxnData.SttAmount;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 14].Value = goodTxnData.TurnTax;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 15].Value = goodTxnData.OtherTax;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 16].Value = goodTxnData.Symbol;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 17].Value = goodTxnData.SecurityName;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 18].Value = goodTxnData.Isin;
        goodDataSheet.Cells[goodDataSheetRecordIndex, 19].Value = goodTxnData.Exchange;
        goodDataSheetRecordIndex++;
      }

      goodDataSheet.Column(1).AutoFit();
      goodDataSheet.Column(2).AutoFit();
      goodDataSheet.Column(3).AutoFit();
      goodDataSheet.Column(4).AutoFit();
      goodDataSheet.Column(5).AutoFit();
      goodDataSheet.Column(6).AutoFit();
      goodDataSheet.Column(7).AutoFit();
      goodDataSheet.Column(8).AutoFit();
      goodDataSheet.Column(9).AutoFit();
      goodDataSheet.Column(10).AutoFit();
      goodDataSheet.Column(11).AutoFit();
      goodDataSheet.Column(12).AutoFit();
      goodDataSheet.Column(13).AutoFit();
      goodDataSheet.Column(14).AutoFit();
      goodDataSheet.Column(15).AutoFit();
      goodDataSheet.Column(16).AutoFit();
      goodDataSheet.Column(17).AutoFit();
      goodDataSheet.Column(18).AutoFit();
      goodDataSheet.Column(19).AutoFit();

      var badDataSheet = excelPkg.Workbook.Worksheets.Add("TxnsWithIssues");
      badDataSheet.TabColor = System.Drawing.Color.Red;
      badDataSheet.DefaultRowHeight = 12;
      badDataSheet.Row(1).Height = 20;
      badDataSheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
      badDataSheet.Row(1).Style.Font.Bold = true;

      badDataSheet.Cells[1, 1].Value = "ClientCode";
      badDataSheet.Cells[1, 2].Value = "ClientName";
      badDataSheet.Cells[1, 3].Value = "StrategyCode";
      badDataSheet.Cells[1, 4].Value = "StrategyName";
      badDataSheet.Cells[1, 5].Value = "ModelName";
      badDataSheet.Cells[1, 6].Value = "TransactionDate";
      badDataSheet.Cells[1, 7].Value = "TransactionType";
      badDataSheet.Cells[1, 8].Value = "MarketRate";
      badDataSheet.Cells[1, 9].Value = "Quantity";
      badDataSheet.Cells[1, 10].Value = "TransactionAmount";
      badDataSheet.Cells[1, 11].Value = "BrokeragePerUnit";
      badDataSheet.Cells[1, 12].Value = "ServiceTax";
      badDataSheet.Cells[1, 13].Value = "SttAmount";
      badDataSheet.Cells[1, 14].Value = "TurnTax";
      badDataSheet.Cells[1, 15].Value = "OtherTax";
      badDataSheet.Cells[1, 16].Value = "Symbol";
      badDataSheet.Cells[1, 17].Value = "SecurityName";
      badDataSheet.Cells[1, 18].Value = "Isin";
      badDataSheet.Cells[1, 19].Value = "Exchange";
      int badDataSheetRecordIndex = 2;

      var txnsWithbadData = directEquityTxnsToImport.Where(txn => string.IsNullOrEmpty(txn.Symbol)).ToList();
      foreach (var badTxnData in txnsWithbadData)
      {
        badDataSheet.Cells[badDataSheetRecordIndex, 1].Value = badTxnData.ClientCode;
        badDataSheet.Cells[badDataSheetRecordIndex, 2].Value = badTxnData.ClientName;
        badDataSheet.Cells[badDataSheetRecordIndex, 3].Value = badTxnData.StrategyCode;
        badDataSheet.Cells[badDataSheetRecordIndex, 4].Value = badTxnData.StrategyName;
        badDataSheet.Cells[badDataSheetRecordIndex, 5].Value = badTxnData.ModelName;
        badDataSheet.Cells[badDataSheetRecordIndex, 6].Value = badTxnData.TransactionDate;
        badDataSheet.Cells[badDataSheetRecordIndex, 7].Value = badTxnData.TransactionType;
        badDataSheet.Cells[badDataSheetRecordIndex, 8].Value = badTxnData.MarketRate;
        badDataSheet.Cells[badDataSheetRecordIndex, 9].Value = badTxnData.Quantity;
        badDataSheet.Cells[badDataSheetRecordIndex, 10].Value = badTxnData.TransactionAmount;
        badDataSheet.Cells[badDataSheetRecordIndex, 11].Value = badTxnData.BrokeragePerUnit;
        badDataSheet.Cells[badDataSheetRecordIndex, 12].Value = badTxnData.ServiceTax;
        badDataSheet.Cells[badDataSheetRecordIndex, 13].Value = badTxnData.SttAmount;
        badDataSheet.Cells[badDataSheetRecordIndex, 14].Value = badTxnData.TurnTax;
        badDataSheet.Cells[badDataSheetRecordIndex, 15].Value = badTxnData.OtherTax;
        badDataSheet.Cells[badDataSheetRecordIndex, 16].Value = badTxnData.Symbol;
        badDataSheet.Cells[badDataSheetRecordIndex, 17].Value = badTxnData.SecurityName;
        badDataSheet.Cells[badDataSheetRecordIndex, 18].Value = badTxnData.Isin;
        badDataSheet.Cells[badDataSheetRecordIndex, 19].Value = badTxnData.Exchange;
        badDataSheetRecordIndex++;
      }

      badDataSheet.Column(1).AutoFit();
      badDataSheet.Column(2).AutoFit();
      badDataSheet.Column(3).AutoFit();
      badDataSheet.Column(4).AutoFit();
      badDataSheet.Column(5).AutoFit();
      badDataSheet.Column(6).AutoFit();
      badDataSheet.Column(7).AutoFit();
      badDataSheet.Column(8).AutoFit();
      badDataSheet.Column(9).AutoFit();
      badDataSheet.Column(10).AutoFit();
      badDataSheet.Column(11).AutoFit();
      badDataSheet.Column(12).AutoFit();
      badDataSheet.Column(13).AutoFit();
      badDataSheet.Column(14).AutoFit();
      badDataSheet.Column(15).AutoFit();
      badDataSheet.Column(16).AutoFit();
      badDataSheet.Column(17).AutoFit();
      badDataSheet.Column(18).AutoFit();
      badDataSheet.Column(19).AutoFit();

      string p_strPath = $"{destinationFile}";
      if (File.Exists(p_strPath))
        File.Delete(p_strPath);

      // Create excel file on physical disk  
      FileStream objFileStrm = File.Create(p_strPath);
      objFileStrm.Close();

      // Write content to excel file  
      File.WriteAllBytes(p_strPath, excelPkg.GetAsByteArray());
      //Close Excel package 
      excelPkg.Dispose();
    }

    public async Task RunPreMigrationCheck(List<DirectEquityTransactionImport> directEquityTransactions, string destinationFile, ISecurityMasterService securityMasterService
			, ITenantSecurityMasterRepository tenantSecMasterRepo, IMarketIndexDataStorageRepository marketDataRepo, IEquityCorporateActionService corporateActionService)
    {
      var isinsInFile = directEquityTransactions.Select(di => di.Isin);
      var isinsInActMaster = (await securityMasterService.GetCompanyMaster()).ToList();
      var isinsInTenantMaster = await tenantSecMasterRepo.GetAll();

      var indexPrices = await marketDataRepo.GetAllCloseValues("16"); //SENSEX
      var listOfSecuritiesValidated = new List<PreMigrationImportValidationResult>();

			#region Data Check & Prep
			foreach (var security in directEquityTransactions.GroupBy(deTxn => deTxn.Isin))
      {
        var securityValidationResult = new PreMigrationImportValidationResult();
        var thisSecurity = isinsInActMaster.FirstOrDefault(sm => sm.Isin == security.Key); 
        securityValidationResult.SecurityType = "Stock";
				securityValidationResult.ImportIsin = security.Key;
				securityValidationResult.ImportSecurityName = security.FirstOrDefault().SecurityName;
				securityValidationResult.FirstTransactionDate = Convert.ToDateTime(security.OrderBy(s => DateTime.ParseExact(s.TransactionDate, "dd-MM-yyyy", CultureInfo.InvariantCulture)).FirstOrDefault().TransactionDate);
				securityValidationResult.LastTransactionDate = Convert.ToDateTime(security.OrderByDescending(s => DateTime.ParseExact(s.TransactionDate, "dd-MM-yyyy", CultureInfo.InvariantCulture)).FirstOrDefault().TransactionDate);

				if (thisSecurity == null && isinsInTenantMaster.FirstOrDefault(sm => sm.Isin == security.Key) == null)
        {
          securityValidationResult.IsinCheckValid = false;
        }
        else
				{
          if(thisSecurity == null)
          {
            var tenantSecurity = isinsInTenantMaster.FirstOrDefault(sm => sm.Isin == security.Key);
            thisSecurity.Isin = tenantSecurity.Isin;
            thisSecurity.CompName = tenantSecurity.Name;
            thisSecurity.Symbol = tenantSecurity.NseSymbol;
            thisSecurity.Scripcode = tenantSecurity.BseSymbol;
          }
          securityValidationResult.MasterIsin = thisSecurity.Isin;
          securityValidationResult.MasterSecurityName = thisSecurity.CompName;
					securityValidationResult.IsinCheckValid = true;
					securityValidationResult.IsListedInBse = thisSecurity.Bse_sublisting == "Active";
					securityValidationResult.IsListedInNse = thisSecurity.Nse_sublisting == "Active";
          securityValidationResult.Scripcode = thisSecurity.Scripcode;
          securityValidationResult.Symbol = thisSecurity.Symbol;
					var sensexPricesDateList = indexPrices.Where(ind => ind.AsAtDate >= securityValidationResult.FirstTransactionDate && ind.AsAtDate <= DateTime.Today).ToList();

					var deTxnDates = new List<DateTime>();
          foreach(var txn in security.ToList())
          {
            deTxnDates.Add(Convert.ToDateTime(txn.TransactionDate));
          }

          if(securityValidationResult.IsListedInBse)
					{
            var allBseSecurityPrices = _equityHistoryRepo.GetEntitiesBetweenDates(securityValidationResult.FirstTransactionDate, DateTime.Today, thisSecurity.Scripcode, "BSE");
            
            var mergedBsePriceRecon = 
              TwoWayDateComparer.MergeDateLists(sensexPricesDateList.Select(dt => dt.AsAtDate).ToList(), 
              allBseSecurityPrices.Select(dt => dt.Date.ConvertToIst()).ToList());
            securityValidationResult.BsePriceRecon = mergedBsePriceRecon;
						securityValidationResult.BseCorporateActions = (await corporateActionService.GetActionsForSymbol(thisSecurity.Scripcode)).ToList();
          }

          if(securityValidationResult.IsListedInNse)
					{
            var allNseSecurityPrices = _equityHistoryRepo.GetEntitiesBetweenDates(securityValidationResult.FirstTransactionDate, DateTime.Today, thisSecurity.Symbol, "NSE");
            var mergedNsePriceRecon = 
              TwoWayDateComparer.MergeDateLists(sensexPricesDateList.Select(dt => dt.AsAtDate).ToList(), 
              allNseSecurityPrices.Select(dt => dt.Date.ConvertToIst()).ToList());
            securityValidationResult.NsePriceRecon = mergedNsePriceRecon;
						securityValidationResult.NseCorporateActions = (await corporateActionService.GetActionsForSymbol(thisSecurity.Symbol)).ToList();
          }
        }

        listOfSecuritiesValidated.Add(securityValidationResult);
      }
			#endregion

			ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
			var excelPkg = new ExcelPackage();

			#region ISIN Check Sheet Configuration
			var isinCheckSheet = excelPkg.Workbook.Worksheets.Add("Isin Summary");
			isinCheckSheet.TabColor = listOfSecuritiesValidated.Any(sec => !sec.IsinCheckValid) ? 
        System.Drawing.Color.Red : System.Drawing.Color.Red;
			isinCheckSheet.DefaultRowHeight = 12;
			isinCheckSheet.Row(1).Height = 20;
			isinCheckSheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
			isinCheckSheet.Row(1).Style.Font.Bold = true;

			isinCheckSheet.Cells[1, 1].Value = "Security Type";
			isinCheckSheet.Cells[1, 2].Value = "Import ISIN";
			isinCheckSheet.Cells[1, 3].Value = "Master ISIN";
			isinCheckSheet.Cells[1, 4].Value = "Import Security Name";
			isinCheckSheet.Cells[1, 5].Value = "Master Security Name";
			isinCheckSheet.Cells[1, 6].Value = "Is ISIN Check Valid";
			isinCheckSheet.Cells[1, 7].Value = "Is Listed in BSE";
			isinCheckSheet.Cells[1, 8].Value = "Is Listed in NSE";
			isinCheckSheet.Cells[1, 9].Value = "First Txn Date";
			isinCheckSheet.Cells[1, 10].Value = "Last Txn Date";
			#endregion

			#region Corp Action Sheet Configuration
			var corpActionSheet = excelPkg.Workbook.Worksheets.Add("Corp Actions");
			corpActionSheet.TabColor = System.Drawing.Color.Yellow;
			corpActionSheet.DefaultRowHeight = 12;
			corpActionSheet.Row(1).Height = 20;
			corpActionSheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
			corpActionSheet.Row(1).Style.Font.Bold = true;

			corpActionSheet.Cells[1, 1].Value = "Symbol";
			corpActionSheet.Cells[1, 2].Value = "Series";
			corpActionSheet.Cells[1, 3].Value = "Subject";
			corpActionSheet.Cells[1, 4].Value = "Ex Date";
			corpActionSheet.Cells[1, 5].Value = "Rec Date";
			corpActionSheet.Cells[1, 6].Value = "Isin";
			corpActionSheet.Cells[1, 7].Value = "CA Broadcast Date";
			corpActionSheet.Cells[1, 8].Value = "CA Type";
			corpActionSheet.Cells[1, 9].Value = "Interest";
			corpActionSheet.Cells[1, 10].Value = "Dividend";
			corpActionSheet.Cells[1, 11].Value = "SplitUnits";
			corpActionSheet.Cells[1, 12].Value = "SplitMultiple";
			corpActionSheet.Cells[1, 13].Value = "BonusUnits";
			corpActionSheet.Cells[1, 14].Value = "BonusMultiple";
			corpActionSheet.Cells[1, 15].Value = "MergerDemergerRatios";
			corpActionSheet.Cells[1, 16].Value = "MergerDemergerPrices";
			corpActionSheet.Cells[1, 17].Value = "DestinationSymbols";
			corpActionSheet.Cells[1, 18].Value = "OrderOfMultipleCA";
			corpActionSheet.Cells[1, 19].Value = "Exchange";
			#endregion

			#region Prices Sheet Configuration
			//var pricesCheckSheet = excelPkg.Workbook.Worksheets.Add("Prices Check");
			//pricesCheckSheet.TabColor = System.Drawing.Color.Blue;
			//pricesCheckSheet.DefaultRowHeight = 12;
			//pricesCheckSheet.Row(1).Height = 20;
			//pricesCheckSheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
			//pricesCheckSheet.Row(1).Style.Font.Bold = true;

			//pricesCheckSheet.Cells[1, 1].Value = "ISIN";
			//pricesCheckSheet.Cells[1, 2].Value = "CompanyName";
			//pricesCheckSheet.Cells[1, 3].Value = "Index Price Date";
			//pricesCheckSheet.Cells[1, 4].Value = "Security Price Date";
			//pricesCheckSheet.Cells[1, 5].Value = "Exchange";
			#endregion

			int isinCheckSheetRecordIndex = 2;
      var corpActionSheetRecordIndex = 2;
      foreach(var validatedSecurity in listOfSecuritiesValidated)
      {
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 1].Value = validatedSecurity.SecurityType;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 2].Value = validatedSecurity.ImportIsin;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 3].Value = validatedSecurity.MasterIsin;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 4].Value = validatedSecurity.ImportSecurityName;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 5].Value = validatedSecurity.MasterSecurityName;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 6].Value = validatedSecurity.IsinCheckValid;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 7].Value = validatedSecurity.IsListedInBse;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 8].Value = validatedSecurity.IsListedInNse;
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 9].Value = validatedSecurity.FirstTransactionDate.ToString("dd-MM-yyyy");
				isinCheckSheet.Cells[isinCheckSheetRecordIndex, 10].Value = validatedSecurity.LastTransactionDate.ToString("dd-MM-yyyy");

        #region CorporateActions
        if (validatedSecurity.BseCorporateActions != null)
        {
          foreach (var corpAction in validatedSecurity.BseCorporateActions)
          {
            corpActionSheet.Cells[corpActionSheetRecordIndex, 1].Value = corpAction.Symbol;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 2].Value = corpAction.Series;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 3].Value = corpAction.Subject;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 4].Value = corpAction.ExDate;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 5].Value = corpAction.RecDate;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 6].Value = corpAction.Isin;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 7].Value = corpAction.CaBroadcastDate;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 8].Value = corpAction.CAType;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 9].Value = corpAction.InterestPerShare;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 10].Value = corpAction.DividendPerShare;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 11].Value = corpAction.SplitUnits;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 12].Value = corpAction.SplitMultiple;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 13].Value = corpAction.BonusUnits;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 14].Value = corpAction.BonusMultiple;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 15].Value = corpAction.MergerDemergerAmalgamationRatios;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 16].Value = corpAction.MergerDemergerAmalgamationPriceRatios;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 17].Value = corpAction.DestinationSymbols;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 18].Value = corpAction.OrderOfMultipleActions;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 19].Value = "BSE";
            corpActionSheetRecordIndex++;
          }
        }

        if(validatedSecurity.NseCorporateActions != null)
        {
          foreach (var corpAction in validatedSecurity.NseCorporateActions)
          {
            corpActionSheet.Cells[corpActionSheetRecordIndex, 1].Value = corpAction.Symbol;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 2].Value = corpAction.Series;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 3].Value = corpAction.Subject;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 4].Value = corpAction.ExDate;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 5].Value = corpAction.RecDate;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 6].Value = corpAction.Isin;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 8].Value = corpAction.CAType;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 9].Value = corpAction.InterestPerShare;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 10].Value = corpAction.DividendPerShare;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 11].Value = corpAction.SplitUnits;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 12].Value = corpAction.SplitMultiple;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 13].Value = corpAction.BonusUnits;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 14].Value = corpAction.BonusMultiple;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 15].Value = corpAction.MergerDemergerAmalgamationRatios;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 16].Value = corpAction.MergerDemergerAmalgamationPriceRatios;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 17].Value = corpAction.DestinationSymbols;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 18].Value = corpAction.OrderOfMultipleActions;
            corpActionSheet.Cells[corpActionSheetRecordIndex, 19].Value = "NSE";
            corpActionSheetRecordIndex++;
          }
        }
				#endregion

				#region Price Checks
				var thisIsinSheet = excelPkg.Workbook.Worksheets.Add($"{validatedSecurity.ImportIsin}");
				thisIsinSheet.TabColor = System.Drawing.Color.Blue;
				thisIsinSheet.DefaultRowHeight = 12;
				thisIsinSheet.Row(1).Height = 20;
				thisIsinSheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
				thisIsinSheet.Row(1).Style.Font.Bold = true;

				thisIsinSheet.Cells[1, 1].Value = "ISIN";
				thisIsinSheet.Cells[1, 2].Value = "BSE Scrip Code";
				thisIsinSheet.Cells[1, 3].Value = "NSE Symbol";
				thisIsinSheet.Cells[1, 4].Value = "CompanyName";
				thisIsinSheet.Cells[1, 5].Value = "Index Price Date";
				thisIsinSheet.Cells[1, 6].Value = "Security Price Date";
				thisIsinSheet.Cells[1, 7].Value = "Exchange";
				var pricesCheckSheetRecordIndex = 2;
				if (validatedSecurity.BsePriceRecon != null)
				{
          foreach (var bsePriceRec in validatedSecurity.BsePriceRecon)
          {

						thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 1].Value = validatedSecurity.MasterIsin;
						thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 2].Value = validatedSecurity.Scripcode;
						thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 3].Value = validatedSecurity.Symbol;
						thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 4].Value = validatedSecurity.MasterSecurityName;
						thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 5].Value = bsePriceRec.Item1 != null ?
              bsePriceRec.Item1.Value.ToString("dd-MM-yyyy") : "No Price";
						thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 6].Value = bsePriceRec.Item2 != null ?
              bsePriceRec.Item2.Value.ToString("dd-MM-yyyy") : "No Price";
						thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 7].Value = "BSE";

            pricesCheckSheetRecordIndex++;
          }
        }

				if (validatedSecurity.NsePriceRecon != null)
        {
				  foreach (var nsePriceRec in validatedSecurity.NsePriceRecon)
				  {
            try
						{
							thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 1].Value = validatedSecurity.MasterIsin;
							thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 2].Value = validatedSecurity.Scripcode;
							thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 3].Value = validatedSecurity.Symbol;
							thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 4].Value = validatedSecurity.MasterSecurityName;
							thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 5].Value = nsePriceRec.Item1 != null ?
									nsePriceRec.Item1.Value.ToString("dd-MM-yyyy") : "No Price";
							thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 6].Value = nsePriceRec.Item2 != null ?
									nsePriceRec.Item2.Value.ToString("dd-MM-yyyy") : "No Price";
							thisIsinSheet.Cells[pricesCheckSheetRecordIndex, 7].Value = "NSE";
							pricesCheckSheetRecordIndex++;
						}
            catch(Exception ex)
            {

            }
				  }
        }
				#endregion
				
        isinCheckSheetRecordIndex++;
			}

			string p_strPath = $"{destinationFile}";
			if (File.Exists(p_strPath))
				File.Delete(p_strPath);

			// Create excel file on physical disk  
			FileStream objFileStrm = File.Create(p_strPath);
			objFileStrm.Close();

			// Write content to excel file  
			File.WriteAllBytes(p_strPath, excelPkg.GetAsByteArray());
			//Close Excel package 
			excelPkg.Dispose();
		}

		public async Task<List<PortfolioCashLedger>> ImportTransactions(IEnumerable<DirectEquityTransactionImport> directEquityTxnsToImport,
      string commonDataConnStr, ISecurityMasterService securityMasterService, bool isMigrationOfTxnsOnly, bool runCorpActions, DateTime corpActionsEndDate)
    {
      var portfolioLedgerTxns = new List<PortfolioLedgerTransactionDto>();
      var ledgerTransactions = new List<PortfolioCashLedger>();

      if (directEquityTxnsToImport.Count() < 1)
        return ledgerTransactions;

      var client = await _clientRepo.GetByClientCode(directEquityTxnsToImport.FirstOrDefault().APClientCode);
      if (client == null)
        throw new InvalidOperationException($"Client not found with AP code: {directEquityTxnsToImport.FirstOrDefault().APClientCode}");


      var corporateActionsService = new CorporateActionsService(_equityHistoryRepo, commonDataConnStr, _mapper, securityMasterService);
      var corpActionsForAllSymbolsInFile =
        (await corporateActionsService.GetCorporateActionsForSymbols(directEquityTxnsToImport.Select(txn => txn.Symbol).Distinct().ToList()))
        .Where(ca => DateTime.Parse(ca.ExDate) <= corpActionsEndDate);
      var generalSettings = await _generalSettingRepository.GetAll();

			var isSttIncludedInCostSetting = generalSettings.FirstOrDefault(sett => sett.Key == "IsSttIncludedInCost");
			var includeSttInCost = false;
			if (isSttIncludedInCostSetting != null)
				includeSttInCost = bool.Parse(isSttIncludedInCostSetting.Value);

			var portfolio = await _portfolioRepository.GetByClientStrategyCode(directEquityTxnsToImport.FirstOrDefault().ClientCode);

      if (portfolio == null)
      {
        Console.WriteLine($"Portfolio {directEquityTxnsToImport.FirstOrDefault().ClientCode} Not Found , Skipping");
        return ledgerTransactions;
      }

      var isinsToRefresh = new List<KeyValuePair<string, List<InvestmentTransaction>>>();
      var isinsIndirectlyAffectedByCorpActions = new List<KeyValuePair<string, List<InvestmentTransaction>>>();
      var txnsGroupedByIsin = directEquityTxnsToImport.GroupBy(txn => txn.Isin);
      foreach (var txnsInThisIsin in txnsGroupedByIsin)
      {
        var securityDetails = ListOfSecuritiesInThisFile.FirstOrDefault(s => s.Isin == txnsInThisIsin.Key);
        if (securityDetails == null)
          continue;

        var identifierToUse = (securityDetails.Nse_sublisting == "Active" && !string.IsNullOrEmpty(securityDetails.Symbol)) ?
          securityDetails.Symbol : securityDetails.Scripcode;

        var securityExchange = (!string.IsNullOrEmpty(securityDetails.Nse_sublisting) && securityDetails.Nse_sublisting.ToLower() != "null" && !string.IsNullOrEmpty(securityDetails.Symbol))
            ? "NSE" : "BSE";
				var defaultSymbol = (securityExchange == "NSE")
						? securityDetails.Symbol : securityDetails.Scripcode;
				var existingTxnsForThisIsinInDb = new List<InvestmentTransaction>();

        var directEquityInvestment = await _investmentRepo.GetByIsinInPortfolio(txnsInThisIsin.FirstOrDefault().Isin, portfolio.Id);

        if (directEquityInvestment != null)
          existingTxnsForThisIsinInDb = (await _investmentTxnRepo.GetTransactionsInInvestment(directEquityInvestment.Id)).ToList();

        existingTxnsForThisIsinInDb.RemoveAll(txn => txn.SubType == TransactionSubType.Dividend.ToString());

        //creation of investment
        if (directEquityInvestment == null)
        {
          directEquityInvestment = new Investment
          {
            ClientId = client.Id,
            PortfolioId = portfolio.Id,
            CurrentPriceDate = DateTime.Today.AddDays(-1),
            Isin = txnsInThisIsin.Key,
            Symbol = defaultSymbol,
            Name = securityDetails.CompName,
            Exchange = securityExchange,
            FirstTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderBy(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            LastTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderByDescending(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            AssetClass = $"{securityDetails.DetermineAssetClass()}",
            Sector = securityDetails.Ind_Shortname,
            MarketCap = securityDetails.Marketcap,
            SecuritySubType = securityDetails.SecuritySubType,
            SecurityType = securityDetails.Status == "ETF" ? $"{SecurityType.ETF}" : $"{SecurityType.Stocks}"
          };

          await _investmentRepo.Insert(directEquityInvestment);
        }
        else
        {
          if (!isMigrationOfTxnsOnly && existingTxnsForThisIsinInDb.Any() && txnsInThisIsin.OrderBy(txn => txn.TransactionDate).FirstOrDefault().TransactionDate.ToDate()
            < existingTxnsForThisIsinInDb.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().TransactionDate)
          {
            throw new InvalidOperationException("Back dated transactions are not allowed in delta import");
          }

          directEquityInvestment.CurrentPriceDate = DateTime.Today.AddDays(-1);
          directEquityInvestment.Symbol = defaultSymbol;
          directEquityInvestment.Name = securityDetails.CompName;
          directEquityInvestment.Exchange = securityExchange;
          directEquityInvestment.LastTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderByDescending(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate);
          directEquityInvestment.Sector = securityDetails.Ind_Shortname;
          directEquityInvestment.MarketCap = securityDetails.Marketcap;
          directEquityInvestment.SecuritySubType = securityDetails.Status == "ETF" ? $"{SecuritySubType.Listed}" : $"{securityDetails.SecuritySubType}";
          directEquityInvestment.SecurityType = securityDetails.Status == "ETF" ? $"{SecurityType.ETF}" : $"{SecurityType.Stocks}";

          await _investmentRepo.Update(directEquityInvestment);
        }
        await _investmentRepo.Commit();

        //adding transactions on the investment
        var txnsToInsert = new List<InvestmentTransaction>();
        foreach (var txnInIsin in txnsInThisIsin)
        {
          var txnToInsert = new InvestmentTransaction
          {
            ClientId = client.Id,
            PortfolioId = portfolio.Id,
            InvestmentId = directEquityInvestment.Id,
            Isin = txnInIsin.Isin,
            Exchange = securityExchange,
            Symbol = defaultSymbol,
            Name = securityDetails.CompName,
            Type = GetTransactionType(txnInIsin).ToString(),
            SubType = GetTransactionSubType(txnInIsin).ToString(),
            TransactionDate = Convert.ToDateTime(txnInIsin.TransactionDate),
            SettlementDate = Convert.ToDateTime(txnInIsin.SettlementDate),
            CGTDate = Convert.ToDateTime(txnInIsin.CGTDate),
            Price = GetTransactionPrice(txnInIsin, includeSttInCost),
            MarketRate = txnInIsin.MarketRate,
            Quantity = txnInIsin.Quantity,
            UnrealisedHolding = GetTransactionType(txnInIsin) == TransactionType.Buy ? txnInIsin.Quantity : 0.0,
            Brokerage = txnInIsin.Quantity * txnInIsin.BrokeragePerUnit,
            ServiceTax = txnInIsin.ServiceTax,
            SttAmount = txnInIsin.SttAmount,
            TurnTax = txnInIsin.TurnTax,
            OtherTax = txnInIsin.OtherTax,
            Amount = txnInIsin.TransactionAmount
          };
          txnToInsert.AcquisitionRate = txnToInsert.Price;

          txnsToInsert.Add(txnToInsert);
        }

				if (isinsToRefresh.Any(isin => isin.Key == identifierToUse))
				{
					var txnsFromMergerDemerger = isinsToRefresh.Where(txn => txn.Key == identifierToUse);
					foreach (var txn in txnsFromMergerDemerger)
					{
						txnsToInsert.AddRange(txn.Value);
					}

					isinsToRefresh.RemoveAll(txn => txn.Key == identifierToUse);
				}

				//adding transactions on the investment
				var txnsForAdjustments = new List<InvestmentTransaction>();
        txnsForAdjustments.AddRange(existingTxnsForThisIsinInDb);
        txnsForAdjustments.AddRange(txnsToInsert);

        //calculating balances and holdings on any specific dates
        CalculateCurrentHoldings(txnsForAdjustments);
        CalculateOpenLotQuantities(txnsForAdjustments);

        var adjustedCorpTxns = new List<InvestmentTransaction>();
        if(runCorpActions)
        {
					//corporate actions
					var corpActionsForThisIsin = corpActionsForAllSymbolsInFile.Where(ca => ca.Symbol == directEquityInvestment.Symbol).ToList();
					var applicableCorpActions =
						corpActionsForThisIsin.Where(ca => DateTime.Parse(ca.ExDate) >= txnsToInsert.OrderBy(txn => txn.TransactionDate).FirstOrDefault().TransactionDate);


					adjustedCorpTxns =
						corporateActionsService.BuildCorpActionTxns(client.Id, portfolio.Id,
							directEquityInvestment.Id, directEquityInvestment.Symbol, applicableCorpActions, txnsForAdjustments, false, out isinsIndirectlyAffectedByCorpActions).ToList();
					isinsToRefresh.AddRange(isinsIndirectlyAffectedByCorpActions);

					//need to rerun holding balance and open lot quantity calculations
					txnsForAdjustments.AddRange(adjustedCorpTxns);
				}

				CalculateCurrentHoldings(txnsForAdjustments.OrderBy(txn => txn.TransactionDate).ToList());
        CalculateOpenLotQuantities(txnsForAdjustments.OrderBy(txn => txn.TransactionDate).ToList());

        directEquityInvestment.CurrentHolding = txnsForAdjustments.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
        directEquityInvestment.Transactions = txnsForAdjustments;
        await _investmentRepo.Update(directEquityInvestment);
        await _investmentRepo.Commit();

        //insert transactions including corp action txns
        foreach (var txn in txnsToInsert.Where(txn =>
        txn.SubType == TransactionSubType.Buy.ToString()
        || txn.SubType == TransactionSubType.Sell.ToString()
        || txn.SubType == TransactionSubType.BuyAdjustment.ToString()
        ))
        {
          var ledgerTxnDto = new PortfolioLedgerTransactionDto
          {
            PortfolioId = txn.PortfolioId,
            Amount = txn.Amount,
            SttAmount = txn.SttAmount,
            TransactionDate = txn.TransactionDate,
            TransactionType = txn.Type,
            TransactionSubType = txn.SubType,
            TransactionRefId = txn.Id
          };
          portfolioLedgerTxns.Add(ledgerTxnDto);
        }

				if (!includeSttInCost)
        {//insert transactions for Security Transaction Tax (STT) in ledger
          foreach (var txn in txnsToInsert.Where(txn =>
          txn.SubType == TransactionSubType.Buy.ToString()
          || txn.SubType == TransactionSubType.Sell.ToString()
          ))
          {
            //If you are not running corporate actions, this means you are running this engine in
            //BAU mode. If you are running in BAU mode, then SELL STT should not be added.
            if (!runCorpActions && txn.SubType == TransactionSubType.Sell.ToString())
              continue;

            var ledgerSttTxnDto = new PortfolioLedgerTransactionDto
            {
              PortfolioId = txn.PortfolioId,
              Amount = txn.SttAmount,
              TransactionDate = txn.TransactionDate,
              SettlementDate = txn.TransactionDate,
              TransactionType = $"{TransactionType.Debit}",
              TransactionSubType = $"{TransactionSubType.Stt}",
              Description = $"{txn.Isin}|{txn.Name}",
              TransactionRefId = txn.Id
            };
            portfolioLedgerTxns.Add(ledgerSttTxnDto);
          }
        }

				#region Dividend Based Code

				var divTdsApplicableDate = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsApplicableDate");
				if (divTdsApplicableDate == null)
					throw new Exception("Dividend TDS (DATE) Settings are not configured for this DB. Please configure these before running migration.");

				var divTdsApplicableRate = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsApplicableRate");
				if (divTdsApplicableRate == null)
					throw new Exception("Dividend TDS (RATE) Settings are not configured for this DB. Please configure these before running migration.");

				var divTdsPreference = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsPreference");
				if (divTdsPreference == null)
					throw new Exception("Dividend TDS Preference Settings are not configured for this DB. Please configure these before running migration.");

				var divTdsAmountThreshold = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsAmountThreshold");
				if (divTdsAmountThreshold == null)
					throw new Exception("Dividend TDS Amount Threshold Settings are not configured for this DB. Please configure these before running migration.");

				var divTdsApplDate = DateTime.ParseExact(divTdsApplicableDate.Value, "dd-MM-yyyy", CultureInfo.CurrentCulture);
				var divTdsAmtThrshld = double.Parse(divTdsAmountThreshold.Value);

				//calculate & insert TDS on Div transactions including
				foreach (var divTxnByIsin in txnsForAdjustments
					.Where(txn => txn.SubType == TransactionSubType.Dividend.ToString() && txn.TransactionDate >= divTdsApplDate).GroupBy(txn => txn.Isin))
				{
					var financialYearsApplicable = FinancialYear.GetNumberOfFinancialYears(divTdsApplDate, DateTime.Today);
					foreach (var finYear in financialYearsApplicable)
					{
						var fyStartDate = finYear.Value;
						var fyEndDate = finYear.Value.AddYears(1).AddDays(-1);
						var divTxnsInThisIsinInThisFy = divTxnByIsin.ToList().Where(txn => txn.TransactionDate >= fyStartDate && txn.TransactionDate <= fyEndDate);

						if (divTxnsInThisIsinInThisFy.Sum(txn => txn.Amount) < divTdsAmtThrshld)
							continue;

						var divTxnCount = 1;
						var divTdsApplied = false;
						var totalDividendCollected = 0.0;
						var tdsDiscountPeriodStartDate = new DateTime(2020, 04, 01);
						var tdsDiscountPeriodEndDate = new DateTime(2021, 03, 31);
						foreach (var divTxn in divTxnsInThisIsinInThisFy)
						{
							totalDividendCollected = totalDividendCollected + divTxn.Amount;
							var tdsRateApplicable = (divTxn.TransactionDate >= tdsDiscountPeriodStartDate && divTxn.TransactionDate <= tdsDiscountPeriodEndDate)
																			? 0.075 : double.Parse(divTdsApplicableRate.Value);

							if (totalDividendCollected >= divTdsAmtThrshld)
							{
								if (divTxnCount == 1 && divTdsApplied == false)
								{
									var applicableTdsThisFy = totalDividendCollected * tdsRateApplicable;
									var lastDivTxnThisFy = divTxn;

									if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
									{
										var capitalRegTxn = new PortfolioCapitalRegister
										{
											IsModelPortfolio = false,
											PortfolioId = lastDivTxnThisFy.PortfolioId,
											ModelportfolioId = null,
											TransactionDate = lastDivTxnThisFy.TransactionDate,
											SettlementDate = lastDivTxnThisFy.TransactionDate,
											TransactionType = $"{TransactionType.Outflow}",
											TransactionSubType = $"{TransactionSubType.CapitalOut}",
											Amount = applicableTdsThisFy,
											TxnRefId = $"{lastDivTxnThisFy.Id}",
											Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
										};

										await _portfolioCapRegRepo.Insert(capitalRegTxn);
									}

									var ledgerTxnDto = new PortfolioLedgerTransactionDto
									{
										PortfolioId = lastDivTxnThisFy.PortfolioId,
										Amount = applicableTdsThisFy,
										TransactionDate = lastDivTxnThisFy.TransactionDate,
										SettlementDate = lastDivTxnThisFy.TransactionDate,
										TransactionType = $"{TransactionType.Debit}",
										TransactionSubType = $"{TransactionSubType.Tds}",
										Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
										TransactionRefId = $"{lastDivTxnThisFy.Id}"
									};
									portfolioLedgerTxns.Add(ledgerTxnDto);

								}
								else if (divTxnCount > 1 && divTdsApplied == false)
								{
									var applicableTdsThisFy = totalDividendCollected * tdsRateApplicable;
									var lastDivTxnThisFy = divTxn;

									if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
									{
										var capitalRegTxn = new PortfolioCapitalRegister
										{
											IsModelPortfolio = false,
											PortfolioId = lastDivTxnThisFy.PortfolioId,
											ModelportfolioId = null,
											TransactionDate = lastDivTxnThisFy.TransactionDate,
											SettlementDate = lastDivTxnThisFy.TransactionDate,
											TransactionType = $"{TransactionType.Outflow}",
											TransactionSubType = $"{TransactionSubType.CapitalOut}",
											Amount = applicableTdsThisFy,
											TxnRefId = $"{lastDivTxnThisFy.Id}",
											Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
										};

										await _portfolioCapRegRepo.Insert(capitalRegTxn);
									}

									var ledgerTxnDto = new PortfolioLedgerTransactionDto
									{
										PortfolioId = lastDivTxnThisFy.PortfolioId,
										Amount = applicableTdsThisFy,
										TransactionDate = lastDivTxnThisFy.TransactionDate,
										SettlementDate = lastDivTxnThisFy.TransactionDate,
										TransactionType = $"{TransactionType.Debit}",
										TransactionSubType = $"{TransactionSubType.Tds}",
										Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
										TransactionRefId = $"{lastDivTxnThisFy.Id}"
									};
									portfolioLedgerTxns.Add(ledgerTxnDto);

								}
								else if (divTxnCount > 1 && divTdsApplied == true)
								{
									var applicableTdsThisFy = divTxn.Amount * tdsRateApplicable;
									var lastDivTxnThisFy = divTxn;

									if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
									{
										var capitalRegTxn = new PortfolioCapitalRegister
										{
											IsModelPortfolio = false,
											PortfolioId = lastDivTxnThisFy.PortfolioId,
											ModelportfolioId = null,
											TransactionDate = lastDivTxnThisFy.TransactionDate,
											SettlementDate = lastDivTxnThisFy.TransactionDate,
											TransactionType = $"{TransactionType.Outflow}",
											TransactionSubType = $"{TransactionSubType.CapitalOut}",
											Amount = applicableTdsThisFy,
											TxnRefId = $"{lastDivTxnThisFy.Id}",
											Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
										};

										await _portfolioCapRegRepo.Insert(capitalRegTxn);
									}

									var ledgerTxnDto = new PortfolioLedgerTransactionDto
									{
										PortfolioId = lastDivTxnThisFy.PortfolioId,
										Amount = applicableTdsThisFy,
										TransactionDate = lastDivTxnThisFy.TransactionDate,
										SettlementDate = lastDivTxnThisFy.TransactionDate,
										TransactionType = $"{TransactionType.Debit}",
										TransactionSubType = $"{TransactionSubType.Tds}",
										Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
										TransactionRefId = $"{lastDivTxnThisFy.Id}"
									};
									portfolioLedgerTxns.Add(ledgerTxnDto);
								}

								divTdsApplied = true;
							}

							divTxnCount++;
						}
					}
				}

				#endregion

				//capital register transactions for SecurityIn & Out
				foreach (var securityTxfrTxn in txnsToInsert.Where(txn => txn.SubType == "SecurityIn" || txn.SubType == "SecurityOut"))
        {
          var capitalRegTxn = new PortfolioCapitalRegister
          {
            PortfolioId = portfolio.Id,
            IsModelPortfolio = false,
            ModelportfolioId = null,
            TransactionDate = securityTxfrTxn.TransactionDate,
            SettlementDate = securityTxfrTxn.TransactionDate,
            TransactionType = securityTxfrTxn.SubType == "SecurityIn" ? TransactionType.Inflow.ToString() : TransactionType.Outflow.ToString(),
            TransactionSubType = securityTxfrTxn.SubType,
            Amount = securityTxfrTxn.Amount,
            Description = $"Legacy upload - {securityTxfrTxn.SubType} of {securityTxfrTxn.Isin} - {securityTxfrTxn.Symbol} - Amount: {securityTxfrTxn.Amount}"
          };

          await _portfolioCapRegRepo.Insert(capitalRegTxn);
          if (capitalRegTxn.TransactionType == "SecurityIn")
          {
            var securityInLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Credit.ToString(),
              TransactionSubType = TransactionSubType.SecurityIn.ToString(),
              TransactionRefId = capitalRegTxn.Id
            };
            portfolioLedgerTxns.Add(securityInLedger);

            var securityInDebitLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Debit.ToString(),
              TransactionSubType = TransactionSubType.Buy.ToString(),
              TransactionRefId = capitalRegTxn.Id
            };
            portfolioLedgerTxns.Add(securityInDebitLedger);
          }
          else if (capitalRegTxn.TransactionType == "SecurityOut")
          {
            var securityOutLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Debit.ToString(),
              TransactionSubType = TransactionSubType.SecurityOut.ToString(),
              TransactionRefId = capitalRegTxn.Id
            };
            portfolioLedgerTxns.Add(securityOutLedger);

            var securityOutCreditLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Credit.ToString(),
              TransactionSubType = TransactionSubType.Sell.ToString(),
              TransactionRefId = capitalRegTxn.Id
            };
            portfolioLedgerTxns.Add(securityOutCreditLedger);
          }
        }

        //ledger entries for corporate actions
        foreach (var corpActionTxn in adjustedCorpTxns)
        {
          if (corpActionTxn.SubType == TransactionSubType.Dividend.ToString() || corpActionTxn.SubType == TransactionSubType.Interest.ToString()
            || corpActionTxn.SubType == TransactionSubType.IncomeSurplus.ToString() || corpActionTxn.SubType == TransactionSubType.ReturnOnCapital.ToString()
            || corpActionTxn.SubType == TransactionSubType.BonusPartial.ToString() || corpActionTxn.SubType == TransactionSubType.SplitPartial.ToString()
            || corpActionTxn.SubType == TransactionSubType.MergerPartial.ToString() || corpActionTxn.SubType == TransactionSubType.DemergerPartial.ToString())
          {
            var ledgerTxnType = TransactionType.Credit.ToString();
            if (corpActionTxn.Type == TransactionType.Buy.ToString())
            {
              ledgerTxnType = TransactionType.Debit.ToString();
            }

            var ledgerCorpActionTxn = new PortfolioCashLedger
            {
              PortfolioId = portfolio.Id,
              ModelportfolioId = null,
              IsModelPortfolio = false,
              Amount = corpActionTxn.Amount,
              Description = $"Corporate Action:{corpActionTxn.SubType} - Investment:{corpActionTxn.Symbol},{corpActionTxn.Isin}",
              SettlementDate = corpActionTxn.SettlementDate,
              TransactionDate = corpActionTxn.TransactionDate,
              TransactionType = ledgerTxnType,
              TransactionSubType = corpActionTxn.SubType,
              TxnRefId = $"{corpActionTxn.Id}"
            };

            ledgerTransactions.Add(ledgerCorpActionTxn);
          }
        }

			}

			//ledger entry for STT prepared
			var transactionsInPortfolioLedgerForStt = portfolioLedgerTxns.Where(txn => txn.TransactionSubType == TransactionSubType.Stt.ToString());
      foreach(var sttTxn in transactionsInPortfolioLedgerForStt)
      {
				var ledgerSttTxn = new PortfolioCashLedger
				{
					PortfolioId = portfolio.Id,
					ModelportfolioId = null,
					IsModelPortfolio = false,
					Amount = sttTxn.Amount,
					Description = $"Security Trx Tax|{sttTxn.Description}",
					SettlementDate = sttTxn.SettlementDate,
					TransactionDate = sttTxn.TransactionDate,
					TransactionType = TransactionType.Debit.ToString(),
					TransactionSubType = TransactionSubType.Stt.ToString(),
					TxnRefId = $"{sttTxn.TransactionRefId}"
				};

				ledgerTransactions.Add(ledgerSttTxn);
			}

      //ledger entries for transactions that are Originally BUY & SELL. BuyAdjustment included because of Split making the original buy
      //as adjusted buy i.e. SubType: BuyAdjustment
      var transactionsInPortfolioLedger = portfolioLedgerTxns.Where(txn => txn.TransactionSubType == TransactionSubType.Buy.ToString()
      || txn.TransactionSubType == TransactionSubType.BuyAdjustment.ToString()
      || txn.TransactionSubType == TransactionSubType.Sell.ToString());
      foreach (var ledgerEntry in transactionsInPortfolioLedger)
      {
        var sttAmount = 0.0;
        if (!includeSttInCost)
          sttAmount = ledgerEntry.SttAmount;

        var ledgerDate = ledgerEntry.TransactionDate;
        var debitAmountTotal = ledgerEntry.TransactionType == TransactionType.Buy.ToString() ? ledgerEntry.Amount - sttAmount : 0.0;
        var creditAmountTotal = ledgerEntry.TransactionType == TransactionType.Sell.ToString() ? ledgerEntry.Amount + sttAmount : 0.0;

        if (debitAmountTotal > 0)
        {
          var ledgerBuyTxn = new PortfolioCashLedger
          {
            PortfolioId = portfolio.Id,
            ModelportfolioId = null,
            IsModelPortfolio = false,
            Amount = debitAmountTotal,
            Description = $"Securities bought Amount reflects net",
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = TransactionType.Debit.ToString(),
            TransactionSubType = TransactionSubType.Buy.ToString(),
            TxnRefId = $"{ledgerEntry.TransactionRefId}"
          };

          ledgerTransactions.Add(ledgerBuyTxn);
        }

        if (creditAmountTotal > 0)
        {
          var ledgerSellTxn = new PortfolioCashLedger
          {
            PortfolioId = portfolio.Id,
            ModelportfolioId = null,
            IsModelPortfolio = false,
            Amount = creditAmountTotal,
            Description = $"Securities sold/dividend Amount reflects net",
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = TransactionType.Credit.ToString(),
            TransactionSubType = TransactionSubType.Sell.ToString(),
            TxnRefId = $"{ledgerEntry.TransactionRefId}"
          };

          ledgerTransactions.Add(ledgerSellTxn);
        }
      }

      //ledger entries for Dividend TDS
      foreach (var txnDate in portfolioLedgerTxns.Where(txn => txn.TransactionSubType == TransactionSubType.Tds.ToString()))
      {
        var ledgerDate = txnDate.TransactionDate;
        var debitAmountTotal = txnDate.Amount;

        if (debitAmountTotal > 0)
        {
          var ledgerBuyTxn = new PortfolioCashLedger
          {
            PortfolioId = portfolio.Id,
            IsModelPortfolio = false,
            ModelportfolioId = null,
            Amount = debitAmountTotal,
            Description = txnDate.Description,
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = txnDate.TransactionType,
            TransactionSubType = txnDate.TransactionSubType,
            TxnRefId = txnDate.TransactionRefId
          };

          ledgerTransactions.Add(ledgerBuyTxn);
        }
      }

      await _investmentRepo.Commit();
      await _investmentTxnRepo.Commit();
      await _portfolioCapRegRepo.Commit();

			#region adjustment for MergerDemerger


			foreach (var isinIndirectlyAffected in isinsToRefresh)
			{
				var investmentSecurityDetails = await securityMasterService.GetStockDetailsBySymbol(isinIndirectlyAffected.Key, "EQ", "NSE");

				var identifier = string.Empty;
				if ((investmentSecurityDetails.Nse_sublisting == "Active" && !string.IsNullOrEmpty(investmentSecurityDetails.Symbol)))
				{
					isinIndirectlyAffected.Value.ToList().ForEach(txn => { txn.Symbol = investmentSecurityDetails.Symbol; txn.Exchange = "NSE"; });
					identifier = investmentSecurityDetails.Symbol;
				}
				else
				{
					isinIndirectlyAffected.Value.ToList().ForEach(txn => { txn.Symbol = investmentSecurityDetails.Scripcode; txn.Exchange = "BSE"; });
					identifier = investmentSecurityDetails.Scripcode;
				}

				var txnsInDb = new List<InvestmentTransaction>();
				var mergerDemergerTxnsInThisIsin = isinIndirectlyAffected.Value;
				var existingInvestment = await _investmentRepo.GetBySymbolInPortfolio(identifier, portfolio.Id);

				if (existingInvestment == null)
				{

					existingInvestment = new Investment
					{
						PortfolioId = portfolio.Id,
						CurrentPriceDate = DateTime.Today.AddDays(-1),
						Isin = investmentSecurityDetails.Isin,
						Symbol = identifier,
						Name = investmentSecurityDetails.CompName,
						Exchange = mergerDemergerTxnsInThisIsin.FirstOrDefault().Exchange,
						FirstTransactionDate = Convert.ToDateTime(mergerDemergerTxnsInThisIsin.OrderBy(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
						LastTransactionDate = Convert.ToDateTime(mergerDemergerTxnsInThisIsin.OrderByDescending(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
						AssetClass = $"{investmentSecurityDetails.DetermineAssetClass()}",
						Sector = investmentSecurityDetails.Ind_Shortname,
						MarketCap = investmentSecurityDetails.Marketcap,
						SecurityType = investmentSecurityDetails.Status == "ETF" ? $"{SecurityType.ETF}" : $"{SecurityType.Stocks}",
						SecuritySubType = $"{SecuritySubType.Listed}"
					};

					txnsInDb.AddRange(isinIndirectlyAffected.Value);

					CalculateCurrentHoldings(txnsInDb);
					CalculateOpenLotQuantities(txnsInDb);

					existingInvestment.CurrentHolding = txnsInDb.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
					existingInvestment.Transactions = txnsInDb;
					await _investmentRepo.Insert(existingInvestment);
				}
				else
				{
					txnsInDb.AddRange(isinIndirectlyAffected.Value);

					if (existingInvestment.Transactions != null)
						txnsInDb.AddRange(existingInvestment.Transactions);

					CalculateCurrentHoldings(txnsInDb);
					CalculateOpenLotQuantities(txnsInDb);

					existingInvestment.CurrentHolding = txnsInDb.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
					existingInvestment.Transactions = txnsInDb;
					await _investmentRepo.Update(existingInvestment);
				}

				await _investmentRepo.Commit();
			}


			#endregion
			
      return ledgerTransactions;
    }


    public async Task<List<PortfolioCashLedger>> ImportLegacyTransactions(IEnumerable<DirectEquityTransactionImport> directEquityTransactionImports, string commonDataConnStr, ISecurityMasterService securityMasterService, IMutualFundInvestmentRepository mutualFundInvestmentRepository, IMutualFundTransactionRepository mutualFundTransactionRepository, IPoolCapitalRegisterRepository poolCapitalRegisterRepository, IPoolCashLedgerRepository poolCashLedgerRepository, ITradeOrderUnsettledAmountsRepository tradeOrderUnsettledAmountsRepository)
    {

      var portfolioLedgerTxns = new List<PortfolioLedgerTransactionDto>();
      var ledgerTransactions = new List<PortfolioCashLedger>();

      var corporateActionsService = new CorporateActionsService(_equityHistoryRepo, commonDataConnStr, _mapper, securityMasterService);


      var corpActionsForAllSymbolsInFile =
        await corporateActionsService.GetCorporateActionsForSymbols(directEquityTransactionImports.Select(txn => txn.Symbol).Distinct().ToList());
      var sortTxns = directEquityTransactionImports.OrderBy(tx => tx.TransactionDate);

      var client = await _clientRepo.GetByClientCode(directEquityTransactionImports.FirstOrDefault().APClientCode);
      if (client == null)
        throw new InvalidOperationException($"Client not found with AP code: {directEquityTransactionImports.FirstOrDefault()?.APClientCode}");
      var strategy = await _strategyRepo.GetStrategyByStrategyCode(directEquityTransactionImports.FirstOrDefault().StrategyCode);
      var strategyModels = await _strategyModelRepo.GetModelsInStrategy(strategy.Id);
      var thisModel = strategyModels.FirstOrDefault(sm => sm.Name == directEquityTransactionImports.FirstOrDefault().ModelName);
      var portfolio = await _portfolioRepository.GetByClientIdAndModelId(thisModel?.Id, client.Id);

      var portfolioService = new PortfolioService(_mapper, _investmentRepo, _investmentTxnRepo,
        mutualFundInvestmentRepository, mutualFundTransactionRepository, poolCapitalRegisterRepository, poolCashLedgerRepository, _portfolioCapRegRepo,
        _portfolioLedgerRepository, _portfolioRepository, _modelPortfolioRepository, securityMasterService, tradeOrderUnsettledAmountsRepository, _clientRepo, _portfolioDistributorSharingRepository);

      var ledgerBuilder = new PortfolioLedgerBuilder(portfolio.Id,
        _portfolioLedgerRepository, portfolioService);

      //Group the uploaded transaction by ISIN
      var mappedInvestmentTxnsGroupedByIsin = sortTxns.GroupBy(x => x.Isin);

      var generalSettings = await _generalSettingRepository.GetAll();
			var isSttIncludedInCost = generalSettings.FirstOrDefault(sett => sett.Key == "IsSttIncludedInCost");
			var includeSttInCost = false;
			if (isSttIncludedInCost != null)
				includeSttInCost = bool.Parse(isSttIncludedInCost.Value);


			var isinsToRefresh = new List<KeyValuePair<string, List<InvestmentTransaction>>>();
      var isinsIndirectlyAffectedByCorpActions = new List<KeyValuePair<string, List<InvestmentTransaction>>>();

      var globalLowestDate = DateTime.MinValue;
      foreach (var txnByIsin in mappedInvestmentTxnsGroupedByIsin)
      {
        var securityDetails = ListOfSecuritiesInThisFile.FirstOrDefault(s => s.Isin == txnByIsin.Key);
        if (securityDetails == null)
          continue;


        DateTime lowestDate = DateTime.ParseExact(txnByIsin.First().TransactionDate, "dd-MM-yyyy",
                             System.Globalization.CultureInfo.InvariantCulture);

        if (globalLowestDate == DateTime.MinValue)
        {
          globalLowestDate = lowestDate;
        }
        if (lowestDate < globalLowestDate)
        {
          globalLowestDate = lowestDate;
        }

        var existingTxnsForThisIsinInDb = new List<InvestmentTransaction>();

        //Investment for the current ISIN
        var directEquityInvestment = await _investmentRepo.GetByIsinInPortfolio(txnByIsin.FirstOrDefault()?.Isin, portfolio.Id);
        if (directEquityInvestment == null)
        {
          throw new Exception("Portfolio Doesn't Exist");
        }

        //Get the Transactions for this ISIN
        existingTxnsForThisIsinInDb = (await _investmentTxnRepo.GetTransactionsInInvestment(directEquityInvestment.Id)).ToList();


        var txnsToInsert = new List<InvestmentTransaction>();

        //Loop through the user Uploaded transactions
        foreach (var txnInIsin in txnByIsin)
        {
          var txnToInsert = new InvestmentTransaction
          {
            ClientId = client.Id,
            PortfolioId = portfolio.Id,
            InvestmentId = directEquityInvestment.Id,
            Isin = txnInIsin.Isin,
            Exchange = txnInIsin.Exchange,
            Symbol = txnInIsin.Symbol,
            Name = securityDetails.CompName,
            Type = GetTransactionType(txnInIsin).ToString(),
            SubType = GetTransactionSubType(txnInIsin).ToString(),
            TransactionDate = Convert.ToDateTime(txnInIsin.TransactionDate),
            SettlementDate = Convert.ToDateTime(txnInIsin.SettlementDate),
            CGTDate = Convert.ToDateTime(txnInIsin.CGTDate),
            Price = GetTransactionPrice(txnInIsin, includeSttInCost),
            MarketRate = txnInIsin.MarketRate,
            AcquisitionRate = txnInIsin.AcquisitionRate,
            Quantity = txnInIsin.Quantity,
            UnrealisedHolding = GetTransactionType(txnInIsin) == TransactionType.Buy ? txnInIsin.Quantity : 0.0,
            Brokerage = txnInIsin.Quantity * txnInIsin.BrokeragePerUnit,
            ServiceTax = txnInIsin.ServiceTax,
            SttAmount = txnInIsin.SttAmount,
            TurnTax = txnInIsin.TurnTax,
            OtherTax = txnInIsin.OtherTax,
            Amount = txnInIsin.TransactionAmount
          };
          txnsToInsert.Add(txnToInsert);
        }

        //Add Both transaction from db and file
        //Sort It according to transaction Date
        var allTransactions = new List<InvestmentTransaction>();
        allTransactions.AddRange(txnsToInsert);
        allTransactions.AddRange(existingTxnsForThisIsinInDb);
        allTransactions = allTransactions.OrderBy(x => x.TransactionDate).ToList();

        var transactionToBeDeleted = allTransactions.Where(x => x.TransactionDate >= lowestDate && x.SubType != TransactionSubType.Buy.ToString() && x.SubType != TransactionSubType.Sell.ToString() && x.SubType != TransactionSubType.SecurityIn.ToString() && x.SubType != TransactionSubType.SecurityOut.ToString());

        //Delete the entry from Portfolio Cash Ledger for this investment transaction (Use TxnRefId and use Contain Function and the Id)
        foreach (var item in transactionToBeDeleted)
        {
          //Delete By Id
          await ledgerBuilder.DeleteTransactionByTxnRefId(item.Id);

          //Delete By Checking whether the TxnRefId has the Symbol
          await ledgerBuilder.DeleteTransactionByTxnRefIdContains(item.Symbol);
        }

        await _investmentTxnRepo.DeleteRange(transactionToBeDeleted.ToList());

        //Transactions that needs to be inserted where the Corporate Action needs to be computed
        //The Transaction that occured after the lowestDate which has subtype other than Buy,SecurityIn,Sell,SecurityOut should be not be considered (It should be deleted)
        var txnsForAdjustments = new List<InvestmentTransaction>();
        txnsForAdjustments.AddRange(allTransactions.Where(x => x.TransactionDate >= lowestDate && (x.SubType == TransactionSubType.Buy.ToString() || x.SubType == TransactionSubType.Sell.ToString() || x.SubType == TransactionSubType.SecurityIn.ToString() || x.SubType == TransactionSubType.SecurityOut.ToString())));
        txnsForAdjustments.AddRange(allTransactions.Where(x => x.TransactionDate < lowestDate));

        //calculating balances and holdings on any specific dates
        CalculateCurrentHoldings(txnsForAdjustments);
        CalculateOpenLotQuantities(txnsForAdjustments);

        //corporate actions
        var corpActionsForThisIsin = corpActionsForAllSymbolsInFile.Where(ca => ca.Symbol == directEquityInvestment.Symbol).ToList();
        var applicableCorpActions =
          corpActionsForThisIsin.Where(ca => DateTime.Parse(ca.ExDate) >= lowestDate && DateTime.Parse(ca.ExDate) >= txnsToInsert.OrderBy(txn => txn.TransactionDate).FirstOrDefault().TransactionDate);
        var adjustedCorpTxns =
          corporateActionsService.BuildCorpActionTxnsForLegacyTransactions(client.Id, portfolio.Id,
            directEquityInvestment.Id, directEquityInvestment.Symbol, applicableCorpActions, txnsForAdjustments, false, out isinsIndirectlyAffectedByCorpActions, lowestDate);
        isinsToRefresh.AddRange(isinsIndirectlyAffectedByCorpActions);

        //need to rerun holding balance and open lot quantity calculations
        txnsForAdjustments.AddRange(adjustedCorpTxns);
        CalculateCurrentHoldings(txnsForAdjustments.OrderBy(txn => txn.TransactionDate).ToList());
        CalculateOpenLotQuantities(txnsForAdjustments.OrderBy(txn => txn.TransactionDate).ToList());


        directEquityInvestment.CurrentHolding = txnsForAdjustments.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
        directEquityInvestment.Transactions = txnsForAdjustments;
        await _investmentRepo.Update(directEquityInvestment);
        await _investmentRepo.Commit();

				//insert transactions including corp action txns
				foreach (var txn in txnsToInsert.Where(txn =>
        txn.SubType == TransactionSubType.Buy.ToString()
        || txn.SubType == TransactionSubType.Sell.ToString()
        || txn.SubType == TransactionSubType.BuyAdjustment.ToString()
        ))
        {
          var ledgerTxnDto = new PortfolioLedgerTransactionDto
          {
            PortfolioId = txn.PortfolioId,
            Amount = txn.Amount,
            TransactionDate = txn.TransactionDate,
            TransactionType = txn.Type,
            TransactionSubType = txn.SubType
          };
          portfolioLedgerTxns.Add(ledgerTxnDto);
        }

				if (!includeSttInCost)
				{//insert transactions for Security Transaction Tax (STT) in ledger
					foreach (var txn in txnsToInsert.Where(txn =>
					txn.SubType == TransactionSubType.Buy.ToString()
					|| txn.SubType == TransactionSubType.Sell.ToString()
					))
					{
						var ledgerSttTxnDto = new PortfolioLedgerTransactionDto
						{
							PortfolioId = txn.PortfolioId,
							Amount = txn.SttAmount,
							TransactionDate = txn.TransactionDate,
							SettlementDate = txn.TransactionDate,
							TransactionType = $"{TransactionType.Debit}",
							TransactionSubType = $"{TransactionSubType.Stt}",
							Description = $"{txn.Isin}|{txn.Name}",
							TransactionRefId = txn.Id
						};
						portfolioLedgerTxns.Add(ledgerSttTxnDto);
					}
				}

				var divTdsApplicableDate = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsApplicableDate");
        if (divTdsApplicableDate == null)
          throw new Exception("Dividend TDS (DATE) Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsApplicableRate = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsApplicableRate");
        if (divTdsApplicableRate == null)
          throw new Exception("Dividend TDS (RATE) Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsPreference = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsPreference");
        if (divTdsPreference == null)
          throw new Exception("Dividend TDS Preference Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsAmountThreshold = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsAmountThreshold");
        if (divTdsAmountThreshold == null)
          throw new Exception("Dividend TDS Amount Threshold Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsApplDate = DateTime.ParseExact(divTdsApplicableDate.Value, "dd-MM-yyyy", CultureInfo.CurrentCulture);
        var divTdsAmtThrshld = double.Parse(divTdsAmountThreshold.Value);

        //calculate & insert TDS on Div transactions including
        foreach (var divTxnByIsin in txnsForAdjustments
          .Where(txn => txn.SubType == TransactionSubType.Dividend.ToString() && txn.TransactionDate >= divTdsApplDate).GroupBy(txn => txn.Isin))
        {
          var financialYearsApplicable = FinancialYear.GetNumberOfFinancialYears(divTdsApplDate, DateTime.Today);
          foreach (var finYear in financialYearsApplicable)
          {
            var fyStartDate = finYear.Value;
            var fyEndDate = finYear.Value.AddYears(1).AddDays(-1);
            var divTxnsInThisIsinInThisFy = divTxnByIsin.ToList().Where(txn => txn.TransactionDate >= fyStartDate && txn.TransactionDate <= fyEndDate);

            if (divTxnsInThisIsinInThisFy.Sum(txn => txn.Amount) < divTdsAmtThrshld)
              continue;

            var divTxnCount = 1;
            var divTdsApplied = false;
            var totalDividendCollected = 0.0;
            foreach (var divTxn in divTxnsInThisIsinInThisFy)
            {
              totalDividendCollected = totalDividendCollected + divTxn.Amount;

              if (totalDividendCollected >= divTdsAmtThrshld)
              {
                if (divTxnCount == 1 && divTdsApplied == false)
                {
                  var applicableTdsThisFy = totalDividendCollected * double.Parse(divTdsApplicableRate.Value);
                  var lastDivTxnThisFy = divTxn;

                  if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
                  {
                    var capitalRegTxn = new PortfolioCapitalRegister
                    {
                      IsModelPortfolio = false,
                      PortfolioId = lastDivTxnThisFy.Id,
                      ModelportfolioId = null,
                      TransactionDate = lastDivTxnThisFy.TransactionDate,
                      SettlementDate = lastDivTxnThisFy.TransactionDate,
                      TransactionType = $"{TransactionType.Outflow}",
                      TransactionSubType = $"{TransactionSubType.CapitalOut}",
                      Amount = applicableTdsThisFy,
                      TxnRefId = $"{lastDivTxnThisFy.Id}",
                      Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
                    };

                    await _portfolioCapRegRepo.Insert(capitalRegTxn);
                  }

                  var ledgerTxnDto = new PortfolioLedgerTransactionDto
                  {
                    PortfolioId = lastDivTxnThisFy.PortfolioId,
                    Amount = applicableTdsThisFy,
                    TransactionDate = lastDivTxnThisFy.TransactionDate,
                    SettlementDate = lastDivTxnThisFy.TransactionDate,
                    TransactionType = $"{TransactionType.Debit}",
                    TransactionSubType = $"{TransactionSubType.Tds}",
                    Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
                    TransactionRefId = $"{lastDivTxnThisFy.Id}"
                  };
                  portfolioLedgerTxns.Add(ledgerTxnDto);

                }
                else if (divTxnCount > 1 && divTdsApplied == false)
                {
                  var applicableTdsThisFy = totalDividendCollected * double.Parse(divTdsApplicableRate.Value);
                  var lastDivTxnThisFy = divTxn;

                  if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
                  {
                    var capitalRegTxn = new PortfolioCapitalRegister
                    {
                      IsModelPortfolio = false,
                      PortfolioId = lastDivTxnThisFy.Id,
                      ModelportfolioId = null,
                      TransactionDate = lastDivTxnThisFy.TransactionDate,
                      SettlementDate = lastDivTxnThisFy.TransactionDate,
                      TransactionType = $"{TransactionType.Outflow}",
                      TransactionSubType = $"{TransactionSubType.CapitalOut}",
                      Amount = applicableTdsThisFy,
                      TxnRefId = $"{lastDivTxnThisFy.Id}",
                      Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
                    };

                    await _portfolioCapRegRepo.Insert(capitalRegTxn);
                  }

                  var ledgerTxnDto = new PortfolioLedgerTransactionDto
                  {
                    PortfolioId = lastDivTxnThisFy.PortfolioId,
                    Amount = applicableTdsThisFy,
                    TransactionDate = lastDivTxnThisFy.TransactionDate,
                    SettlementDate = lastDivTxnThisFy.TransactionDate,
                    TransactionType = $"{TransactionType.Debit}",
                    TransactionSubType = $"{TransactionSubType.Tds}",
                    Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
                    TransactionRefId = $"{lastDivTxnThisFy.Id}"
                  };
                  portfolioLedgerTxns.Add(ledgerTxnDto);

                }
                else if (divTxnCount > 1 && divTdsApplied == true)
                {
                  var applicableTdsThisFy = divTxn.Amount * double.Parse(divTdsApplicableRate.Value);
                  var lastDivTxnThisFy = divTxn;

                  if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
                  {
                    var capitalRegTxn = new PortfolioCapitalRegister
                    {
                      IsModelPortfolio = false,
                      PortfolioId = lastDivTxnThisFy.Id,
                      ModelportfolioId = null,
                      TransactionDate = lastDivTxnThisFy.TransactionDate,
                      SettlementDate = lastDivTxnThisFy.TransactionDate,
                      TransactionType = $"{TransactionType.Outflow}",
                      TransactionSubType = $"{TransactionSubType.CapitalOut}",
                      Amount = applicableTdsThisFy,
                      TxnRefId = $"{lastDivTxnThisFy.Id}",
                      Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
                    };

                    await _portfolioCapRegRepo.Insert(capitalRegTxn);
                  }

                  var ledgerTxnDto = new PortfolioLedgerTransactionDto
                  {
                    PortfolioId = lastDivTxnThisFy.PortfolioId,
                    Amount = applicableTdsThisFy,
                    TransactionDate = lastDivTxnThisFy.TransactionDate,
                    SettlementDate = lastDivTxnThisFy.TransactionDate,
                    TransactionType = $"{TransactionType.Debit}",
                    TransactionSubType = $"{TransactionSubType.Tds}",
                    Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
                    TransactionRefId = $"{lastDivTxnThisFy.Id}"
                  };
                  portfolioLedgerTxns.Add(ledgerTxnDto);
                }

                divTdsApplied = true;
              }

              divTxnCount++;
            }
          }
        }

        //capital register transactions for SecurityIn & Out
        foreach (var securityTxfrTxn in txnsToInsert.Where(txn => txn.SubType == "SecurityIn" || txn.SubType == "SecurityOut"))
        {
          var capitalRegTxn = new PortfolioCapitalRegister
          {
            PortfolioId = portfolio.Id,
            IsModelPortfolio = false,
            ModelportfolioId = null,
            TransactionDate = securityTxfrTxn.TransactionDate,
            SettlementDate = securityTxfrTxn.TransactionDate,
            TransactionType = securityTxfrTxn.SubType == "SecurityIn" ? TransactionType.Inflow.ToString() : TransactionType.Outflow.ToString(),
            TransactionSubType = securityTxfrTxn.SubType,
            Amount = securityTxfrTxn.Amount,
            Description = $"Legacy upload - {securityTxfrTxn.SubType} of {securityTxfrTxn.Isin} - {securityTxfrTxn.Symbol} - Amount: {securityTxfrTxn.Amount}"
          };

          await _portfolioCapRegRepo.Insert(capitalRegTxn);
          if (capitalRegTxn.TransactionType == "SecurityIn")
          {
            var securityInLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Credit.ToString(),
              TransactionSubType = TransactionSubType.SecurityIn.ToString()
            };
            portfolioLedgerTxns.Add(securityInLedger);

            var securityInDebitLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Debit.ToString(),
              TransactionSubType = TransactionSubType.Buy.ToString()
            };
            portfolioLedgerTxns.Add(securityInDebitLedger);
          }
          else if (capitalRegTxn.TransactionType == "SecurityOut")
          {
            var securityOutLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Debit.ToString(),
              TransactionSubType = TransactionSubType.SecurityOut.ToString()
            };
            portfolioLedgerTxns.Add(securityOutLedger);

            var securityOutCreditLedger = new PortfolioLedgerTransactionDto
            {
              PortfolioId = capitalRegTxn.PortfolioId,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Credit.ToString(),
              TransactionSubType = TransactionSubType.Sell.ToString()
            };
            portfolioLedgerTxns.Add(securityOutCreditLedger);
          }
        }

        //ledger entries for corporate actions
        foreach (var corpActionTxn in adjustedCorpTxns)
        {
          if (corpActionTxn.SubType == TransactionSubType.Dividend.ToString() || corpActionTxn.SubType == TransactionSubType.Interest.ToString()
            || corpActionTxn.SubType == TransactionSubType.IncomeSurplus.ToString() || corpActionTxn.SubType == TransactionSubType.ReturnOnCapital.ToString()
            || corpActionTxn.SubType == TransactionSubType.BonusPartial.ToString() || corpActionTxn.SubType == TransactionSubType.SplitPartial.ToString()
            || corpActionTxn.SubType == TransactionSubType.MergerPartial.ToString())
          {
            var ledgerTxnType = TransactionType.Credit.ToString();
            if (corpActionTxn.Type == TransactionType.Buy.ToString())
            {
              ledgerTxnType = TransactionType.Debit.ToString();
            }

            var ledgerCorpActionTxn = new PortfolioCashLedger
            {
              PortfolioId = portfolio.Id,
              ModelportfolioId = null,
              IsModelPortfolio = false,
              Amount = corpActionTxn.Amount,
              Description = $"Corporate Action:{corpActionTxn.SubType} - Investment:{corpActionTxn.Symbol},{corpActionTxn.Isin}",
              SettlementDate = corpActionTxn.SettlementDate,
              TransactionDate = corpActionTxn.TransactionDate,
              TransactionType = ledgerTxnType,
              TransactionSubType = corpActionTxn.SubType,
              TxnRefId = $"{corpActionTxn.Isin}-{corpActionTxn.Symbol}-{corpActionTxn.SubType}"
            };

            ledgerTransactions.Add(ledgerCorpActionTxn);
          }
        }

      }

      //ledger entries for transactions that are Originally BUY & SELL. BuyAdjustment included because of Split making the original buy
      //as adjusted buy i.e. SubType: BuyAdjustment
      var transactionsGroupedByDate = portfolioLedgerTxns.Where(txn => txn.TransactionSubType == TransactionSubType.Buy.ToString()
      || txn.TransactionSubType == TransactionSubType.BuyAdjustment.ToString()
      || txn.TransactionSubType == TransactionSubType.Sell.ToString()).GroupBy(txn => txn.TransactionDate);
      foreach (var txnDate in transactionsGroupedByDate)
      {
        var ledgerDate = txnDate.Key;
        var debitAmountTotal = txnDate.Where(txn => txn.TransactionType == TransactionType.Buy.ToString()).Sum(txn => txn.Amount);
        var creditAmountTotal = txnDate.Where(txn => txn.TransactionType == TransactionType.Sell.ToString()).Sum(txn => txn.Amount);

        if (debitAmountTotal > 0)
        {
          var ledgerBuyTxn = new PortfolioCashLedger
          {
            PortfolioId = portfolio.Id,
            ModelportfolioId = null,
            IsModelPortfolio = false,
            Amount = debitAmountTotal,
            Description = $"Securities bought Amount reflects net",
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = TransactionType.Debit.ToString(),
            TransactionSubType = TransactionSubType.Buy.ToString(),
            TxnRefId = $"{ledgerDate}"
          };

          ledgerTransactions.Add(ledgerBuyTxn);
        }

        if (creditAmountTotal > 0)
        {
          var ledgerSellTxn = new PortfolioCashLedger
          {
            PortfolioId = portfolio.Id,
            ModelportfolioId = null,
            IsModelPortfolio = false,
            Amount = creditAmountTotal,
            Description = $"Securities sold/dividend Amount reflects net",
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = TransactionType.Credit.ToString(),
            TransactionSubType = TransactionSubType.Sell.ToString(),
            TxnRefId = $"{ledgerDate}"
          };

          ledgerTransactions.Add(ledgerSellTxn);
        }
      }

      //ledger entries for Dividend TDS
      foreach (var txnDate in portfolioLedgerTxns.Where(txn => txn.TransactionSubType == TransactionSubType.Tds.ToString()))
      {
        var ledgerDate = txnDate.TransactionDate;
        var debitAmountTotal = txnDate.Amount;

        if (debitAmountTotal > 0)
        {
          var ledgerBuyTxn = new PortfolioCashLedger
          {
            PortfolioId = portfolio.Id,
            IsModelPortfolio = false,
            ModelportfolioId = null,
            Amount = debitAmountTotal,
            Description = txnDate.Description,
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = txnDate.TransactionType,
            TransactionSubType = txnDate.TransactionSubType,
            TxnRefId = txnDate.TransactionRefId
          };

          ledgerTransactions.Add(ledgerBuyTxn);
        }
      }

      await _investmentTxnRepo.Commit();
      await _portfolioCapRegRepo.Commit();

      foreach (var isinIndirectlyAffected in isinsToRefresh)
      {
        var txnsInDb = new List<InvestmentTransaction>();
        var txnsInThisIsin = isinIndirectlyAffected.Value;
        var existingInvestment = await _investmentRepo.GetBySymbolInPortfolio(isinIndirectlyAffected.Key, portfolio.Id);

        if (existingInvestment == null)
        {
          var investmentSecurityDetails = await securityMasterService.GetStockDetailsBySymbol(isinIndirectlyAffected.Key);
          existingInvestment = new Investment
          {
            PortfolioId = portfolio.Id,
            CurrentPriceDate = DateTime.Today.AddDays(-1),
            Isin = investmentSecurityDetails.Isin,
            Symbol = investmentSecurityDetails.Symbol,
            Name = investmentSecurityDetails.CompName,
            Exchange = txnsInThisIsin.FirstOrDefault().Exchange,
            FirstTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderBy(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            LastTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderByDescending(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            AssetClass = $"{investmentSecurityDetails.DetermineAssetClass()}",
            Sector = investmentSecurityDetails.Ind_Shortname,
            MarketCap = investmentSecurityDetails.Marketcap,
            SecurityType = investmentSecurityDetails.Status == "ETF" ? $"{SecurityType.ETF}" : $"{SecurityType.Stocks}",
            SecuritySubType = $"{SecuritySubType.Listed}"
          };

          txnsInDb.AddRange(isinIndirectlyAffected.Value);

          CalculateCurrentHoldings(txnsInDb);
          CalculateOpenLotQuantities(txnsInDb);

          existingInvestment.CurrentHolding = txnsInDb.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
          existingInvestment.Transactions = txnsInDb;
          await _investmentRepo.Insert(existingInvestment);
        }
        else
        {
          txnsInDb.AddRange(await _investmentTxnRepo.GetTransactionsInInvestmentUsingContext(existingInvestment.Id));
          txnsInThisIsin.ForEach(txn => txnsInDb.Add(txn));
          CalculateCurrentHoldings(txnsInDb);
          CalculateOpenLotQuantities(txnsInDb);

          existingInvestment.CurrentHolding = txnsInDb.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
          existingInvestment.Transactions = txnsInDb;
          await _investmentRepo.Update(existingInvestment);
        }

        await _investmentRepo.Commit();
      }


      await ledgerBuilder.ComputeRunningBalanceFromAsAtDate(globalLowestDate, portfolio.ClientStrategyCode);
      return ledgerTransactions;
    }


    public async Task<List<PortfolioCashLedger>> ImportModelPortfolioTransactions(IEnumerable<DirectEquityTransactionImport> directEquityTxnsToImport,
      string commonDataConnStr, ISecurityMasterService securityMasterService)
    {
      var portfolioLedgerTxns = new List<PortfolioLedgerTransactionDto>();
      var ledgerTransactions = new List<PortfolioCashLedger>();
      var strategy = await _strategyRepo.GetStrategyByStrategyCode(directEquityTxnsToImport.FirstOrDefault().StrategyCode);
      var strategyModels = await _strategyModelRepo.GetModelsInStrategy(strategy.Id);
      var thisModel = await _strategyModelRepo.GetModelWithSecurities(strategyModels.FirstOrDefault(sm => sm.Name == directEquityTxnsToImport.FirstOrDefault().ModelName).Id);
      var modelPortfolio = await _modelPortfolioRepository.GetByModelId(thisModel.Id);
      var corporateActionsService = new CorporateActionsService(_equityHistoryRepo, commonDataConnStr, _mapper, securityMasterService);
      var corpActionsForAllSymbolsInFile = await corporateActionsService.GetCorporateActionsForSymbols(directEquityTxnsToImport.Select(txn => txn.Symbol).Distinct().ToList());
      var generalSettings = await _generalSettingRepository.GetAll();
			var isSttIncludedInCost = generalSettings.FirstOrDefault(sett => sett.Key == "IsSttIncludedInCost");
			var includeSttInCost = false;
			if (isSttIncludedInCost != null)
				includeSttInCost = bool.Parse(isSttIncludedInCost.Value);


			if (modelPortfolio == null)
      {
        modelPortfolio = new ModelPortfolio
        {
          Name = $"{strategy.Name} - {thisModel.Name}",
          ModelId = thisModel.Id
        };

        await _modelPortfolioRepository.Insert(modelPortfolio);
        await _modelPortfolioRepository.Commit();
      }

      var isinsToRefresh = new List<KeyValuePair<string, List<InvestmentTransaction>>>();
      var isinsIndirectlyAffectedByCorpActions = new List<KeyValuePair<string, List<InvestmentTransaction>>>();
      var investmentsInModel = new List<Investment>();
      var txnsGroupedByIsin = directEquityTxnsToImport.GroupBy(txn => txn.Isin);
      foreach (var txnsInThisIsin in txnsGroupedByIsin)
      {
        var securityDetails = ListOfSecuritiesInThisFile.FirstOrDefault(s => s.Isin == txnsInThisIsin.Key);
        if (securityDetails == null)
          continue;

        if (string.IsNullOrEmpty(txnsInThisIsin.FirstOrDefault().Symbol))
          continue;

        if (string.IsNullOrEmpty(securityDetails.Symbol) && string.IsNullOrEmpty(securityDetails.Scripcode))
          continue;

        var directEquityInvestment = await _investmentRepo.GetByIsinInModelPortfolio(txnsInThisIsin.FirstOrDefault().Isin, modelPortfolio.Id);

        //creation of investment
        if (directEquityInvestment == null)
        {
          directEquityInvestment = new Investment
          {
            ModelportfolioId = modelPortfolio.Id,
            CurrentPriceDate = DateTime.Today.AddDays(-1),
            Isin = txnsInThisIsin.Key,
            Symbol = txnsInThisIsin.FirstOrDefault().Symbol,
            Name = securityDetails.CompName,
            Exchange = txnsInThisIsin.FirstOrDefault().Exchange,
            FirstTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderBy(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            LastTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderByDescending(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            AssetClass = $"{securityDetails.DetermineAssetClass()}",
            Sector = securityDetails.Industry,
            MarketCap = securityDetails.Marketcap,
            SecurityType = securityDetails.Status == "ETF" ? $"{SecurityType.ETF}" : $"{SecurityType.Stocks}",
            SecuritySubType = $"{SecuritySubType.Listed}"
          };

          investmentsInModel.Add(directEquityInvestment);

          await _investmentRepo.Insert(directEquityInvestment);
        }
        else
        {
          directEquityInvestment.CurrentPriceDate = DateTime.Today.AddDays(-1);
          directEquityInvestment.Symbol = txnsInThisIsin.FirstOrDefault().Symbol;
          directEquityInvestment.Name = securityDetails.CompName;
          directEquityInvestment.Exchange = txnsInThisIsin.FirstOrDefault().Exchange;
          directEquityInvestment.FirstTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderBy(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate);
          directEquityInvestment.LastTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderByDescending(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate);
          directEquityInvestment.Sector = securityDetails.Ind_Shortname;
          directEquityInvestment.MarketCap = securityDetails.Marketcap;
          directEquityInvestment.SecurityType = securityDetails.Status == "ETF" ? $"{SecurityType.ETF}" : $"{SecurityType.Stocks}";
          directEquityInvestment.SecuritySubType = $"{SecuritySubType.Listed}";

          await _investmentRepo.Update(directEquityInvestment);
        }
        await _investmentRepo.Commit();

        //adding transactions on the investment
        var txnsToInsert = new List<InvestmentTransaction>();
        foreach (var txnInIsin in txnsInThisIsin)
        {
          var txnToInsert = new InvestmentTransaction
          {
            InvestmentId = directEquityInvestment.Id,
            Isin = txnInIsin.Isin,
            Exchange = txnInIsin.Exchange,
            Symbol = txnInIsin.Symbol,
            Name = securityDetails.CompName,
            Type = GetTransactionType(txnInIsin).ToString(),
            SubType = GetTransactionSubType(txnInIsin).ToString(),
            TransactionDate = Convert.ToDateTime(txnInIsin.TransactionDate),
            SettlementDate = Convert.ToDateTime(txnInIsin.SettlementDate),
            CGTDate = Convert.ToDateTime(txnInIsin.CGTDate),
            Price = GetTransactionPrice(txnInIsin, includeSttInCost),
            MarketRate = txnInIsin.MarketRate,
            AcquisitionRate = txnInIsin.AcquisitionRate,
            Quantity = txnInIsin.Quantity,
            UnrealisedHolding = GetTransactionType(txnInIsin) == TransactionType.Buy ? txnInIsin.Quantity : 0.0,
            Brokerage = txnInIsin.Quantity * txnInIsin.BrokeragePerUnit,
            ServiceTax = txnInIsin.ServiceTax,
            SttAmount = txnInIsin.SttAmount,
            TurnTax = txnInIsin.TurnTax,
            OtherTax = txnInIsin.OtherTax,
            Amount = txnInIsin.TransactionAmount
          };

          txnsToInsert.Add(txnToInsert);
        }

        //calculating balances and holdings on any specific dates
        CalculateCurrentHoldings(txnsToInsert);
        CalculateOpenLotQuantities(txnsToInsert);

        if (txnsInThisIsin.FirstOrDefault().Symbol == "RELIANCE")
        {
          var a = 1;
        }

        //corporate actions
        var corpActionsForThisIsin = corpActionsForAllSymbolsInFile.Where(ca => ca.Symbol == directEquityInvestment.Symbol).ToList();
        var adjustedCorpTxns =
          corporateActionsService.BuildCorpActionTxns(null, modelPortfolio.Id,
            directEquityInvestment.Id, directEquityInvestment.Symbol, corpActionsForThisIsin, txnsToInsert, true, out isinsIndirectlyAffectedByCorpActions);
        isinsToRefresh.AddRange(isinsIndirectlyAffectedByCorpActions);

        //need to rerun holding balance and open lot quantity calculations
        txnsToInsert.AddRange(adjustedCorpTxns);
        CalculateCurrentHoldings(txnsToInsert.OrderBy(txn => txn.TransactionDate).ToList());
        CalculateOpenLotQuantities(txnsToInsert.OrderBy(txn => txn.TransactionDate).ToList());

        directEquityInvestment.CurrentHolding = txnsToInsert.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
        directEquityInvestment.Transactions = txnsToInsert;
        await _investmentRepo.Update(directEquityInvestment);
        await _investmentRepo.Commit();

        //insert transactions including corp action txns
        foreach (var txn in txnsToInsert.Where(txn => txn.SubType ==
        TransactionSubType.Buy.ToString()
        || txn.SubType == TransactionSubType.Sell.ToString()
        || txn.SubType == TransactionSubType.BuyAdjustment.ToString()
        ))
        {
          //await _investmentTxnRepo.Insert(txn);
          var ledgerTxnDto = new PortfolioLedgerTransactionDto
          {
            ModelPortfolioId = modelPortfolio.Id,
            Amount = txn.Amount,
            TransactionDate = txn.TransactionDate,
            TransactionType = txn.Type,
            TransactionSubType = txn.SubType
          };
          portfolioLedgerTxns.Add(ledgerTxnDto);
        }

				if (!includeSttInCost)
				{//insert transactions for Security Transaction Tax (STT) in ledger
					foreach (var txn in txnsToInsert.Where(txn =>
					txn.SubType == TransactionSubType.Buy.ToString()
					|| txn.SubType == TransactionSubType.Sell.ToString()
					))
					{
						var ledgerSttTxnDto = new PortfolioLedgerTransactionDto
						{
							ModelPortfolioId = modelPortfolio.Id,
							Amount = txn.SttAmount,
							TransactionDate = txn.TransactionDate,
							SettlementDate = txn.TransactionDate,
							TransactionType = $"{TransactionType.Debit}",
							TransactionSubType = $"{TransactionSubType.Stt}",
							Description = $"{txn.Isin}|{txn.Name}",
							TransactionRefId = txn.Id
						};
						portfolioLedgerTxns.Add(ledgerSttTxnDto);
					}
				}

				var divTdsApplicableDate = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsApplicableDate");
        if (divTdsApplicableDate == null)
          throw new Exception("Dividend TDS (DATE) Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsApplicableRate = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsApplicableRate");
        if (divTdsApplicableRate == null)
          throw new Exception("Dividend TDS (RATE) Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsPreference = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsPreference");
        if (divTdsPreference == null)
          throw new Exception("Dividend TDS Preference Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsAmountThreshold = generalSettings.FirstOrDefault(sett => sett.Key == "DividendTdsAmountThreshold");
        if (divTdsAmountThreshold == null)
          throw new Exception("Dividend TDS Amount Threshold Settings are not configured for this DB. Please configure these before running migration.");

        var divTdsApplDate = DateTime.Parse(divTdsApplicableDate.Value);
        var divTdsAmtThrshld = double.Parse(divTdsAmountThreshold.Value);
        //calculate & insert TDS on Div transactions including
        foreach (var divTxnByIsin in txnsToInsert
          .Where(txn => txn.SubType == TransactionSubType.Dividend.ToString() && txn.TransactionDate >= divTdsApplDate).GroupBy(txn => txn.Isin))
        {
          var financialYearsApplicable = FinancialYear.GetNumberOfFinancialYears(divTdsApplDate, DateTime.Today);
          foreach (var finYear in financialYearsApplicable)
          {
            var fyStartDate = finYear.Value;
            var fyEndDate = finYear.Value.AddYears(1).AddDays(-1);
            var divTxnsInThisIsinInThisFy = divTxnByIsin.ToList().Where(txn => txn.TransactionDate >= fyStartDate && txn.TransactionDate <= fyEndDate);

            if (divTxnsInThisIsinInThisFy.Sum(txn => txn.Amount) < divTdsAmtThrshld)
              continue;

            var divTxnCount = 1;
            var divTdsApplied = false;
            var totalDividendCollected = 0.0;
            foreach (var divTxn in divTxnsInThisIsinInThisFy)
            {
              totalDividendCollected = totalDividendCollected + divTxn.Amount;

              if (totalDividendCollected >= divTdsAmtThrshld)
              {
                if (divTxnCount == 1 && divTdsApplied == false)
                {
                  var applicableTdsThisFy = totalDividendCollected * double.Parse(divTdsApplicableRate.Value);
                  var lastDivTxnThisFy = divTxn;

                  if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
                  {
                    var capitalRegTxn = new PortfolioCapitalRegister
                    {
                      IsModelPortfolio = true,
                      ModelportfolioId = lastDivTxnThisFy.Id,
                      TransactionDate = lastDivTxnThisFy.TransactionDate,
                      SettlementDate = lastDivTxnThisFy.TransactionDate,
                      TransactionType = $"{TransactionType.Outflow}",
                      TransactionSubType = $"{TransactionSubType.CapitalOut}",
                      Amount = applicableTdsThisFy,
                      TxnRefId = $"{lastDivTxnThisFy.Id}",
                      Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
                    };

                    await _portfolioCapRegRepo.Insert(capitalRegTxn);
                  }

                  var ledgerTxnDto = new PortfolioLedgerTransactionDto
                  {
                    ModelPortfolioId = lastDivTxnThisFy.PortfolioId,
                    Amount = applicableTdsThisFy,
                    TransactionDate = lastDivTxnThisFy.TransactionDate,
                    SettlementDate = lastDivTxnThisFy.TransactionDate,
                    TransactionType = $"{TransactionType.Debit}",
                    TransactionSubType = $"{TransactionSubType.Tds}",
                    Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
                    TransactionRefId = $"{lastDivTxnThisFy.Id}"
                  };
                  portfolioLedgerTxns.Add(ledgerTxnDto);

                }
                else if (divTxnCount > 1 && divTdsApplied == false)
                {
                  var applicableTdsThisFy = totalDividendCollected * double.Parse(divTdsApplicableRate.Value);
                  var lastDivTxnThisFy = divTxn;

                  if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
                  {
                    var capitalRegTxn = new PortfolioCapitalRegister
                    {
                      IsModelPortfolio = true,
                      ModelportfolioId = lastDivTxnThisFy.Id,
                      TransactionDate = lastDivTxnThisFy.TransactionDate,
                      SettlementDate = lastDivTxnThisFy.TransactionDate,
                      TransactionType = $"{TransactionType.Outflow}",
                      TransactionSubType = $"{TransactionSubType.CapitalOut}",
                      Amount = applicableTdsThisFy,
                      TxnRefId = $"{lastDivTxnThisFy.Id}",
                      Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
                    };

                    await _portfolioCapRegRepo.Insert(capitalRegTxn);
                  }

                  var ledgerTxnDto = new PortfolioLedgerTransactionDto
                  {
                    ModelPortfolioId = lastDivTxnThisFy.PortfolioId,
                    Amount = applicableTdsThisFy,
                    TransactionDate = lastDivTxnThisFy.TransactionDate,
                    SettlementDate = lastDivTxnThisFy.TransactionDate,
                    TransactionType = $"{TransactionType.Debit}",
                    TransactionSubType = $"{TransactionSubType.Tds}",
                    Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
                    TransactionRefId = $"{lastDivTxnThisFy.Id}"
                  };
                  portfolioLedgerTxns.Add(ledgerTxnDto);

                }
                else if (divTxnCount > 1 && divTdsApplied == true)
                {
                  var applicableTdsThisFy = divTxn.Amount * double.Parse(divTdsApplicableRate.Value);
                  var lastDivTxnThisFy = divTxn;

                  if (divTdsPreference.Value == $"{DividendTdsApplicabilityPreference.CapitalRegisterAndLedger}")
                  {
                    var capitalRegTxn = new PortfolioCapitalRegister
                    {
                      IsModelPortfolio = true,
                      ModelportfolioId = lastDivTxnThisFy.Id,
                      TransactionDate = lastDivTxnThisFy.TransactionDate,
                      SettlementDate = lastDivTxnThisFy.TransactionDate,
                      TransactionType = $"{TransactionType.Outflow}",
                      TransactionSubType = $"{TransactionSubType.CapitalOut}",
                      Amount = applicableTdsThisFy,
                      TxnRefId = $"{lastDivTxnThisFy.Id}",
                      Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment"
                    };

                    await _portfolioCapRegRepo.Insert(capitalRegTxn);
                  }

                  var ledgerTxnDto = new PortfolioLedgerTransactionDto
                  {
                    ModelPortfolioId = lastDivTxnThisFy.PortfolioId,
                    Amount = applicableTdsThisFy,
                    TransactionDate = lastDivTxnThisFy.TransactionDate,
                    SettlementDate = lastDivTxnThisFy.TransactionDate,
                    TransactionType = $"{TransactionType.Debit}",
                    TransactionSubType = $"{TransactionSubType.Tds}",
                    Description = $"{lastDivTxnThisFy.Isin}-{lastDivTxnThisFy.Symbol}:Dividend TDS Payment",
                    TransactionRefId = $"{lastDivTxnThisFy.Id}"
                  };
                  portfolioLedgerTxns.Add(ledgerTxnDto);
                }

                divTdsApplied = true;
              }

              divTxnCount++;
            }
          }
        }

        //capital register transactions for SecurityIn & Out
        foreach (var securityTxfrTxn in txnsToInsert.Where(txn => txn.SubType == "SecurityIn" || txn.SubType == "SecurityOut"))
        {
          var capitalRegTxn = new PortfolioCapitalRegister
          {
            ModelportfolioId = modelPortfolio.Id,
            IsModelPortfolio = true,
            TransactionDate = securityTxfrTxn.TransactionDate,
            SettlementDate = securityTxfrTxn.TransactionDate,
            TransactionType = securityTxfrTxn.SubType == "SecurityIn" ? TransactionType.Inflow.ToString() : TransactionType.Outflow.ToString(),
            TransactionSubType = securityTxfrTxn.SubType,
            Amount = securityTxfrTxn.Amount,
            Description = $"Legacy upload - {securityTxfrTxn.SubType} of {securityTxfrTxn.Isin} - {securityTxfrTxn.Symbol} - Amount: {securityTxfrTxn.Amount}"
          };

          await _portfolioCapRegRepo.Insert(capitalRegTxn);
          if (capitalRegTxn.TransactionType == "SecurityIn")
          {
            var securityInLedger = new PortfolioLedgerTransactionDto
            {
              ModelPortfolioId = modelPortfolio.Id,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Credit.ToString(),
              TransactionSubType = TransactionSubType.SecurityIn.ToString()
            };
            portfolioLedgerTxns.Add(securityInLedger);

            var securityInDebitLedger = new PortfolioLedgerTransactionDto
            {
              ModelPortfolioId = modelPortfolio.Id,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Debit.ToString(),
              TransactionSubType = TransactionSubType.Buy.ToString()
            };
            portfolioLedgerTxns.Add(securityInDebitLedger);
          }
          else if (capitalRegTxn.TransactionType == "SecurityOut")
          {
            var securityOutLedger = new PortfolioLedgerTransactionDto
            {
              ModelPortfolioId = modelPortfolio.Id,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Debit.ToString(),
              TransactionSubType = TransactionSubType.SecurityOut.ToString()
            };
            portfolioLedgerTxns.Add(securityOutLedger);

            var securityOutCreditLedger = new PortfolioLedgerTransactionDto
            {
              ModelPortfolioId = modelPortfolio.Id,
              Amount = capitalRegTxn.Amount,
              TransactionDate = capitalRegTxn.TransactionDate,
              TransactionType = TransactionType.Credit.ToString(),
              TransactionSubType = TransactionSubType.Sell.ToString()
            };
            portfolioLedgerTxns.Add(securityOutCreditLedger);
          }
        }

        //ledger entries for corporate actions
        foreach (var corpActionTxn in adjustedCorpTxns)
        {
          if (corpActionTxn.SubType == TransactionSubType.Dividend.ToString() || corpActionTxn.SubType == TransactionSubType.Interest.ToString()
            || corpActionTxn.SubType == TransactionSubType.IncomeSurplus.ToString() || corpActionTxn.SubType == TransactionSubType.ReturnOnCapital.ToString()
            || corpActionTxn.SubType == TransactionSubType.BonusPartial.ToString() || corpActionTxn.SubType == TransactionSubType.SplitPartial.ToString()
            || corpActionTxn.SubType == TransactionSubType.MergerPartial.ToString())
          {
            var ledgerTxnType = TransactionType.Credit.ToString();
            if (corpActionTxn.Type == TransactionType.Buy.ToString())
            {
              ledgerTxnType = TransactionType.Debit.ToString();
            }

            var ledgerCorpActionTxn = new PortfolioCashLedger
            {
              PortfolioId = null,
              ModelportfolioId = modelPortfolio.Id,
              IsModelPortfolio = true,
              Amount = corpActionTxn.Amount,
              Description = $"Corporate Action:{corpActionTxn.SubType} - Investment:{corpActionTxn.Symbol}",
              SettlementDate = corpActionTxn.SettlementDate,
              TransactionDate = corpActionTxn.TransactionDate,
              TransactionType = ledgerTxnType,
              TransactionSubType = corpActionTxn.SubType,
              TxnRefId = $"{corpActionTxn.Symbol}-{corpActionTxn.SubType}"
            };

            ledgerTransactions.Add(ledgerCorpActionTxn);
          }
        }
      }


      //ledger entries for transactions that are Originally BUY & SELL. BuyAdjustment included because of Split making the original buy
      //as adjusted buy i.e. SubType: BuyAdjustment
      var transactionsGroupedByDate = portfolioLedgerTxns.Where(txn => txn.TransactionSubType == TransactionSubType.Buy.ToString()
      || txn.TransactionSubType == TransactionSubType.BuyAdjustment.ToString()
      || txn.TransactionSubType == TransactionSubType.Sell.ToString()).GroupBy(txn => txn.TransactionDate);
      foreach (var txnDate in transactionsGroupedByDate)
      {
        var ledgerDate = txnDate.Key;
        var debitAmountTotal = txnDate.Where(txn => txn.TransactionType == TransactionType.Buy.ToString()).Sum(txn => txn.Amount);
        var creditAmountTotal = txnDate.Where(txn => txn.TransactionType == TransactionType.Sell.ToString()).Sum(txn => txn.Amount);

        if (debitAmountTotal > 0)
        {
          var ledgerBuyTxn = new PortfolioCashLedger
          {
            ModelportfolioId = modelPortfolio.Id,
            IsModelPortfolio = true,
            Amount = debitAmountTotal,
            Description = $"Securities bought Amount reflects net",
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = TransactionType.Debit.ToString(),
            TransactionSubType = TransactionSubType.Buy.ToString(),
            TxnRefId = $"{ledgerDate}"
          };

          ledgerTransactions.Add(ledgerBuyTxn);
        }

        if (creditAmountTotal > 0)
        {
          var ledgerSellTxn = new PortfolioCashLedger
          {
            ModelportfolioId = modelPortfolio.Id,
            IsModelPortfolio = true,
            Amount = creditAmountTotal,
            Description = $"Securities sold/dividend Amount reflects net",
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = TransactionType.Credit.ToString(),
            TransactionSubType = TransactionSubType.Sell.ToString(),
            TxnRefId = $"{ledgerDate}"
          };

          ledgerTransactions.Add(ledgerSellTxn);
        }
      }

      //ledger entries for Dividend TDS
      foreach (var txnDate in portfolioLedgerTxns.Where(txn => txn.TransactionSubType == TransactionSubType.Tds.ToString()))
      {
        var ledgerDate = txnDate.TransactionDate;
        var debitAmountTotal = txnDate.Amount;

        if (debitAmountTotal > 0)
        {
          var ledgerBuyTxn = new PortfolioCashLedger
          {
            ModelportfolioId = modelPortfolio.Id,
            IsModelPortfolio = true,
            Amount = debitAmountTotal,
            Description = txnDate.Description,
            SettlementDate = ledgerDate,
            TransactionDate = ledgerDate,
            TransactionType = txnDate.TransactionType,
            TransactionSubType = txnDate.TransactionSubType,
            TxnRefId = txnDate.TransactionRefId
          };

          ledgerTransactions.Add(ledgerBuyTxn);
        }
      }

      await _investmentTxnRepo.Commit();
      await _portfolioCapRegRepo.Commit();

      foreach (var isinIndirectlyAffected in isinsToRefresh)
      {
        var txnsInDb = new List<InvestmentTransaction>();
        var txnsInThisIsin = isinIndirectlyAffected.Value;
        var existingInvestment = await _investmentRepo.GetBySymbolInModelPortfolio(isinIndirectlyAffected.Key, modelPortfolio.Id);

        if (existingInvestment == null)
        {
          var investmentSecurityDetails = await securityMasterService.GetStockDetailsBySymbol(isinIndirectlyAffected.Key);
          existingInvestment = new Investment
          {
            ModelportfolioId = modelPortfolio.Id,
            CurrentPriceDate = DateTime.Today.AddDays(-1),
            Isin = investmentSecurityDetails.Isin,
            Symbol = investmentSecurityDetails.Symbol,
            Name = investmentSecurityDetails.CompName,
            Exchange = txnsInThisIsin.FirstOrDefault().Exchange,
            FirstTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderBy(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            LastTransactionDate = Convert.ToDateTime(txnsInThisIsin.OrderByDescending(txn => Convert.ToDateTime(txn.TransactionDate)).FirstOrDefault().TransactionDate),
            AssetClass = $"{investmentSecurityDetails.DetermineAssetClass()}",
            Sector = investmentSecurityDetails.Ind_Shortname,
            MarketCap = investmentSecurityDetails.Marketcap,
            SecurityType = investmentSecurityDetails.Status == "ETF" ? $"{SecurityType.ETF}" : $"{SecurityType.Stocks}",
            SecuritySubType = $"{SecuritySubType.Listed}"
          };

          txnsInDb.AddRange(isinIndirectlyAffected.Value);

          CalculateCurrentHoldings(txnsInDb);
          CalculateOpenLotQuantities(txnsInDb);

          existingInvestment.CurrentHolding = txnsInDb.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
          existingInvestment.Transactions = txnsInDb;
          await _investmentRepo.Insert(existingInvestment);
        }
        else
        {
          txnsInDb.AddRange(isinIndirectlyAffected.Value);

          if (existingInvestment.Transactions != null)
            txnsInDb.AddRange(existingInvestment.Transactions);

          CalculateCurrentHoldings(txnsInDb);
          CalculateOpenLotQuantities(txnsInDb);

          existingInvestment.CurrentHolding = txnsInDb.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
          existingInvestment.Transactions = txnsInDb;
          await _investmentRepo.Update(existingInvestment);
        }

        await _investmentRepo.Commit();
      }

      var finalInvestmentsInModel = await _investmentRepo.GetInvestmentsInModelPortfolio(modelPortfolio.Id);
      foreach (var investment in finalInvestmentsInModel
        .Where(i =>
          i.SecurityType == $"{SecurityType.Stocks}" || i.SecurityType == $"{SecurityType.ETF}")
        )
      {
        var modelSecurity = new ModelSecurity
        {
          CompName = investment.Name,
          Isin = investment.Isin,
          Exchange = investment.Exchange,
          Symbol = investment.Symbol,
          IsMutualFund = false,
          Industry = investment.Sector,
          ModelId = thisModel.Id,
          Name = investment.Name
        };

        thisModel.SecuritiesInModel.Add(modelSecurity);
      }

      await _strategyModelRepo.Update(thisModel);
      await _strategyModelRepo.Commit();

      return ledgerTransactions;
    }

    public async Task<IEnumerable<DirectEquityTransactionImport>> LoadDataFromFile(string path, ISecurityMasterService securityMasterService
      , ITenantSecurityMasterRepository tenantSecMasterRepo)
    {
      var directEquityTxnData = new List<DirectEquityTransactionImport>();
      ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
      using (var stream = File.OpenRead(path))
      using (var package = new ExcelPackage(stream))
      {
        var worksheet = package.Workbook.Worksheets.FirstOrDefault(sheet => sheet.Name == "ImportData");

        if (worksheet == null)
          throw new InvalidOperationException("Expecting a sheet with name \"ImportData\" not found");

        int colCount = worksheet.Dimension.End.Column;
        int rowCount = worksheet.Dimension.End.Row;

        //if (colCount != 20)
        //  throw new InvalidOperationException("Please check the file. You may have not uploaded the wrong file.");

        //if (string.IsNullOrEmpty(worksheet.Cells[1, 1].Value.ToString()))
        //  throw new InvalidOperationException("Please check the file. You may have not uploaded the wrong file.");

        var isinsInFile = new List<string>();
        for (int row = 1; row <= rowCount; row++)
        {
          if (worksheet.Cells[row, 21].Value == null)
          {
            continue;
          }

          if (worksheet.Cells[row, 21].Value.ToString().Trim().ToLower() == "stocks" || worksheet.Cells[row, 21].Value.ToString().Trim().ToLower() == "etf"
            || worksheet.Cells[row, 21].Value.ToString().Trim().ToLower().Contains("shares"))
            isinsInFile.Add(worksheet.Cells[row, 19].Value == null ? string.Empty : worksheet.Cells[row, 19].Value.ToString().Trim());
        }

        if (isinsInFile.Distinct().ToList().Count() > 0)
        {
          ListOfSecuritiesInThisFile.AddRange(await securityMasterService.GetStockDetailsForMultipleIsins(isinsInFile.Distinct().ToList()));
        }

        var tenantMasterSecurities = await tenantSecMasterRepo.GetAll();
        ListOfSecuritiesInThisFile.AddRange(ConvertToEquityMaster(tenantMasterSecurities.ToList()));

        var isinsNotInMainMaster = new List<string>();
        isinsNotInMainMaster.AddRange(isinsInFile.FindAll(isin => !ListOfSecuritiesInThisFile.Select(security => security.Isin).Contains<string>(isin)).Distinct());

        if (isinsNotInMainMaster.Count() > 0)
          ListOfSecuritiesInThisFile.AddRange(await securityMasterService.GetStockDetailsForMultipleMissingIsins(isinsNotInMainMaster.Distinct().ToList()));

        for (int row = 1; row <= rowCount; row++)
        {
          if (worksheet.Cells[row, 1].Value == null)
          {
            continue;
          }

          if (row == 1)
          {
            continue;
          }

          var isin = worksheet.Cells[row, 19].Value == null ? String.Empty : worksheet.Cells[row, 19].Value.ToString().Trim();
          if (!isinsInFile.Contains(isin))
            continue;

          var exchange = worksheet.Cells[row, 20].Value == null ? "NSE" : worksheet.Cells[row, 20].Value.ToString().Trim().ToUpper() == "BSE" ? "BSE" : "NSE";
          var securityDetails = ListOfSecuritiesInThisFile.FirstOrDefault(s => s.MigrationInputIsin == isin);

          var securityExchange = string.Empty;
          if (securityDetails != null)
            securityExchange = (!string.IsNullOrEmpty(securityDetails.Nse_sublisting) && securityDetails.Nse_sublisting.ToLower() != "null" && !string.IsNullOrEmpty(securityDetails.Symbol))
              ? "NSE" : "BSE";
          var marketRate = worksheet.Cells[row, 9].Value == null ? 0.0 : Math.Abs(Convert.ToDouble(worksheet.Cells[row, 9].Value.ToString().Trim()));
          var transactionDate = worksheet.Cells[row, 6].GetValue<DateTime>().ToShortDateString();

          var txnType = worksheet.Cells[row, 8].Value.ToString().Trim();
          var txnSubType = worksheet.Cells[row, 8].Value.ToString().Trim();

          if(worksheet.Cells[row, 8].Value.ToString().Trim().Contains("Reinvest"))
          {
            txnType = $"{TransactionType.DividendReinvestment}";
            txnSubType = $"{TransactionSubType.DividendReinvestment}";
					}
          else if(worksheet.Cells[row, 8].Value.ToString().Trim().Contains("Buyback"))
					{
						txnType = $"{TransactionType.Sell}";
						txnSubType = $"{TransactionSubType.Sell}";
					}

					var directEquityTxnImportRow = new DirectEquityTransactionImport();
          directEquityTxnImportRow.ClientCode = worksheet.Cells[row, 1].Value == null ? String.Empty : worksheet.Cells[row, 1].Value.ToString().Trim();
          directEquityTxnImportRow.ClientName = worksheet.Cells[row, 2].Value == null ? String.Empty : worksheet.Cells[row, 2].Value.ToString().Trim();
          directEquityTxnImportRow.StrategyCode = worksheet.Cells[row, 3].Value == null ? String.Empty : worksheet.Cells[row, 3].Value.ToString().Trim();
          directEquityTxnImportRow.StrategyName = worksheet.Cells[row, 4].Value == null ? String.Empty : worksheet.Cells[row, 4].Value.ToString().Trim();
          directEquityTxnImportRow.ModelName = worksheet.Cells[row, 5].Value == null ? String.Empty : worksheet.Cells[row, 5].Value.ToString().Trim();
          directEquityTxnImportRow.TransactionDate = transactionDate; //.Value == null ? String.Empty : worksheet.Cells[row, 6].Value.ToString().Trim();
          directEquityTxnImportRow.SettlementDate = worksheet.Cells[row, 7].GetValue<DateTime>().ToShortDateString();//.Value == null ? String.Empty : worksheet.Cells[row, 7].Value.ToString().Trim();
          directEquityTxnImportRow.TransactionType = txnType;
          directEquityTxnImportRow.TransactionSubType = txnSubType;
          directEquityTxnImportRow.MarketRate = marketRate;
          directEquityTxnImportRow.Quantity = worksheet.Cells[row, 10].Value == null ? 0 : Convert.ToDouble(worksheet.Cells[row, 10].Value.ToString().Trim());
          directEquityTxnImportRow.TransactionAmount = worksheet.Cells[row, 11].Value == null ? 0 : Convert.ToDouble(worksheet.Cells[row, 11].Value.ToString().Trim());
          directEquityTxnImportRow.BrokeragePerUnit = worksheet.Cells[row, 12].Value == null ? 0 : Convert.ToDouble(worksheet.Cells[row, 12].Value.ToString().Trim());
          directEquityTxnImportRow.ServiceTax = worksheet.Cells[row, 13].Value == null ? 0 : Convert.ToDouble(worksheet.Cells[row, 13].Value.ToString().Trim());
          directEquityTxnImportRow.SttAmount = worksheet.Cells[row, 14].Value == null ? 0 : Convert.ToDouble(worksheet.Cells[row, 14].Value.ToString().Trim());
          directEquityTxnImportRow.TurnTax = worksheet.Cells[row, 15].Value == null ? 0 : Convert.ToDouble(worksheet.Cells[row, 15].Value.ToString().Trim());
          directEquityTxnImportRow.OtherTax = worksheet.Cells[row, 16].Value == null ? 0 : Convert.ToDouble(worksheet.Cells[row, 16].Value.ToString().Trim());
          directEquityTxnImportRow.Symbol = securityDetails == null ? string.Empty : securityExchange == "BSE" ? securityDetails.Scripcode : securityDetails.Symbol;
          directEquityTxnImportRow.SecurityName = worksheet.Cells[row, 18].Value == null ? string.Empty : worksheet.Cells[row, 18].Value.ToString().Trim();
          directEquityTxnImportRow.Isin = securityDetails == null ? isin : securityDetails.Isin;
          directEquityTxnImportRow.Exchange = exchange;
          directEquityTxnImportRow.MFFolio = worksheet.Cells[row, 22].Value == null ? string.Empty : worksheet.Cells[row, 22].Value.ToString().Trim();
          directEquityTxnImportRow.APClientCode = worksheet.Cells[row, 23].Value == null ? string.Empty : worksheet.Cells[row, 23].Value.ToString().Trim();
          directEquityTxnImportRow.CGTDate = worksheet.Cells[row, 24].Value == null ? transactionDate : worksheet.Cells[row, 24].GetValue<DateTime>().ToShortDateString();
          directEquityTxnImportRow.AcquisitionRate = worksheet.Cells[row, 25].Value == null ? marketRate : Math.Abs(Convert.ToDouble(worksheet.Cells[row, 25].Value));

          directEquityTxnData.Add(directEquityTxnImportRow);
        }
      }

      return directEquityTxnData;
    }

    public async Task RunCorporateActionsForSpecificIsin(string isin, string portfolioId, string clientId, string commonDataConnStr, ISecurityMasterService securityMasterService)
    {
      var isinsIndirectlyAffectedByCorpActions = new List<KeyValuePair<string, List<InvestmentTransaction>>>();
      var corporateActionsService = new CorporateActionsService(_equityHistoryRepo, commonDataConnStr, _mapper, securityMasterService);
      var investment = await _investmentRepo.GetByIsinInPortfolio(isin, portfolioId);
      var existingTransactions = await _investmentTxnRepo.GetTransactionsInInvestment(investment.Id);
      var corpActionSymbols = new List<string>();
      corpActionSymbols.Add(investment.Symbol);
      var allCorpActionForSymbol = await corporateActionsService.GetCorporateActionsForSymbols(corpActionSymbols);

      var adjustedCorpTxns =
          corporateActionsService.BuildCorpActionTxns(clientId, portfolioId,
            investment.Id, investment.Symbol, allCorpActionForSymbol, existingTransactions, false, out isinsIndirectlyAffectedByCorpActions);

      //need to rerun holding balance and open lot quantity calculations
      var modifiedTransactions = existingTransactions.ToList();
      modifiedTransactions.AddRange(adjustedCorpTxns);
      CalculateCurrentHoldings(modifiedTransactions.OrderBy(txn => txn.TransactionDate).ToList());
      CalculateOpenLotQuantities(modifiedTransactions.OrderBy(txn => txn.TransactionDate).ToList());

      investment.CurrentHolding = modifiedTransactions.OrderByDescending(txn => txn.TransactionDate).FirstOrDefault().CurrentHolding;
      await _investmentRepo.Update(investment);
      await _investmentRepo.Commit();

      //insert transactions including corp action txns
      foreach (var txn in modifiedTransactions)
      {
        if (!string.IsNullOrEmpty(txn.Id))
        {
          await _investmentTxnRepo.Update(txn);
        }
        else
        {
          await _investmentTxnRepo.Insert(txn);
        }
      }

      //ledger entries for corporate actions
      foreach (var corpActionTxn in adjustedCorpTxns)
      {
        if (corpActionTxn.SubType == TransactionSubType.Dividend.ToString() || corpActionTxn.SubType == TransactionSubType.Interest.ToString()
          || corpActionTxn.SubType == TransactionSubType.IncomeSurplus.ToString() || corpActionTxn.SubType == TransactionSubType.ReturnOnCapital.ToString()
          || corpActionTxn.SubType == TransactionSubType.BonusPartial.ToString() || corpActionTxn.SubType == TransactionSubType.SplitPartial.ToString())
        {
          var ledgerTxn = new PortfolioCashLedger
          {
            PortfolioId = portfolioId,
            ModelportfolioId = null,
            IsModelPortfolio = false,
            Amount = corpActionTxn.Amount,
            Description = $"Corporate Action:{corpActionTxn.SubType} - Investment:{corpActionTxn.Symbol}",
            SettlementDate = corpActionTxn.TransactionDate,
            TransactionDate = corpActionTxn.TransactionDate,
            TransactionType = TransactionType.Credit.ToString(),
            TransactionSubType = corpActionTxn.SubType,
            TxnRefId = investment.Id,
            CreatedDate = DateTime.Today,
            LastUpdatedDate = DateTime.Today
          };

          await _portfolioLedgerRepository.Insert(ledgerTxn);
        }
      }

      await _investmentTxnRepo.Commit();
      await _portfolioLedgerRepository.Commit();
    }

    public async Task<IEnumerable<PortfolioCashLedger>> BuildLedgerEntriesForExistingTransactions(string portfolioId)
    {

      var ledgerTransactions = new List<PortfolioCashLedger>();
      //adding transactions on the investment
      var txnsToInsert = await _investmentTxnRepo.GetTransactionsInPortfolio(portfolioId);
      foreach (var txnInIsin in txnsToInsert)
      {
        var ledgerTxn = new PortfolioCashLedger
        {
          PortfolioId = portfolioId,
          ModelportfolioId = null,
          IsModelPortfolio = false,
          Amount = txnInIsin.Amount,
          Description = $"Securities {txnInIsin.Type}-{txnInIsin.SubType}",
          SettlementDate = txnInIsin.TransactionDate,
          TransactionDate = txnInIsin.TransactionDate,
          TransactionType = txnInIsin.Type == "Buy" ? TransactionType.Debit.ToString() : TransactionType.Credit.ToString(),
          TransactionSubType = txnInIsin.SubType,
          TxnRefId = $"{txnInIsin.Id}"
        };

        ledgerTransactions.Add(ledgerTxn);
      }

      return ledgerTransactions;
    }
  }
}
