{"appService.defaultWebAppToDeploy": "/subscriptions/8ad78f39-974a-4e0c-b123-010c4f3e21fb/resourceGroups/alphap-rg/providers/Microsoft.Web/sites/qa-alphap", "appService.deploySubpath": "Actlogica.AlphaPortfolios/Actlogica.AlphaPortfolios.Api/bin/Publish", "azureFunctions.projectSubpath": "Actlogica.AlphaPortfolios/AlphaP.ReportGenerator", "azureFunctions.deploySubpath": "Actlogica.AlphaPortfolios/AlphaP.ReportGenerator/bin/Release/net8.0/publish", "azureFunctions.projectLanguage": "C#", "azureFunctions.projectRuntime": "~4", "debug.internalConsoleOptions": "neverOpen", "azureFunctions.preDeployTask": "publish (functions)"}